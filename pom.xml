<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay.sofa</groupId>
		<artifactId>sofaboot-alipay-dependencies</artifactId>
		<version>4.3.0</version>
	</parent>
	<groupId>com.alipay</groupId>
	<artifactId>findataquality-parent</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>findataquality</name>
	<description>Demo project for SOFA Boot</description>

	<modules>
		<module>app/bootstrap</module>
		<module>app/service</module>
		<module>app/dal</module>
		<module>app/model</module>
		<module>app/utils</module>
		<module>app/facade</module>
	</modules>

	<properties>
		<java.version>17</java.version>
		<facade.version>0.0.2.20250526</facade.version>
		<tracer.alipay.version>4.0.3</tracer.alipay.version>
	</properties>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-bootstrap</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-service</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-dal</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-model</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-utils</artifactId>
				<version>0.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.alipay</groupId>
				<artifactId>findataquality-facade</artifactId>
				<version>${facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-mcp-server-webmvc-sofa-boot-starter</artifactId>
				<version>1.7.2</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-mcp-client-sofa-boot-starter</artifactId>
				<version>1.7.2</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-codefuse-sofa-boot-starter</artifactId>
				<version>1.7.2</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-antllm-sofa-boot-starter</artifactId>
				<version>1.7.2</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-modelops-sofa-boot-starter</artifactId>
				<version>1.7.2</version>
			</dependency>
			<dependency>
				<groupId>ch.qos.logback</groupId>
				<artifactId>logback-classic</artifactId>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-jdk14</artifactId>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.jboss.slf4j</groupId>
				<artifactId>slf4j-jboss-logging</artifactId>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-jcl</artifactId>
				<scope>provided</scope>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-to-slf4j</artifactId>
				<scope>provided</scope>
			</dependency>
			<!--slf4j日志打印-->
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-reload4j</artifactId>
				<version>2.0.12</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.zdal</groupId>
				<artifactId>zdal-orm-annotation</artifactId>
				<version>1.0.0</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.75_noneautotype</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa.common</groupId>
				<artifactId>sofa-common-tools</artifactId>
				<version>2.1.1</version>
			</dependency>
			<dependency>
				<groupId>alipay-common-lang</groupId>
				<artifactId>alipay-common-lang</artifactId>
				<version>2.2.1</version>
			</dependency>
			<!--groovy脚本引擎-->
			<dependency>
				<groupId>org.codehaus.groovy</groupId>
				<artifactId>groovy</artifactId>
				<version>3.0.11</version>
			</dependency>
			<!--单元测试依赖-->
			<dependency>
				<groupId>junit</groupId>
				<artifactId>junit</artifactId>
				<version>4.13.2</version>
				<scope>test</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>3.1.5</version>
			</dependency>
			<!-- Apache POI for Excel parsing -->
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>5.2.3</version>
			</dependency>
			<!-- Commons IO (必需) -->
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>2.16.1</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>sofa-ai-bom</artifactId>
				<version>1.7.2</version>
				<scope>import</scope>
				<type>pom</type>
			</dependency>
			<dependency>
				<groupId>com.alipay.sofa</groupId>
				<artifactId>tracer-core</artifactId>
				<version>4.0.2</version>
			</dependency>
			<dependency>
				<groupId>com.alipay.common</groupId>
				<artifactId>tracer</artifactId>
				<version>4.0.3</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.2</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>16</source>
					<target>16</target>
					<encoding>UTF-8</encoding>
					<compilerArgs>
						<arg>-parameters</arg>
					</compilerArgs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.22.2</version>
				<configuration>
					<threadCount>1</threadCount>
					<includes>
						<include>**/*Tests.java</include>
						<include>**/*Test.java</include>
					</includes>
					<excludes>
						<exclude>**/Abstract*.java</exclude>
					</excludes>
					<argLine>--add-opens=java.base/*=ALL-UNNAMED</argLine>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.apache.maven.surefire</groupId>
						<artifactId>surefire-junit-platform</artifactId>
						<version>2.22.2</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.0.2</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-eclipse-plugin</artifactId>
				<version>2.6</version>
				<configuration>
					<downloadSources>true</downloadSources>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.alipay.sofa.plugins</groupId>
				<artifactId>gitops-maven-plugin</artifactId>
				<version>1.0-SNAPSHOT</version>
			</plugin>
		</plugins>
	</build>

	<profiles>
        <!--蚂蚁主站开发环境maven配置-->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>alipay_dev</id>
                    <url>https://maven-dev.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
		         <pluginRepository>
                    <id>alipay_dev</id>
                    <url>https://maven-dev.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
		<!--蚂蚁主站生产环境maven配置-->
        <profile>
            <id>prod</id>
            <repositories>
                <repository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>alipay_test</id>
                    <url>https://maven-test.alipay.com/artifactory/content/groups/public/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

</project>
