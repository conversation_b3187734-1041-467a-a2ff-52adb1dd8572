package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.AccLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.CifCustAssetTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.FinmemberTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.ObcifTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinfundprotocolTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.AftransTradeCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransAccountTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAccountTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreTransInTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName YebTransInQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 10:32
 * @Version V1.0
 **/
public class YebTransInAndSwitchQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public YebTransInAndSwitchQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                //捞取转入表信息
                //YebcoreAccountPreTask.class
                YebcoreTransInTask.class,
                FinancingcoreCommonTask.class,
                MftransLogCommonTask.class,
                //OrapayTask.class,
                ObcloudpayTask.class,
                AccLogCommonTask.class,
                InstpayCommonTask.class,

                //捞取账户信息
                YebcoreAccountTask.class,
                CifCustAssetTask.class,
                FinmemberTask.class,
                ObcifTask.class,
                FinfundprotocolTask.class,
                MftransAccountTask.class,
                ProdtransTask.class,
                AftransTradeCommonTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,1,2,2,2,2,2,2,2,2,2,3,3,3};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.YEB_TRANS_IN_AND_SWITCH;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
    }
}
