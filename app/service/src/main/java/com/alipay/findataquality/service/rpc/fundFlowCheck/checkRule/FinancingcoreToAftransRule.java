package com.alipay.findataquality.service.rpc.fundFlowCheck.checkRule;

/**
 * @ClassName FinancingcoreToAftransRule
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/11 16:44
 * @Version V1.0
 **/
public class FinancingcoreToAftransRule extends AbstractCheckRule{
    @Override
    public String leftTable() {
        return null;
    }

    @Override
    public String rightTable() {
        return null;
    }

    @Override
    public Object[][] leftCheck() {
        return new Object[0][];
    }

    @Override
    public Object[][] rightCheck() {
        return new Object[0][];
    }

    @Override
    public boolean rowCntPreCheck(int leftCnt, int rightCnt) {
        return false;
    }

    @Override
    public Object[][] crossCheckEach() {
        return new Object[0][];
    }

    @Override
    public Object[][] crossCheckAll() {
        return new Object[0][];
    }
}
