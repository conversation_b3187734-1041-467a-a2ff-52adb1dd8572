package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import java.util.List;

public class UIMockQueryDetailResult {
    String id;

    String name;

    String description;

    List<String> type;

    String startPage;
    List<MockConfig> mockConfig;
    String sprintId;
    String projectId;

    String thumbnail;
    String url;

    String originUrl;
    String qs;
    String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getType() {
        return type;
    }

    public void setType(List<String> type) {
        this.type = type;
    }

    public String getStartPage() {
        return startPage;
    }

    public void setStartPage(String startPage) {
        this.startPage = startPage;
    }

    public List<MockConfig> getMockConfig() {
        return mockConfig;
    }

    public void setMockConfig(List<MockConfig> mockConfig) {
        this.mockConfig = mockConfig;
    }

    public String getSprintId() {
        return sprintId;
    }

    public void setSprintId(String sprintId) {
        this.sprintId = sprintId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getQs() {
        return qs;
    }

    public void setQs(String qs) {
        this.qs = qs;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getOriginUrl() {
        return originUrl;
    }

    public void setOriginUrl(String originUrl) {
        this.originUrl = originUrl;
    }

    @Override
    public String toString() {
        return "{"
                + "\"id\":\""
                + id + '\"'
                + ",\"name\":\""
                + name + '\"'
                + ",\"description\":\""
                + description + '\"'
                + ",\"type\":\""
                + type + '\"'
                + ",\"startPage\":\""
                + startPage + '\"'
                + ",\"mockConfig\":"
                + mockConfig
                + ",\"sprintId\":\""
                + sprintId + '\"'
                + ",\"projectId\":\""
                + projectId + '\"'
                + ",\"thumbnail\":\""
                + thumbnail + '\"'
                + ",\"url\":\""
                + url + '\"'
                + ",\"originUrl\":\""
                + originUrl + '\"'
                + ",\"qs\":\""
                + qs + '\"'
                + ",\"status\":\""
                + status + '\"'
                + "}";

    }
}
