package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import java.util.List;

public class UIMockAddRequest {
    String projectId;
    String sprintId;
    String name;
    String status;
    List<String> type;
    String startPage;
    String qs;
    List<MockConfig> mockConfig;
    Object schemaConfig;
    Object envConfig;
    String description;

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getSprintId() {
        return sprintId;
    }

    public void setSprintId(String sprintId) {
        this.sprintId = sprintId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<String> getType() {
        return type;
    }

    public void setType(List<String> type) {
        this.type = type;
    }

    public String getStartPage() {
        return startPage;
    }

    public void setStartPage(String startPage) {
        this.startPage = startPage;
    }

    public String getQs() {
        return qs;
    }

    public void setQs(String qs) {
        this.qs = qs;
    }

    public List<MockConfig> getMockConfig() {
        return mockConfig;
    }

    public void setMockConfig(List<MockConfig> mockConfig) {
        this.mockConfig = mockConfig;
    }

    public Object getSchemaConfig() {
        return schemaConfig;
    }

    public void setSchemaConfig(Object schemaConfig) {
        this.schemaConfig = schemaConfig;
    }

    public Object getEnvConfig() {
        return envConfig;
    }

    public void setEnvConfig(Object envConfig) {
        this.envConfig = envConfig;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "{"
                + "\"projectId\":\""
                + projectId + '\"'
                + ",\"sprintId\":\""
                + sprintId + '\"'
                + ",\"name\":\""
                + name + '\"'
                + ",\"status\":\""
                + status + '\"'
                + ",\"type\":"
                + type
                + ",\"startPage\":\""
                + startPage + '\"'
                + ",\"qs\":\""
                + qs + '\"'
                + ",\"mockConfig\":"
                + mockConfig
                + ",\"schemaConfig\":"
                + schemaConfig
                + ",\"envConfig\":"
                + envConfig
                + ",\"description\":\""
                + description + '\"'
                + "}";

    }
}
