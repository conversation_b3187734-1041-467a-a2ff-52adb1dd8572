package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

/**
 * 字符串处理工具类
 */
public class StringUtils extends org.apache.commons.lang3.StringUtils {

    public static String toFixedLength(String str, int length, char fillChar) {
        return toFixedLength(str, length, -1, fillChar);
    }

    /**
     * get fixed length string
     * @param str
     * @param length
     * @param direction <0: from front; >0: from behind;
     * @param fillChar fill the char in the lack of bit
     * @return
     */
    public static String toFixedLength(String str, int length, int direction, char fillChar) {
        if (str.length() == length) {
            return str;
        }
        else if (str.length() > length) {
            if (direction < 0) {
                return str.substring(0, length);
            } else {
                return str.substring(str.length() - length);
            }
        }
        else {
            String fillStr = "";
            for (int i = 0; i < length - str.length(); i++) {
                fillStr += fillChar;
            }
            if (direction < 0) {
                return fillStr + str;
            } else {
                return str + fillStr;
            }
        }
    }
}
