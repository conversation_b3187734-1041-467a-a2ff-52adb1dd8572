package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName CustAssetTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 18:01
 * @Version V1.0
 **/
public class CifCustAssetTask extends AbstractQueryTask {
    public CifCustAssetTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.CUSTASSET;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from `ca_usage_agreement_${db_flag}` where principal_id = '${user_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[1];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[1];
    }
}
