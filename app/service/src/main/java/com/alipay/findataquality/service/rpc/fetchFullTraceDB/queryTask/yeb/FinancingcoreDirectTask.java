package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName FinancingcoreTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 11:06
 * @Version V1.0
 **/
public class FinancingcoreDirectTask extends AbstractQueryTask {
    public FinancingcoreDirectTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCINGCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from yeb_asset_increase_order_${db_flag} where asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_decrease_order_${db_flag} where asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_freeze_order_${db_flag} where asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_unfreeze_order_${db_flag} where asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_instruction_${db_flag} where instruction_id like '${date}%' and asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_batch_mftrans_order_${db_flag} where asset_order_id in (${asset_order_id})"
        };
    }

    @Override
    public String[] outcomeValue() {
        String keys = "payment_id,biz_no->aftrans_order_list,asset_account_no->common_mf_account_no_list,ext_info.fcTraceId->trace_id";
        return new String[]{keys,keys,keys,keys,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null};
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        //对于使用该类作为入口的调用，不含单引号，对于内部编排传递可能带单引号，故此处进行兼容
        context.updateValueWithWrap("asset_order_id", "'");
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context){
//        String assetAccountNo = tableData.getFirstValue("asset_account_no");
//        context.putGlobal("mf_account_no",assetAccountNo);
        String userId = tableData.getFirstValue("user_id");
        context.putGlobal("user_id",userId);
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        //将结果变量asset_account_no转换为mf_account_no传入下游
        //context.copyValue("biz_no","order_no");
        context.copyValue("aftrans_order_list","mftrans_combine_order_list");
    }
}
