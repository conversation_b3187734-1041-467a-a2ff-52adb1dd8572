package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.accTransData.AcctransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;

/**
 * @ClassName YebAssetData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 16:57
 * @Version V1.0
 **/
public class YeFcCheck extends AbstractCheckScene {

    public YeFcCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return null;
    }

    /**
     * 规则检查
     * @return
     */
    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public String[] checkFcAndAftrans(FinancingcoreData fc, AftransTaskData aftrans, AcctransData acc) {
        putVar("testa","fc.real_amount");
        return new String[]{
                "${fc.status}->{S}",
                "${fc.status}={I,S}"
        };
    }

    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public RuleCheckResult checkAll(FinancingcoreData fc){
        fc.findData("inst_id=='SHUMIJJ'");
        //autoCheckEqual(fc,new String[]{"123"});
        return null;
    }

    /**
     * 测试样例
     * @param fc
     * @param aftrans
     * @param acc
     * @return
     */
    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public RuleCheckResult checkFcAndAftrans_test(FinancingcoreData fc, AftransTaskData aftrans, AcctransData acc) {
        putVar("testa","fc.real_amount");
        //String a= fc.INCREASE;
        LOGGER.info("fc={},af={},acc={}",fc,aftrans,acc);
        return null;
    }

    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public String[] checkFcAndAftrans2(FinancingcoreData fc) {
        LOGGER.info("hello");
        putVar("a","b2");
        return null;
    }

    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public String[] checkFcAndAftrans3(FinancingcoreData fc) {
        LOGGER.info("hello");
        putVar("a","b");
        return null;
    }
}
