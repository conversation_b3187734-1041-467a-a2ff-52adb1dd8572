package com.alipay.findataquality.service.rpc.fundFlowCheck.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.FundFlowCheckService;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.BusinessErrorEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowAnalyzeRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowCheckRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowDetectRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowAnalyzeResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowDetectResult;
import com.alipay.findataquality.service.dto.FullTraceValidateDataFetchRecordDTO;
import com.alipay.findataquality.service.repository.FullTraceValidateDataFetchRecordRepository;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelMainGraphCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelNodeCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelSubGraphCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb.*;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph.FundFlowAnalyze;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph.FundFlowTransGraph;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene.FundFlowSceneRecognize;
import com.alipay.sofa.common.thread.SofaThreadPoolExecutor;
import com.alipay.sofa.common.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * @ClassName FullTraceDataCheckServiceImpl
 * @Description 资金模型分析+检查实现
 * <AUTHOR>
 * @Date 2024/11/8 16:07
 * @Version V1.0
 **/
@Service
public class FundFlowCheckServiceImpl implements FundFlowCheckService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FundFlowCheckService.class);

    /**
     * 数据获取记录仓储层
     */
    @Autowired
    private FullTraceValidateDataFetchRecordRepository fullTraceValidateDataFetchRecordRepository;

    @Override
    public FundFlowAnalyzeResult analyzeFundFlowGraph(FundFlowAnalyzeRequest request) {
        FundFlowAnalyzeResult result = new AbstractExecuteTemplate<FundFlowAnalyzeResult, FundFlowAnalyzeResult>() {
        }.doExecute("FundFlowCheckService-AnalyzeFundFlowGraph", new AbstractExecuteTemplate.ExecuteInvoke<FundFlowAnalyzeResult, FundFlowAnalyzeResult>() {
            @Override
            public void checkParam() {
                String dataJson = request.getDataJson();
                String shareCode = request.getShareCode();
                if(StringUtil.isBlank(dataJson)&&StringUtil.isBlank(shareCode)){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson和shareCode不能同时为空");
                }
                if(StringUtil.isNotBlank(dataJson)&&!JSON.isValid(request.getDataJson())){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson格式不正确");
                }
            }

            @Override
            public void assembleResult(FundFlowAnalyzeResult result, FundFlowAnalyzeResult model) {
                if(model!=null){
                    result.setTransactionGroupList(model.getTransactionGroupList());
                    result.setScenePrediction(model.getScenePrediction());
//                    result.setTransactionComposedSceneList(model.getTransactionComposedSceneList());
                    result.setSuccess(true);
                }else{
                    result.setSuccess(false);
                }
            }

            @Override
            public FundFlowAnalyzeResult getResultInstance() {
                return new FundFlowAnalyzeResult();
            }

            @Override
            public FundFlowAnalyzeResult execute() {
                FundFlowAnalyzeResult result = new FundFlowAnalyzeResult();
                //资金流分析
                FundFlowAnalyze analyze = new FundFlowAnalyze();

                String fullDataContent = null;
                //当shareCode不为空时，从数据库中获取数据
                if(StringUtil.isNotBlank(request.getShareCode())){
                    FullTraceValidateDataFetchRecordDTO dto = fullTraceValidateDataFetchRecordRepository.queryByShareCode(request.getShareCode());
                    if(dto!=null){
                        fullDataContent = dto.getContent();
                    }else{
                        throw new BusinessException(BusinessErrorEnum.SYSTEM_ERROR,"根据shareCode={"+request.getShareCode()+"}未查询到数据");
                    }
                }else{
                    fullDataContent = request.getDataJson();
                }

                List<TransactionGroup> groupList = analyze.analyzeFundFlowGroup(fullDataContent);
                result.setTransactionGroupList(groupList);

                if(request.isRecognizeScene()){
                    //场景识别
                    FundFlowSceneRecognize sceneRecognize = new FundFlowSceneRecognize();
                    List<TransactionStageScene> transactionStageSceneList =  sceneRecognize.recognizeScene(groupList,request.getDataJson());
                    if (transactionStageSceneList == null || transactionStageSceneList.size() == 0) {
                        LOGGER.error("场景匹配为空，需要先定义场景");
                    }else{
                        //基于规则，合并交易场景组合，得到组合场景结论集合；
                        List<TransactionComposedScene> transactionComposedScenes = sceneRecognize.consolidateStageScenes(transactionStageSceneList);
                        result.setTransactionComposedSceneList(transactionComposedScenes);
                        //一句话推断
                        String scenePrediction = sceneRecognize.generateTransactionSceneSummary(transactionComposedScenes);
                        result.setScenePrediction(scenePrediction);
                    }
                }
                return result;
            }
        });
        return result;
    }

    @Override
    public FundFlowDetectResult detectFundFlow(FundFlowDetectRequest fundFlowDetectRequest, FundFlowAnalyzeRequest fundFlowAnalyzeRequest) {
        FundFlowDetectResult result = new AbstractExecuteTemplate<FundFlowDetectResult, FundFlowDetectResult>() {
        }.doExecute("FundFlowCheckService-detectFundFlow", new AbstractExecuteTemplate.ExecuteInvoke<FundFlowDetectResult, FundFlowDetectResult>() {
            @Override
            public void checkParam() {
                if( fundFlowDetectRequest.getTransactionGroupList()!=null && fundFlowDetectRequest.getTransactionGroupList().size()>0){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"transactionList不能为空");
                }
                if(StringUtil.isBlank(fundFlowAnalyzeRequest.getDataJson())){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson不能为空");
                }
            }

            @Override
            public void assembleResult(FundFlowDetectResult result, FundFlowDetectResult model) {
                if(model!=null){
                    //todo：组装结果
                    result.setSuccess(true);
                }else{
                    result.setSuccess(false);
                }
            }

            @Override
            public FundFlowDetectResult getResultInstance() {
                return new FundFlowDetectResult();
            }

            @Override
            public FundFlowDetectResult execute() {
                FundFlowDetectResult result = new FundFlowDetectResult();
                /************场景识别，此处含规则细致识别TransactionStageScene******************/
                FundFlowSceneRecognize sceneRecognize = new FundFlowSceneRecognize();
                List<TransactionStageScene> transactionStageSceneList =  sceneRecognize.recognizeScene(fundFlowDetectRequest.getTransactionGroupList(),fundFlowAnalyzeRequest.getDataJson());
                //基于规则，合并交易场景组合，得到组合场景结论集合；
                sceneRecognize.consolidateStageScenes(transactionStageSceneList);
                //todo：一句话推断

                /************数据检查，此处含规则细致识别TransactionStageScene******************/
                //todo：匹配需要导航到的规则链

                //todo：计算每个资金对象的出入金


                //todo：规则匹配检查

                return result;
            }
        });
        return result;
    }

    @Override
    public FundFlowCheckResult checkFundFlow(FundFlowCheckRequest request) {
        FundFlowCheckResult result = new AbstractExecuteTemplate<FundFlowCheckResult, FundFlowCheckResult>() {
        }.doExecute("FundFlowCheckService-checkFundFlow\n", new AbstractExecuteTemplate.ExecuteInvoke<FundFlowCheckResult, FundFlowCheckResult>() {
            @Override
            public void checkParam() {
                String dataJson = request.getDataJson();
                String shareCode = request.getShareCode();
                if(StringUtil.isBlank(dataJson)&&StringUtil.isBlank(shareCode)){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson和shareCode不能同时为空");
                }
                if(StringUtil.isNotBlank(dataJson)&&!JSON.isValid(request.getDataJson())){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson格式不正确");
                }
            }

            @Override
            public void assembleResult(FundFlowCheckResult result, FundFlowCheckResult model) {
                if(model!=null){
                    result.setResultCode(model.getResultCode());
                    result.setResultDesc(model.getResultDesc());
                    result.setCheckResultList(model.getCheckResultList());
//                    result.setModelCheckResultList(model.getModelCheckResultList());
                    result.setExtInfo(model.getExtInfo());
                    result.setSuccess(true);
                }else{
                    result.setSuccess(false);
                }
            }

            private Class[] getCommonCheckScene(){
                return new Class[]{
                        YebFcCommonCheck.class,
                        YebFcTimeCommonCheck.class,
                        YebFcSubCardCommonCheck.class,
                        YebCkkCheck.class
                };
            }

            private Class[] getStageCheckScene(List<TransactionStageScene> transactionStageSceneList){
                if (CollectionUtils.isEmpty(transactionStageSceneList)){
                    return new Class[]{};
                }
                //场景匹配初始化check class
                Class[] checkSceneClass = transactionStageSceneList.stream()
                        .map(TransactionStageScene::getFundFlowStageEnum)
                        .map(scene -> {
                            switch (scene) {
//                                case BANK_TO_YEB:
//                                    return BankToYebCheck.class;
//                                case COUPON_TO_YEB:
//                                    return CouponToYebCheck.class;
//                                case YEB_CKK_TO_YEB:
//                                    return YebCkkCheck.class;
//                                case YEB_TO_BANK:
//                                    return YebToBankCheck.class;
//                                case YEB_TO_COUPON:
//                                    return YebToCouponCheck.class;
//                                case YEB_TO_FUND_DUMMY:
//                                    return YebToFundDummyCheck.class;
//                                case YEB_TO_FUND_DUMMY_HQ:
//                                    return YebToFundDummyHqCheck.class;
//                                case YEB_YUE_TO_FUND_DUMMY_HQ:
//                                    return YebYueToFundDummyHqCheck.class;
//                                case FUND_DUMMY_TO_YEB_CANCEL:
//                                    return FundDummyToYebCancelCheck.class;
//                                case FUND_DUMMY_TO_YEB_TOTALREFUND_03T1:
//                                    return FundDummyToYebPartRefund03T1Check.class;
//                                case FUND_DUMMY_TO_YEB_TOTALREFUND_03T2:
//                                    return FundDummyToYebPartRefund03T2Check.class;
//                                case FUND_DUMMY_TO_YEB_PARTREFUND_03T1:
//                                    return FundDummyToYebPartRefund03T1Check.class;
//                                case FUND_DUMMY_TO_YEB_PARTREFUND_03T2:
//                                    return FundDummyToYebPartRefund03T2Check.class;
                                default:
                                    return null;  // 处理未匹配的情况
                            }
                        })
                        .filter(clazz -> clazz != null) // 过滤掉null值
                        .distinct() // 保证类唯一
                        .toArray(Class<?>[]::new); // 转换成Class数组
                LOGGER.info("此笔交易匹配到的Check执行器：{}",JSON.toJSONString(checkSceneClass));
                return checkSceneClass;
            }

            private FundFlowCheckResult doCheck(Class checkClass,TableDataContext context){
                AbstractCheckScene checkScene = null;
                try {
                    checkScene = (AbstractCheckScene)checkClass.getConstructor(TableDataContext.class).newInstance(context);
                } catch (Exception e) {
                    //e.printStackTrace();
                    LOGGER.error("校验场景{} doCheck生成校验实例异常",checkClass);
                }
                checkScene.setDataContext(context);
                return checkScene.executeTask();
            }

            @Override
            public FundFlowCheckResult getResultInstance() {
                return new FundFlowCheckResult();
            }

            @Override
            public FundFlowCheckResult execute() {
                TableDataContext context = new TableDataContext();
                FundFlowTransGraph graph = new FundFlowTransGraph();

                String fullDataContent = null;
                //当shareCode不为空时，从数据库中获取数据
                if(StringUtil.isNotBlank(request.getShareCode())){
                    FullTraceValidateDataFetchRecordDTO dto = fullTraceValidateDataFetchRecordRepository.queryByShareCode(request.getShareCode());
                    if(dto!=null){
                        fullDataContent = dto.getContent();
                    }else{
                        throw new BusinessException(BusinessErrorEnum.SYSTEM_ERROR,"根据shareCode={"+request.getShareCode()+"}未查询到数据");
                    }
                }else{
                    fullDataContent = request.getDataJson();
                }

                //1.获取资金流图
                FundFlowAnalyze analyze = new FundFlowAnalyze();
                List<TransactionGroup> groupList = analyze.analyzeFundFlowGroup(fullDataContent);
                //初始化数据上下文.
                context.initFromContent(fullDataContent,graph.getFundFlowClasses(),groupList);

                long start = System.currentTimeMillis();
                ExecutorService service = Executors.newFixedThreadPool(10);
//                SofaThreadPoolExecutor service = new SofaThreadPoolExecutor(10, 10, 60,
//                        TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));
                long end = System.currentTimeMillis();
                LOGGER.info("线程池创建完成，共耗时{}s",(end-start)/1000.0);
                context.setExecutorService(service);

                //2.资金规则检测：场景检测
                List<TransactionStageScene> transactionStageSceneList = request.getTransactionStageScenes();
                Class[] stageClasses = new Class[] {};
                if(!CollectionUtils.isEmpty(transactionStageSceneList)){
                    //场景识别
                    FundFlowSceneRecognize sceneRecognize = new FundFlowSceneRecognize();
                    transactionStageSceneList =  sceneRecognize.recognizeScene(groupList,fullDataContent);
                    LOGGER.info("此笔交易包含的阶段场景：{}",JSON.toJSONString(transactionStageSceneList));
                    stageClasses = this.getStageCheckScene(transactionStageSceneList); //资金规则检测-场景匹配规则
                    LOGGER.info("根据资金规则-场景校验场景{}个，校验列表为{}",stageClasses.length,stageClasses);
                }
                Class[] commonClasses = this.getCommonCheckScene(); //资金规则检测-场景通用规则
                LOGGER.info("获取到资金规则-通用校验场景{}个，校验列表为{}",commonClasses.length,commonClasses);
                Class[] allClasses = Stream.concat(Arrays.stream(stageClasses), Arrays.stream(commonClasses))
                        .distinct()
                        .toArray(Class[]::new);
                LOGGER.info("资金规则校验（阶段场景及通用校验场景合并后）汇总场景{}个，校验列表为{}",allClasses.length,allClasses);

                FundFlowCheckResult result = new FundFlowCheckResult();
                for (Class classType: allClasses) {
                    result.merge(doCheck(classType,context));
                }
                service.shutdown();
                result.setSuccess(true);
                return result;
            }
        });
        return result;
    }

    @Override
    public FundFlowCheckResult checkFundFlowModel(FundFlowCheckRequest request) {
        FundFlowCheckResult result = new AbstractExecuteTemplate<FundFlowCheckResult, FundFlowCheckResult>() {
        }.doExecute("FundFlowCheckService-checkFundFlowModel\n", new AbstractExecuteTemplate.ExecuteInvoke<FundFlowCheckResult, FundFlowCheckResult>() {
            @Override
            public void checkParam() {
                String dataJson = request.getDataJson();
                String shareCode = request.getShareCode();
                if(StringUtil.isBlank(dataJson)&&StringUtil.isBlank(shareCode)){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson和shareCode不能同时为空");
                }
                if(StringUtil.isNotBlank(dataJson)&&!JSON.isValid(request.getDataJson())){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"dataJson格式不正确");
                }
            }

            @Override
            public void assembleResult(FundFlowCheckResult result, FundFlowCheckResult model) {
                if(model!=null){
                    result.setResultCode(model.getResultCode());
                    result.setResultDesc(model.getResultDesc());
//                    result.setCheckResultList(model.getCheckResultList());
                    result.setModelCheckResultList(model.getModelCheckResultList());
                    result.setExtInfo(model.getExtInfo());
                    result.setSuccess(true);
                }else{
                    result.setSuccess(false);
                }
            }

            private Class[] getAssetModelCheckScene(){
                return new Class[]{
                        FundFlowModelMainGraphCheck.class,
                        FundFlowModelSubGraphCheck.class,
                        FundFlowModelNodeCheck.class
                };
            }

            private FundFlowCheckResult doCheck(Class checkClass,TableDataContext context){
                AbstractCheckScene checkScene = null;
                try {
                    checkScene = (AbstractCheckScene)checkClass.getConstructor(TableDataContext.class).newInstance(context);
                } catch (Exception e) {
                    //e.printStackTrace();
                    LOGGER.error("校验场景{} doCheck生成校验实例异常",checkClass);
                }
                checkScene.setDataContext(context);
                return checkScene.executeTask();
            }

            @Override
            public FundFlowCheckResult getResultInstance() {
                return new FundFlowCheckResult();
            }

            @Override
            public FundFlowCheckResult execute() {
                TableDataContext context = new TableDataContext();
                FundFlowTransGraph graph = new FundFlowTransGraph();

                String fullDataContent = null;
                //当shareCode不为空时，从数据库中获取数据
                if(StringUtil.isNotBlank(request.getShareCode())){
                    FullTraceValidateDataFetchRecordDTO dto = fullTraceValidateDataFetchRecordRepository.queryByShareCode(request.getShareCode());
                    if(dto!=null){
                        fullDataContent = dto.getContent();
                    }else{
                        throw new BusinessException(BusinessErrorEnum.SYSTEM_ERROR,"根据shareCode={"+request.getShareCode()+"}未查询到数据");
                    }
                }else{
                    fullDataContent = request.getDataJson();
                }

                //1.获取资金流图
                FundFlowAnalyze analyze = new FundFlowAnalyze();
                List<TransactionGroup> groupList = analyze.analyzeFundFlowGroup(fullDataContent);
                //初始化数据上下文.
                context.initFromContent(fullDataContent,graph.getFundFlowClasses(),groupList);

                long start = System.currentTimeMillis();
//                ExecutorService service = Executors.newFixedThreadPool(10);
                SofaThreadPoolExecutor service = new SofaThreadPoolExecutor(10, 10, 60,
                        TimeUnit.SECONDS, new LinkedBlockingQueue<>(10));
                long end = System.currentTimeMillis();
                LOGGER.info("线程池创建完成，共耗时{}s",(end-start)/1000.0);
                context.setExecutorService(service);

                //3.资金模型检测
                Class[] assetModelClasses = this.getAssetModelCheckScene(); //资金模型检测
                LOGGER.info("获取到资金模型校验场景{}个，校验列表为{}",assetModelClasses.length,assetModelClasses);

                FundFlowCheckResult result = new FundFlowCheckResult();
                for (Class classType: assetModelClasses) {
                    result.merge(doCheck(classType,context));
                }
                service.shutdown();
                result.setSuccess(true);
                return result;
            }
        });
        return result;
    }
}
