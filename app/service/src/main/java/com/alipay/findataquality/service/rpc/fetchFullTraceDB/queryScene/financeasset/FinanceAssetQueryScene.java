package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.financeasset;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetAntTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetInsTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAftransCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAftransCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName YebTransInQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 10:32
 * @Version V1.0
 **/
public class FinanceAssetQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FinanceAssetQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                FinanceAssetTask.class,
                FinanceAssetInsTask.class,
                FinanceAssetAntTask.class,

                FinancingcoreCommonTask.class,
                YebcoreAftransCommonTask.class,
                AccLogCommonTask.class,
                //AccLogEpTask.class,
                //AccLogFundEpTask.class,

                MftransLogCommonTask.class,
                InstpayCommonTask.class,
                ProdtransTask.class,
                FinancingcoreCommonOpTask.class,
                MftransLogCommonOpTask.class,

                YebcoreAftransCommonOpTask.class,
                ObcloudpayTask.class,

                AccLogInnerAccountTask.class,
                AccLogInnerAccount2Task.class,
                AccLogInnerAccount3Task.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{
                0,0,0,
                1,1,1,
                2,2,2,2,2,
                3,3,
                4,4,4
        };
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FINANCE_ASSET_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
        context.putGlobal("db_r", DbUtils.getDbrFromDbFlag(context.getValue("db_flag")));
    }
}
