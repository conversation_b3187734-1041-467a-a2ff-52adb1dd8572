package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreSubcardTradeTask extends AbstractQueryTask {

    public YebcoreSubcardTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //子卡存在和主卡交互转入、转出场景
                "select * from fmp_yeb_trans_in_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_trans_in_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                //转出
                "select * from fmp_yeb_trans_out_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_trans_out_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))" ,
                //查询扩展表
                "select * from fmp_yeb_ext_${db_flag} where order_no in (${fc_combine_order_list})",
                //分账任务捞取
                "select * from yebs_subcard_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{null,null,"order_no->fc_combine_order_list","order_no->fc_combine_order_list",null,null,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[]{null,null,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null,null,null};
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValueAppend("fc_combine_order_list","mftrans_combine_order_list");
        context.copyValueAppend("mftrans_combine_order_list","prodtrans_out_biz_no_list");
    }
}
