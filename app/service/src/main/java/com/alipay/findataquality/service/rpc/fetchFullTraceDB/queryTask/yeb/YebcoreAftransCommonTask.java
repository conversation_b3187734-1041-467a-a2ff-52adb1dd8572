package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreAftransCommonTask extends AbstractQueryTask {

    public YebcoreAftransCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //分账任务捞取
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${aftrans_order_list}) or cnl_no in (${aftrans_order_list}))",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${aftrans_order_list}) or cnl_no in (${aftrans_order_list}))",
                "select * from yebs_subcard_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${aftrans_order_list}) or cnl_no in (${aftrans_order_list}))",
                "select * from yebs_trans_in_order_${db_flag} where order_no in (${aftrans_order_list})",
                "select * from fmp_yeb_trans_in_order_${db_flag} where order_no in (${aftrans_order_list})",
                "select * from fmp_yeb_trans_out_order_${db_flag} where order_no in (${aftrans_order_list})",
                "select * from yebs_trans_out_order_${db_flag} where order_no in (${aftrans_order_list})",
                "select * from fmp_yeb_ext_${db_flag} where order_no in (${aftrans_order_list})",
                "select * from yebs_transfer_order_${db_flag} where order_no in (${aftrans_order_list})"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[9];
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[9];
    }
}
