package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import com.alibaba.common.lang.StringUtil;

import com.alipay.oceanbase.util.SecureIdentityLoginModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName DbDecodeUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 20:28
 * @Version V1.0
 **/
public class DbUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(DbUtils.class);

    /**
     * 通用加密方法
     * @param text
     * @return
     */
    public static String commonEncode(String text){
        return encode(text);
    }

    /**
     * 通用解密方法
     * @param text
     * @return
     */
    public static String commonDecode(String text){
        return decode1(text);
    }


    public static String decodeZdal(String encodePwd) {
        String input = encodePwd;
        String pwd = decode1(input);
        if (StringUtil.isBlank(pwd)) {
            pwd = obDecode(input);
        }
        return pwd;
    }

    private static String decode1(String pwd) {
        try {
            //return DefaultEncryptionService.decode(null, pwd);
            return ZdalEncryptionUtil.decode(null,pwd);
        } catch (Exception e) {
            LOGGER.warn("decode1发生异常{}",e);
        }
        return null;
    }

    private static String obDecode(String pwd) {
        try {
            return SecureIdentityLoginModule.decode(pwd);
        } catch (Exception e) {
            LOGGER.warn("obDecode发生异常{}",e);
        }
        return null;
    }

    public static String encode(String pwd){
        try {
            //return DefaultEncryptionService.encode(null, pwd);
            return ZdalEncryptionUtil.encode(null,pwd);
        } catch (Exception e) {
            LOGGER.warn("encode发生异常{}",e);
        }
        return null;
    }

    public static String getDbSchemaFromDbFlag(String dbFlag){
        if(!StringUtil.isEmpty(dbFlag)){
            try{
                return "0"+dbFlag.charAt(0);
            }catch (Exception e){
                return null;
            }
        }
        return null;
    }

    public static String getDbrFromDbFlag(String dbFlag){
        if(!StringUtil.isEmpty(dbFlag)){
            try{
                int v = Integer.parseInt(dbFlag);
                int r = v/20;
                return "r"+r;
            }catch (Exception e){
                return null;
            }
        }
        return null;
    }

    public static String getInstpaySchemaFromDbFlag(String dbFlag){
        try{
            int flag = Integer.parseInt(dbFlag);
            String schema = flag/50+"";
            return schema;
        }catch (Exception e){
            LOGGER.warn("getInstpaySchemaFromDbFlag发生异常{}",e);
        }
        return null;
    }

    /**
     * 获取fintraderecord分库分表位，格式为倒数二三位+倒数第四位
     * 如：2088402257173122，为123
     * @param uid
     * @return
     */
    public static String getFinTradeRecordSchemaFromUid(String uid){
        try{
            String a = StrUtils.substrFromEnd(uid,3,2);
            String b = StrUtils.substrFromEnd(uid,4,1);
            String schema = a+b;
            return schema;
        }catch (Exception e){
            LOGGER.warn("getFinTradeRecordSchemaFromUid发生异常{}",e);
        }
        return null;
    }

    /**
     * 获取产品账类型分表位，格式为倒数二三位_(月份-1)
     * @param dbFlag
     * @param date
     * @return
     */
    public static String getProdtransSchemaFromDbFlag(String dbFlag,String date){
        if(!StringUtil.isBlank(dbFlag)&&!StringUtil.isBlank(date)){
            String monthStr = StrUtils.substrFromStart(date,4,2);
            try{
                int month = Integer.parseInt(monthStr);
                if(month>0&&month<=12){
                    String prodtransFlag = StrUtils.fillBlank((month-1)+"",'0',2);
                    //prodtrans的分库分表位是分表位+(月份-1)
                    return dbFlag+"_"+prodtransFlag;
                }
            }
            catch (Exception e){
                LOGGER.warn("getProdtransSchemaFromDbFlag发生异常{}",e);
            }
        }
        return null;
    }
}
