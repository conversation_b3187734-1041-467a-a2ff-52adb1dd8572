package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene;

import com.alibaba.common.lang.StringUtil;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QueryTaskResult;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @ClassName BaseQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 19:03
 * @Version V1.0
 **/
public abstract class AbstractQueryScene {
    private static final Logger logger = LoggerFactory.getLogger(AbstractQueryTask.class);
    //取数任务最大重试次数
    private static final int maxRetryTimes = 2;

    private QueryContext queryContext;

    public QueryTaskResult doQuery(){
        //初始化上下文
        initContext();
        //获取并执行取数任务
        QueryTaskResult result = this.executeTask();
        return result;
    }

    /**
     * 合并结果
     * @param dest
     * @param src
     */
    private void mergeResult(QueryTaskResult dest, QueryTaskResult src){
        if(dest==null||src==null){
            return;
        }
        dest.getTableData().addAll(src.getTableData());
        dest.getKeyData().addAll(src.getKeyData());
    }

    /**
     * BaseQueryScene
     * @param queryContext
     */
    public AbstractQueryScene(QueryContext queryContext){
        this.queryContext=queryContext;
    }

    private Map<Integer,List<AbstractQueryTask>> getTaskMap(){
        AbstractQueryTask[] tasks = getAllTasks();
        int[] taskRank= queryTaskRank();
        if(taskRank==null||tasks==null||taskRank.length!=tasks.length){
            logger.warn("{}取数场景任务数量同优先级配置不匹配，执行中断",this.getClass().getSimpleName());
            return null;
        }
        Map<Integer,List<AbstractQueryTask>> taskMap = new TreeMap<>();
        for (int i = 0; i < taskRank.length; i++) {
            if(taskMap.containsKey(taskRank[i])){
                taskMap.get(taskRank[i]).add(tasks[i]);
            }else{
                List<AbstractQueryTask> taskList = new ArrayList<>();
                taskList.add(tasks[i]);
                taskMap.put(taskRank[i],taskList);
            }
        }
        return taskMap;
    }

    private QueryTaskResult executeTask(){
        logger.info("{}全链路取数任务开始执行",this.queryScene().getCode());
        long start = System.currentTimeMillis();
        QueryTaskResult result = new QueryTaskResult();
        //获取待执行列表
        Map<Integer,List<AbstractQueryTask>> taskMap = getTaskMap();
        if(taskMap==null){
            return null;
        }
        //获取线程池
        ExecutorService executor = this.getQueryContext().getExecutorService();
        Set<Integer> keys = taskMap.keySet();
        for (Integer key: keys) {
            List<Future<QueryTaskResult>> futures = new ArrayList<>();
            for (AbstractQueryTask task: taskMap.get(key)) {
                logger.info("{}添加执行任务{}，优先级{}",this.getClass().getSimpleName(),task.getClass().getSimpleName(),key);
                futures.add(executor.submit(task));
            }
            //Task列表
            List<AbstractQueryTask> allTask = new ArrayList<>(taskMap.get(key));
            // 收集并打印所有查询的结果
            //任务重试次数
            Map<AbstractQueryTask,Integer> retryMap = new HashMap<>();
            for(int i=0;i<futures.size();i++) {
                Future<QueryTaskResult> future = futures.get(i);
                try {
                    //get()会阻塞直到有结果，此处设定每个任务最长等待5s
                    long timeout = 2L;
                    TimeUnit unit = TimeUnit.SECONDS;
                    mergeResult(result,future.get(timeout,unit));
                } catch (InterruptedException | ExecutionException | TimeoutException e) {
                    AbstractQueryTask currTask = allTask.get(i);
                    String taskName = currTask.getClass().getSimpleName();
                    logger.warn("{}执行任务{}异常，异常信息如下：\n{}",this.getClass().getSimpleName(),taskName,e);
                    //e.printStackTrace();
                    //对于timeout的类型，允许进行取数任务重试
                    if(e instanceof TimeoutException){
                        int times = retryMap.getOrDefault(currTask,0);
                        if(times<maxRetryTimes){
                            futures.add(executor.submit(currTask));
                            allTask.add(currTask);
                            int retry = times+1;
                            retryMap.put(currTask,retry);
                            logger.info("{}执行任务{}异常已重新加入队列，当前重试次数{}",this.getClass().getSimpleName(),taskName,retry);
                        }else{
                            logger.info("{}执行任务{}异常已重试{}次，达到重试上限",this.getClass().getSimpleName(),taskName,maxRetryTimes);
                        }
                    }
                }
            }
        }

        /**
         * 识别traceId信息
         */
        String traceIds = StrUtils.getUniqueTraceIds(this.getQueryContext().getValue("trace_id"));
        if(StringUtil.isNotBlank(traceIds)){
            logger.info("全链路取数中识别到traceIds={}",traceIds);
            result.setTraceIds(traceIds);
        }
        long end = System.currentTimeMillis();
        logger.info("全链路取数任务执行完成，共执行{}条SQL，获取数据{}条，耗时{}s，数据获取详情如下:\n{}",result.getTotalSqlCnt(),result.getTotalRowCnt(),(end-start)/1000.0,result.getTotalRowSumStr());
        return result;
    }

    private AbstractQueryTask[] getAllTasks(){
        Class[]taskClassList= queryTask();
        if(taskClassList==null){
            return null;
        }
        AbstractQueryTask[] taskInstanceList = new AbstractQueryTask[taskClassList.length];
        for (int i = 0; i < taskInstanceList.length; i++) {
            try {
                //获取构造参数
                Constructor<AbstractQueryTask> baseTask =  taskClassList[i].getConstructor(QueryContext.class);
                //通过构造参数构造实例
                taskInstanceList[i] = baseTask.newInstance(this.getQueryContext());
            } catch (Exception e) {
                logger.warn("{}取数任务实例化失败,e={}",this.getClass().getSimpleName(),e);
            }
        }
        return taskInstanceList;
    }

    /**
     * 查询任务列表
     */
    public abstract Class[] queryTask();

    public abstract int[] queryTaskRank();

    public abstract QuerySceneEnum queryScene();

    private String inputName(){
        return this.queryScene().getInputName();
    }

    //初始化上下文，如需要在子类中覆写
    public abstract void handleContext(QueryContext context, String inputValue);

    public void initContext(){
        //上下文放入初始字段
        QueryContext context = this.getQueryContext();
        context.putGlobal(this.queryScene().getInputName(),context.getInputValue().trim());
        //初始化上下文信息
        handleContext(context,context.getInputValue());
    }

    public QueryContext getQueryContext() {
        return queryContext;
    }

    public void setQueryContext(QueryContext queryContext) {
        this.queryContext = queryContext;
    }
}
