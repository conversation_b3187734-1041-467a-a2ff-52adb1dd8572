package com.alipay.findataquality.service.ai.agent;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;

@Controller
@RequestMapping("/agent")
public class AgentController {

    @Autowired(required = false)
    private AgentService agentService;

    @GetMapping("/react_stream")
    public SseEmitter steamChat(@RequestParam String input, HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("text/event-stream");

        Flux<String> fluxResponse = agentService.useStreamReactAgent(input);

        SseEmitter emitter = new SseEmitter();
        fluxResponse.subscribe(chatResponse -> {
            SseEmitter.SseEventBuilder event = SseEmitter.event().data(chatResponse, MediaType.TEXT_PLAIN);
            try {
                emitter.send(event);
            } catch (IOException e) {
                emitter.completeWithError(e); // 发送错误事件
            }
        }, emitter::completeWithError, emitter::complete);

        return emitter;
    }

}
