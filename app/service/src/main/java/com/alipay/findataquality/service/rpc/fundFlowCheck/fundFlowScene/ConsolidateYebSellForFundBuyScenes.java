package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import com.iwallet.biz.common.util.money.Money;

import java.util.LinkedList;
import java.util.List;

/**
 * 组合余额宝申购买基金的交易阶段场景，需要区分是否是高保以及基金支付链路，最终得到组合场景
 * -- 此处默认余额宝申购基金在一笔完整场景中不存在发生多次
 */
public class ConsolidateYebSellForFundBuyScenes implements ConsolidateScenes{
    @Override
    public TransactionComposedScene consolidateStageScenes(List<TransactionStageScene> transactionStageScenes) {
        TransactionComposedScene transactionComposedScene = new TransactionComposedScene();
        StringBuffer composedSceneDesc = new StringBuffer();
        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
        Money sumAmount = new Money(0);
        int transactionTimes = 0;

        //此处默认余额宝申购基金在一笔完整场景中不存在发生多次
        for (TransactionStageScene transactionStageScene:transactionStageScenes){
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.YEB_TO_FUND_DUMMY){
                composedSceneDesc.append("余额宝申购基金(普通交易链路)");
                sumAmount = transactionStageScene.getAmount();
                transactionStageSceneList.add(transactionStageScene);
                transactionTimes++;
                break;
            } else if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ) {
                composedSceneDesc.append("余额宝申购基金(高保非基金支付链路)");
                sumAmount = transactionStageScene.getAmount();
                transactionStageSceneList.add(transactionStageScene);
                transactionTimes++;
                break;
            } else if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ) {
                composedSceneDesc.append("余额宝申购基金(高保基金支付链路)");
                sumAmount = transactionStageScene.getAmount();
                transactionStageSceneList.add(transactionStageScene);
                transactionTimes++;
                break;
            }
        }
        if (transactionTimes==0){
            return null;
        }else{
            //组装结果
            StringBuffer composedSceneDescFinalBuffer = new StringBuffer();
            composedSceneDescFinalBuffer.append("[金额").append(sumAmount).append("元]").append(composedSceneDesc).append("\n");
            transactionComposedScene.setComposedSceneDesc(composedSceneDescFinalBuffer.toString());
            transactionComposedScene.setSumAmount(sumAmount);
            transactionComposedScene.setTransactionStageSceneList(transactionStageSceneList);
            return transactionComposedScene;
        }
    }
}
