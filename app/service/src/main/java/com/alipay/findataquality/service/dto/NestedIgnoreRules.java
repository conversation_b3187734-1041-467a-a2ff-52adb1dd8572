/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.findataquality.service.dto;

import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version : NestedIgnoreRules.java, v 0.1 2025年03月17日 14:31 zhaolinling Exp $
 */
public class NestedIgnoreRules {
    private final Map<String, Set<String>> rules;

    public NestedIgnoreRules(Map<String, Set<String>> rules) {
        this.rules = rules;
    }

    public Set<String> getNestedKeys() {
        return rules.keySet();
    }

    public Set<String> getSubKeys(String parentKey) {
        return rules.getOrDefault(parentKey, Collections.emptySet());
    }

    public Set<String> getTopLevelKeys() {
        return rules.keySet().stream()
                .filter(k -> !k.contains("."))
                .collect(Collectors.toSet());
    }

}