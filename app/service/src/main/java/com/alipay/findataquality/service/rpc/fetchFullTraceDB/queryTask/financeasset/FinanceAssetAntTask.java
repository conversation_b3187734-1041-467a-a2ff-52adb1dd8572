package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName FinanceAssetTask
 * @Description FA ins部署单元
 * <AUTHOR>
 * @Date 2024/5/30 13:57
 * @Version V1.0
 **/
public class FinanceAssetAntTask extends FinanceAssetTask {
    public FinanceAssetAntTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCE_ASSET_ANT;
    }

    @Override
    public String[]querySqlBlackList(){
        return new String[]{"fa_async_trade_detail"};
    }
}
