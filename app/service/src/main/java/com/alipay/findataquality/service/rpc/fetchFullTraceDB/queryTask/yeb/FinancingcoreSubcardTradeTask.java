package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName FinancingcoreTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 11:06
 * @Version V1.0
 **/
public class FinancingcoreSubcardTradeTask extends AbstractQueryTask {
    public FinancingcoreSubcardTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCINGCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                /*
                "select * from yeb_asset_increase_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in ('${order_no}','${biz_no}') or out_biz_no in ('${order_no}','${biz_no}') or cnl_no in ('${order_no}','${biz_no}'))",
                "select * from yeb_asset_decrease_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in ('${order_no}','${biz_no}') or out_biz_no in ('${order_no}','${biz_no}') or cnl_no in ('${order_no}','${biz_no}'))",
                "select * from yeb_asset_freeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in ('${order_no}','${biz_no}') or out_biz_no in ('${order_no}','${biz_no}') or cnl_no in ('${order_no}','${biz_no}'))",
                "select * from yeb_asset_unfreeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in ('${order_no}','${biz_no}') or out_biz_no in ('${order_no}','${biz_no}') or cnl_no in ('${order_no}','${biz_no}'))",
                "select * from yeb_asset_instruction_${db_flag} where instruction_id like '${date}%' and asset_order_id in (${asset_order_id})"
                 */
                "select * from yeb_asset_increase_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                "select * from yeb_asset_decrease_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                "select * from yeb_asset_freeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                "select * from yeb_asset_unfreeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                "select * from yeb_asset_instruction_${db_flag} where instruction_id like '${date}%' and asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_batch_mftrans_order_${db_flag} where asset_order_id in (${asset_order_id})"
        };
    }

    @Override
    public String[] outcomeValue() {
        String key = "asset_order_id,payment_id,asset_account_no->common_mf_account_no_list,ext_info.fcTraceId->trace_id";
        return new String[]{key,key,key,key,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null};
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        //产品账取数使用
        context.copyValueAppend("common_mf_account_no_list","prodtrans_principal_id_list");
        context.copyValueAppend("mftrans_combine_order_list","prodtrans_out_biz_no_list");
    }
}
