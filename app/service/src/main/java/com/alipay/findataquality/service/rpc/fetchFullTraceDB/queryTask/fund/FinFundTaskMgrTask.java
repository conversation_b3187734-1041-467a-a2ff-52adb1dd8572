package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
/**
 * @ClassName FinFundTaskMgrTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/19 19:50
 * @Version V1.0
 **/
public class FinFundTaskMgrTask extends AbstractQueryTask {
    public FinFundTaskMgrTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINFUNDTASKMGR;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from fund_charge_base_data where biz_date='${bizDate}' and fund_code = '${fundCode}'",
                "select * from fund_charge_schedule where biz_date='${bizDate}' and fund_code = '${fundCode}'",
                "select * from fund_charge_event_flow where biz_date='${bizDate}' and fund_code = '${fundCode}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[3];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[3];
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        String input = context.getInputValue();
        if(!input.contains("-")){
            return;
        }
        String inputParts[]=input.split("-");
        context.putGlobal("bizDate",inputParts[0].trim());
        context.putGlobal("fundCode",inputParts[1].trim());
    }
}
