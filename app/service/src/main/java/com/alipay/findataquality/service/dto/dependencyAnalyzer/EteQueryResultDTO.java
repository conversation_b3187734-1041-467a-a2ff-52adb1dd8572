package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 端到端查询结果DTO
 * @auther lidengke.ldk
 */
public class EteQueryResultDTO {
    /**
     * 执行状态
     */
    private EteResultEnum executeStatus;
    /**
     * 执行结果
     */
    private EteResultEnum executeResult;
    /**
     * traceId
     */
    private String traceId;
    /**
     * 执行详情描述
     */
    private String message;
    /**
     * 记录额外信息
     */
    private Map<String, Object> recordExt;

    private String flowInstanceCode;

    private String flowRecordCode;

    private String tradeNo;
    /**
     * 端到端节点入参
     */
    private Map<String, String> flowNodeInputs;

    /**
     * 请求参数
     */
    private JSONArray requestParams;
    /**
     * 返回结果
     */
    private JSONObject responseContent;

    /**
     * 返回结果
     */
    private String responseContentExt;

    /**
     * linkd环境
     */
    private String linkdEnv;
    /**
     * 每个子节点的查询结果
     */
    private List<EteQueryResultDTO> subQueryResultDTOList;
    /**
     * 起始时间
     */
    private Long begin;

    public EteResultEnum getExecuteStatus() {
        return executeStatus;
    }

    public void setExecuteStatus(EteResultEnum executeStatus) {
        this.executeStatus = executeStatus;
    }

    public EteResultEnum getExecuteResult() {
        return executeResult;
    }

    public void setExecuteResult(EteResultEnum executeResult) {
        this.executeResult = executeResult;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getRecordExt() {
        return recordExt;
    }

    public void setRecordExt(Map<String, Object> recordExt) {
        this.recordExt = recordExt;
    }

    public String getFlowInstanceCode() {
        return flowInstanceCode;
    }

    public void setFlowInstanceCode(String flowInstanceCode) {
        this.flowInstanceCode = flowInstanceCode;
    }

    public String getFlowRecordCode() {
        return flowRecordCode;
    }

    public void setFlowRecordCode(String flowRecordCode) {
        this.flowRecordCode = flowRecordCode;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public Map<String, String> getFlowNodeInputs() {
        return flowNodeInputs;
    }

    public void setFlowNodeInputs(Map<String, String> flowNodeInputs) {
        this.flowNodeInputs = flowNodeInputs;
    }

    public JSONArray getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(JSONArray requestParams) {
        this.requestParams = requestParams;
    }

    public JSONObject getResponseContent() {
        return responseContent;
    }

    public void setResponseContent(JSONObject responseContent) {
        this.responseContent = responseContent;
    }

    public String getLinkdEnv() {
        return linkdEnv;
    }

    public void setLinkdEnv(String linkdEnv) {
        this.linkdEnv = linkdEnv;
    }

    public List<EteQueryResultDTO> getSubQueryResultDTOList() {
        return subQueryResultDTOList;
    }

    public void setSubQueryResultDTOList(List<EteQueryResultDTO> subQueryResultDTOList) {
        this.subQueryResultDTOList = subQueryResultDTOList;
    }

    public String getResponseContentExt() {
        return responseContentExt;
    }

    public void setResponseContentExt(String responseContentExt) {
        this.responseContentExt = responseContentExt;
    }

    public Long getBegin() {
        return begin;
    }

    public void setBegin(Long begin) {
        this.begin = begin;
    }

    /**
     * 抽取端到端调用中包含的切流单元业务信息
     * @return
     */
    public String extractKeyWords() {
        return Arrays.asList("trade_model=" + getNotEmptyString(this.getFlowNodeInputs().get("tradeModel")),
                        "operation=" + getNotEmptyString(this.getFlowNodeInputs().get("operation")),
                        "biz_type=" + getNotEmptyString(this.getFlowNodeInputs().get("bizType")),
                        "scene=" + getNotEmptyString(this.getFlowNodeInputs().get("scene")),
                        "biz_category=" + getNotEmptyString(this.getFlowNodeInputs().get("bizCategory")),
                        "biz_scene=" + getNotEmptyString(this.getFlowNodeInputs().get("bizScene")),
                        "channel_type=" + getNotEmptyString(this.getFlowNodeInputs().get("channelType")))
                .stream().collect(Collectors.joining(","));
    }

    private String getNotEmptyString(String str) {
        if(StringUtils.isBlank(str)) {
            return "null";
        }
        return str;
    }
}
