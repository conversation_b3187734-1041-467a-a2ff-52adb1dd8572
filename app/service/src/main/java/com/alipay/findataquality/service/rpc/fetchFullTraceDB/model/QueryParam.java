package com.alipay.findataquality.service.rpc.fetchFullTraceDB.model;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName SqlQueryConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 14:32
 * @Version V1.0
 **/
public class QueryParam {
    //输出参数列表
    private Map<String,String> outputMap=new HashMap<>();

    //需执行的sql
    private String sql;

    public QueryParam(String sql) {
        this.sql = sql;
    }

    public QueryParam(String sql,Map<String,String>outputMap) {
        this.sql = sql;
        this.outputMap = outputMap;
    }

    public Map<String, String> getOutputMap() {
        return outputMap;
    }

    public void setOutputMap(Map<String, String> outputMap) {
        this.outputMap = outputMap;
    }

    public String getSql() {
        return sql;
    }

    public void setSql(String sql) {
        this.sql = sql;
    }
}
