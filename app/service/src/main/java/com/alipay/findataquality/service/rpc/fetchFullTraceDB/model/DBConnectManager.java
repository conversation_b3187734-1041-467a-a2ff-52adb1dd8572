package com.alipay.findataquality.service.rpc.fetchFullTraceDB.model;


import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName DBConnectConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 15:21
 * @Version V1.0
 **/
public class DBConnectManager {
    private Map<QueryDBEnum, DBConnectConfig> dbConnectionInfoMap = new HashMap<>();

    public DBConnectManager(){
        init();
    }

    public DBConnectConfig getConnectionInfo(String className){
        return dbConnectionInfoMap.getOrDefault(className,null);
    }

    public Map<QueryDBEnum, DBConnectConfig> getDbConnectionInfoMap() {
        return dbConnectionInfoMap;
    }

    public void setDbConnectionInfoMap(Map<QueryDBEnum, DBConnectConfig> dbConnectionInfoMap) {
        this.dbConnectionInfoMap = dbConnectionInfoMap;
    }

    public void init(){
        for (QueryDBEnum dbType: QueryDBEnum.values()) {
            String configStr = DbUtils.commonDecode(getEncryption());
            JSONObject configObj = JSONObject.parseObject(configStr);
            DBConnectConfig config = getConfig(dbType,configObj);
            if(config!=null){
                this.dbConnectionInfoMap.put(dbType,config);
            }
        }
    }

    private DBConnectConfig getConfig(QueryDBEnum dbType, JSONObject configObj){
        DBConnectConfig config = new DBConnectConfig();
        config.setJdbcTypeEnum(dbType.getJdbc());
        JSONObject currentConfig = configObj.getJSONObject(dbType.name());
        if(currentConfig!=null){
            config.setDbName(currentConfig.getString("dbSchema"));
            config.setDbUser(currentConfig.getString("dbUser"));
            config.setZdalEncryption(currentConfig.getString("dbEncryption"));
            return config;
        }
        return null;
    }

    /**
     * 获取加密串，结构如下
     * {"数据库枚举name":{"dbSchema":"xx{db_flag}","dbUser":"xx","dbEncryption":"xx"}
     * @return
     */
    private String getEncryption(){
        return "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";
    }
}
