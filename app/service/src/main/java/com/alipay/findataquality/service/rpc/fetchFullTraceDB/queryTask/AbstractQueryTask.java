package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask;

import com.alibaba.common.lang.StringUtil;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.QueryTaskResult;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.DBConnectConfig;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryParam;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.sql.*;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName SqlQueryTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 12:10
 * @Version V1.0
 **/
public abstract class AbstractQueryTask implements Callable<QueryTaskResult> {
    private static final Logger logger = LoggerFactory.getLogger(AbstractQueryTask.class);

    //查询上下文
    private QueryContext queryContext;

    public AbstractQueryTask(QueryContext queryContext) {
        this.queryContext = queryContext;
    }

    @Override
    public QueryTaskResult call(){
        logger.info("开始执行任务{}",this.getClass().getSimpleName());
        long start = System.currentTimeMillis();
        boolean re = preProcess();
        if(!re){
            return null;
        }
        QueryTaskResult result = process();
        long end = System.currentTimeMillis();
        postProcess();
        logger.info("任务{}执行完成，共执行{}条SQL，获取到{}条数据，耗时{}s",this.getClass().getSimpleName(),result.getTotalSqlCnt(),result.getTotalRowCnt(),(end-start)/1000.0);
        return result;
    }

    private boolean postProcess(){
        handleContextAfter(this.getQueryContext());
        return true;
    }

    private boolean preProcess(){
        handleContextBefore(this.getQueryContext());
        //自动计算变量依赖
        String[] depedencyVars = queryDependency();
        logger.info("计算{}依赖的前置变量，结果为{}",this.getClass().getSimpleName(), StrUtils.joinArray(depedencyVars,","));

        if(depedencyVars!=null&&depedencyVars.length>0){
            for(String var: depedencyVars){
                String value = this.getQueryContext().getValue(var);
                if(value==null){
                    logger.warn("{}依赖的前置变量{}不存在，执行中断",this.getClass().getSimpleName(),var);
                    return false;
                }
            }
        }

        //前置SQL、输出值判断
        if(this.querySql()==null || this.outcomeValue()==null || this.outcomeType()==null
                || this.querySql().length != this.outcomeValue().length
                || this.outcomeValue().length != this.outcomeType().length){
            logger.warn("{}子类SQL及输出参数不匹配，执行中断",this.getClass().getSimpleName());
            return false;
        }
        return true;
    }

    private QueryTaskResult process(){
        QueryTaskResult queryTaskResult = new QueryTaskResult();
        //获取DB连接信息
        DBConnectConfig dbConfig=queryContext.getDbConnectConfigMap().get(this.queryDb());
        String url = replaceWithContext(dbConfig.getDbUrl(),queryContext);
        String userName = replaceWithContext(dbConfig.getDbUser(),queryContext);
        if(StringUtil.isBlank(url)||url.contains("$")||StringUtil.isBlank(userName)||userName.contains("$")){
            logger.warn("{}任务DB链接存在未替换变量，执行跳过: url={},userName={}",this.getClass().getSimpleName(),url,userName);
            return queryTaskResult;
        }
        String psd = dbConfig.getDbPassword();
        //连接DB并执行SQL
        try(Connection connection = DriverManager.getConnection(url,userName,psd);){
            try (Statement stmt = connection.createStatement();) {
                int index=0;
                List<QueryParam> queryList = this.getQueryList();
                for (QueryParam query : queryList) {
                    if(this.isSqlInBlackList(query.getSql())){
                        logger.info("{}任务SQL在黑名单中，执行跳过: {}",this.getClass().getSimpleName(),query.getSql());
                        continue;
                    }
                    String sql = replaceWithContext(query.getSql(),queryContext);
                    if(StringUtil.isBlank(sql)||sql.contains("$")){
                        logger.warn("{}任务SQL存在未替换变量，执行跳过: {}",this.getClass().getSimpleName(),sql);
                        continue;
                    }
                    logger.info("{}执行SQL: {}",this.getClass().getSimpleName(),sql);
                    TableData tableData = new TableData();
                    try (ResultSet rs = stmt.executeQuery(sql);) {
                        if(rs==null){
                            return null;
                        }
                        //存储DB、表及对应字段信息
                        ResultSetMetaData metaData = rs.getMetaData();
                        int cnt = metaData.getColumnCount();
                        tableData.setTableName(metaData.getTableName(1));
                        tableData.setDbName(this.queryDb().getCode());
                        for(int i = 1; i <= cnt; i++) {
                            String columnLabel = metaData.getColumnLabel(i);
                            tableData.addColumnName(columnLabel);
                        }
                        //循环读取对应的每行数据
                        while (rs.next()) {
                            List<String> dataList = new ArrayList<>();
                            for(int i = 1; i <= cnt; i++) {
                                //存储字段数据
                                String data = rs.getString(i);
                                dataList.add(data);
                            }
                            tableData.getColumnData().add(dataList);
                        }
                    }

                    handleOutCome(index,tableData);
                    handleContextEachSql(index,tableData,this.getQueryContext());
                    //添加到result结果中。对于一些仅做上下文透传的数据，可在子类中返回false，不放入最终结果中
                    if(this.outputToResult(index)){
                        queryTaskResult.getTableData().add(tableData);
                        //记录需要保留的关键数据
                        TableData keyData = getKeyData(index,tableData);
                        if(keyData!=null){
                            queryTaskResult.getKeyData().add(keyData);
                        }
                    }else{
                        logger.info("{}执行SQL后结果设置不放入最终数据集: {}",this.getClass().getSimpleName(),sql);
                    }
                    index++;
                }
                return queryTaskResult;
            }
        }catch (Exception e){
            logger.warn("{}执行SQL异常: {}",this.getClass().getSimpleName(),e);
            return null;
        }
    }

    /**
     * 处理输出值，支持的格式如下：
     * a,b,c，意为a->a,b->b,c->c，其中->右边表示放入的上下文变量名
     * a->d,b->d,c->d，意为a、b、c都放入d中
     * a|b|c->d，意为a、b、c都放入d中，为简化形式的表达
     * @param index
     * @param tableData
     */
    private void handleOutCome(int index,TableData tableData){
        if(StringUtil.isBlank(this.outcomeValue()[index])){
            return;
        }
        String[] outComes = this.outcomeValue()[index].split(",");
        OutTypeEnum outType = outcomeType()[index];
        for (String outCome: outComes) {
            outCome=outCome.trim();
            String srcVar = null;
            String destVar = null;
            if(outCome.contains("->")){
                String[] tmp = outCome.split("->");
                srcVar = tmp[0].trim();
                destVar = tmp[1].trim();
                if(StringUtil.isBlank(srcVar)||StringUtil.isBlank(destVar)){
                    continue;
                }
                //支持格式为a|b|c->d，等同于a->d,b->d,c->d
                if(srcVar.contains("|")){
                    String[] tmp2 = srcVar.split("\\|");
                    for (int i=0;i<tmp2.length;i++){
                        String varNamePart = tmp2[i].trim();
                        if(StringUtil.isNotBlank(varNamePart)){
                            handleSingleOutCome(outType,tableData,varNamePart,destVar);
                        }
                    }
                }else{
                    handleSingleOutCome(outType,tableData,srcVar,destVar);
                }
            }else{
                srcVar = outCome;
                destVar = outCome;
                handleSingleOutCome(outType,tableData,srcVar,destVar);
            }
        }
    }

    /**
     * 生成SQL语句
     * @param tableWithDbFlag
     * @param primaryVar
     * @param varsHasIndex
     * @param varsNoneIndex
     * @return
     */
    public String genSql(String tableWithDbFlag,String primaryVar,String valueSet,String[]varsHasIndex,String[] varsNoneIndex){
        if(StringUtil.isBlank(tableWithDbFlag)||StringUtil.isBlank(primaryVar)){
            return null;
        }
        String template = "select * from $tableName where $varsHasIndexCondition $varsNoneIndexCondition";
        /**
         * $varsHasIndexCondition格式
         * a in (${valueSet}) or b in (${valueSet})
         */
        String varsHasIndexCondition = "";
        String[] indexVars = StrUtils.addAndRemoveDuplicates(varsHasIndex,primaryVar);
        StringBuilder sb = new StringBuilder();
        int cnt=0;
        for (String indexVar: indexVars) {
            if(cnt++>0){
                sb.append(" or ");
            }
            sb.append(indexVar+" in (${"+valueSet+"})");
        }
        varsHasIndexCondition = sb.toString();
        /**
         * $varsNoneIndexCondition格式
         * and primaryVar like '${date}%' and (a in ${valueSet} or b in ${valueSet})
         */
        String varsNoneIndexCondition = "";
        StringBuilder sb2 = new StringBuilder();
        if(varsNoneIndex!=null&&varsNoneIndex.length>0){
            sb2.append("or ("+primaryVar+" like '${date}%' and (");
            int cnt2=0;
            for (String var: varsNoneIndex) {
                if(cnt2++>0){
                    sb2.append(" or ");
                }
                sb2.append(var+" in (${"+valueSet+"})");
            }
            sb2.append("))");
        }
        varsNoneIndexCondition = sb2.toString();
        template = template.replace("$tableName",tableWithDbFlag);
        template = template.replace("$varsHasIndexCondition",varsHasIndexCondition);
        template = template.replace("$varsNoneIndexCondition",varsNoneIndexCondition);
        return template;
    }

    /**
     * 处理单值输出
     * @param outType
     * @param tableData
     * @param srcVar
     * @param destVar
     */
    public void handleSingleOutCome(OutTypeEnum outType, TableData tableData, String srcVar, String destVar){
        switch(outType){
            case SINGLE:
                //放置单值，覆盖原值
                autoPutFist(tableData,srcVar,destVar);
                break;
            case ARRAY:
                //放置数组，覆盖原值
                autoPutArray(tableData,srcVar,destVar);
                break;
            case ARRAY_APPEND:
                //追加数组，在原基础上增加
                autoAppendArray(tableData,srcVar,destVar);
                break;
            default:break;
        }
    }

    /**
     * 是否为对手方任务
     * 对手方任务，如K12子卡，群成员转入到群主主卡，会同时涉及群成员及群主的单据，在捞取过程中，需要单独设置一个捞取群成员的任务，即为主卡的对手方任务
     * 对于是对手方任务类型的，在处理链接信息、SQL时需要将分库、分表替换为对应的对手方参数
     * 注：该处返回的是使用的对手方信息前缀，如原来为db_flag,假如设定为前缀为op，则返回为op_db_flag
     * @return
     */
    public String opTask(){
        return null;
    }

    /**
     * 执行前上下文处理
     * @param context
     */
    public void handleContextBefore(QueryContext context){}

    /**
     * 执行结束后上下文处理
     * @param context
     */
    public void handleContextAfter(QueryContext context){}


    /**
     * 执行过程中每个SQL的上下文处理
     * @param context 上下文
     * @param index 本次sql的执行索引
     * @param tableData 本次sql的执行结果
     */
    public void handleContextEachSql(int index, TableData tableData, QueryContext context){}

    public abstract QueryDBEnum queryDb();

    public abstract String[] querySql();

    public String[] querySqlBlackList(){
        return null;
    }

    public abstract String[] outcomeValue();

    /**
     * 默认返回ARRAY_APPEND类型，数组大小同querySql()长度一致
     * @return
     */
    public OutTypeEnum[] outcomeType(){
        OutTypeEnum[] array = new OutTypeEnum[this.querySql().length];
        Arrays.fill(array, OutTypeEnum.ARRAY_APPEND);
        return array;
    }

    /**
     * 需要重点导出的数据
     * @return
     */
    public String[] keyData(){
        return null;
    }

    /**
     * 判断sql是否在黑名单中
     * @param sql
     * @return
     */
    private boolean isSqlInBlackList(String sql){
        if(this.querySqlBlackList()==null){
            return false;
        }
        for (String sqlBlackList: this.querySqlBlackList()) {
            if(sql.contains(sqlBlackList)){
                return true;
            }
        }
        return false;
    }

    /**
     * 获取关键重要数据
     * @param index
     * @param tableData
     * @return
     */
    private TableData getKeyData(int index,TableData tableData){
        String[] importantValueList = keyData();
        if(importantValueList==null||importantValueList.length-1<index||tableData.getColumnData().isEmpty()){
            return null;
        }
        if(importantValueList[index]==null){
            return null;
        }
        //创建importantData表
        TableData importantData = new TableData();
        importantData.setDbName(tableData.getDbName());
        importantData.setTableName(tableData.getTableName());
        for(int i=0;i<tableData.getColumnData().size();i++){
            //默认添加N行数据，同tableData保持一致
            importantData.getColumnData().add(new ArrayList<>());
        }
        //获取需要输出的关键数据列表
        String varNames = importantValueList[index];
        String[] varNameList = varNames.split(",");
        for (String varName: varNameList){
            varName = varName.trim();
            if(varName.contains(".")){
                String[] varNamePart = varName.split("\\.");
                varNamePart[0]=varNamePart[0].trim();
                varNamePart[1]=varNamePart[1].trim();
                String[] valueList = tableData.getArrayValueList(varNamePart[0]);
                if(valueList==null){
                    continue;
                }
                //JSON格式的解析和放置
                for (int i = 0; i < valueList.length; i++) {
                    //保留原有的数据，包含大小写，故此处使用大小写敏感的方式
                    String itemValue = StrUtils.getJsonValue(valueList[i],varNamePart[1],false);
                    importantData.getColumnData().get(i).add(itemValue);
                }
                importantData.addColumnName(varName);
            }else{
                //非JSON格式直接添加
                String[] valueList = tableData.getArrayValueList(varName);
                if(valueList==null){
                    continue;
                }
                for (int i = 0; i < valueList.length; i++) {
                    importantData.getColumnData().get(i).add(valueList[i]);
                }
                importantData.addColumnName(varName);
            }
        }
        if(importantData.getColumnName().isEmpty()){
            return null;
        }
        return importantData;
    }

    /**
     * 结果是否输出到最终结果
     * @return
     */
    public int[] filterResultIndex(){
        return null;
    }

    public void autoPutFist(TableData tableData, String srcVar, String destVar){
        if(tableData==null||tableData.getColumnData().isEmpty()||srcVar==null||destVar==null){
            return;
        }
        String dataValue = getValueListFromTable(tableData,srcVar);
        String item = StrUtils.getFirstItem(dataValue);
        if(StringUtil.isNotBlank(item)){
            this.getQueryContext().putGlobal(destVar,item);
        }
    }

    public void autoAppendArray(TableData tableData, String srcVar, String destVar){
        if(tableData==null||tableData.getColumnData().isEmpty()||srcVar==null||destVar==null){
            return;
        }
        QueryContext context = this.getQueryContext();
        String dataValue = getValueListFromTable(tableData,srcVar);
        String currentContextValue = this.getQueryContext().getValue(destVar);
        String result = StrUtils.autoJoinArrayUnique(currentContextValue,dataValue,'\'');
        context.putGlobal(destVar,result);
    }

    public void autoPutArray(TableData tableData, String srcVar, String destVar){
        if(tableData==null||tableData.getColumnData().isEmpty()||srcVar==null||destVar==null){
            return;
        }
        QueryContext context = this.getQueryContext();
        String dataValue = getValueListFromTable(tableData,srcVar);
        String result = StrUtils.autoJoinArrayUnique(null,dataValue,'\'');
        context.putGlobal(destVar,result);
    }

    private String getValueListFromTable(TableData tableData, String srcVar){
        String dataValue = null;
        //带#认为从JSON格式的扩展字段中获取信息
        if(srcVar.contains(".")){
            String[] vars = srcVar.split("\\.");
            String[] list = tableData.getArrayValueList(vars[0]);
            StringBuilder sb = new StringBuilder();
            int cnt = 0;
            for (String item: list){
                String itemRe = StrUtils.getJsonValue(item,vars[1],false);
                if(!StringUtil.isBlank(itemRe)){
                    if(cnt++==0){
                        sb.append(itemRe);
                    }else{
                        sb.append(","+itemRe);
                    }
                }
            }
            dataValue = sb.toString();
        }else{
            //其他类型直接从数据表中获取
            dataValue = tableData.getArrayValue(srcVar);
        }
        if(StringUtil.isBlank(dataValue)){
            return null;
        }
        return dataValue;
    }

    public boolean outputToResult(int index){
        if(filterResultIndex()!=null){
            for(int idx: filterResultIndex()){
                if(idx == index){
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 提取一段sql中的table
     * @param sql
     * @return
     */
    public static String extractTableName(String sql) {
        String[] parts = sql.split("(?i)FROM "); // 使用(?i)忽略大小写
        if (parts.length != 2) {
            return null;
        }
        String tableName = parts[1].split(" ")[0];
        int index = tableName.lastIndexOf("_$");
        if (index >= 0 && tableName.length() > index + 2) {
            tableName = tableName.substring(0, index);
        }
        return tableName;
    }

    public List<QueryParam>getQueryList(){
        List<QueryParam> queryParams = new ArrayList<>();
        String[]sqlList = this.querySql();
        //循环处理对应的SQL值
        for(int i=0; i<sqlList.length; i++){
            //SQL
            String outCome = this.outcomeValue()[i];
            QueryParam queryParam = new QueryParam(sqlList[i]);
            //输出outCome
            if(outCome!=null){
                Map<String,String> map = new HashMap<>();
                map.put(outCome,outCome);
                queryParam.setOutputMap(map);
            }
            queryParams.add(queryParam);
        }
        return queryParams;
    }


    public String[]queryDependency(){
        return this.autoFindDependency();
    }

    public String[]autoFindDependency(){
        List<String> dependencyList = new ArrayList<>();
        Map<String,Boolean> exist = new HashMap<>();

        //内部SQL动态执行时的输出变量，最终依赖检查时刨除
        Map<String,Boolean>innerDependency = new HashMap<>();
        String[] outcomeValues = this.outcomeValue();
        for(int i=0; i<outcomeValues.length; i++){
            //为空则跳过
            if(StringUtil.isBlank(outcomeValues[i])){
                continue;
            }
            if(outcomeValues[i].contains(",")){
                String[]values = outcomeValues[i].split(",");
                for (String value: values) {
                    innerDependency.put(getInnerDependencyValue(value),true);
                }
            }else{
                innerDependency.put(getInnerDependencyValue(outcomeValues[i]),true);
            }
        }
        //计算SQL、DB连接信息中的依赖变量
        String[] sqlList = this.querySql();
        DBConnectConfig dbConfig=queryContext.getDbConnectConfigMap().get(this.queryDb());
        StringBuilder sb = new StringBuilder();
        for (String sql: sqlList) {
            sb.append(sql+"\n");
        }
        sb.append(dbConfig.getDbUrl()+"\n");
        sb.append(dbConfig.getDbUser()+"\n");
        Pattern pattern = Pattern.compile("\\$\\{(\\w+)}");
        Matcher matcher = pattern.matcher(sb.toString());
        while (matcher.find()) {
            String key = matcher.group(1);
            if(!exist.containsKey(key) && !innerDependency.containsKey(key)){
                dependencyList.add(key);
                exist.put(key,true);
            }
        }
        return dependencyList.toArray(new String[0]);
    }

    /**
     * 包含->的，返回->后面的值，即输出变量
     * 不包含->的，直接返回字符串过滤空白字符值
     * @param value
     * @return
     */
    private String getInnerDependencyValue(String value){
        if(value.contains("->")){
            String[]split = value.split("->");
            return split[1].trim();
        }else{
            return value.trim();
        }
    }

    public String replaceWithContext(String sql, QueryContext context){
        //如果为对手方任务，则替换分库、分表位为对手方信息
        if(opTask()!=null && StringUtil.isNotBlank(opTask().trim())){
            String opTaskPrefix = opTask().trim();
            sql = sql.replaceAll("\\{db_flag\\}","\\{"+opTaskPrefix+"_db_flag\\}");
            sql = sql.replaceAll("\\{db_schema\\}","\\{"+opTaskPrefix+"_db_schema\\}");
            sql = sql.replaceAll("\\{db_r\\}","\\{"+opTaskPrefix+"_db_r\\}");
            sql = sql.replaceAll("\\{prodtrans_db_flag\\}","\\{"+opTaskPrefix+"_prodtrans_db_flag\\}");
            sql = sql.replaceAll("\\{instpay_db_schema\\}","\\{"+opTaskPrefix+"_instpay_db_schema\\}");
        }

        Pattern pattern = Pattern.compile("\\$\\{(\\w+)}");
        Matcher matcher = pattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        Map<String,Boolean> notFoundMap = new HashMap<>();
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = context.getGlobalParams().get(key);
            if (value != null) {
                matcher.appendReplacement(sb, value);
            } else {
                if(notFoundMap.containsKey(key)){
                    continue;
                }
                logger.warn("{}执行SQL变量{}替换失败: {}",this.getClass().getSimpleName(),key,sql);
                notFoundMap.put(key,true);
            }
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    public QueryContext getQueryContext() {
        return queryContext;
    }

    public void setQueryContext(QueryContext queryContext) {
        this.queryContext = queryContext;
    }
}