package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.AccLogTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreTransOutTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName YebTransInQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 10:32
 * @Version V1.0
 **/
public class YebTransOutQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public YebTransOutQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                YebcoreTransOutTask.class,
                FinancingcoreTradeTask.class,
                MftransLogTask.class,
                ObcloudpayTask.class,
                AccLogTask.class,
                InstpayTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,0,1,1,1,1};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.YEB_TRANS_OUT_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
    }
}
