package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName FinancingcoreTask
 * @Description 用于获取对手方信息，如红包B类户、跨分表位转账等数据
 * <AUTHOR>
 * @Date 2024/5/23 11:06
 * @Version V1.0
 **/
public class FinancingcoreCommonOpTask extends FinancingcoreCommonTask {
    public FinancingcoreCommonOpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String opTask() {
        return "op";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
