package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreTransInTask extends AbstractQueryTask {

    public YebcoreTransInTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //直代销转入表
                "select * from fmp_yeb_trans_in_order_${db_flag} where order_no = '${order_no}'",
                "select * from yebs_trans_in_order_${db_flag} where order_no = '${order_no}'",
                "select * from fmp_yeb_ext_${db_flag} where order_no = '${order_no}'",
                //1.自动转入场景时会有转入任务流水，关联关系trade_no = '${order_no}'
                //2.切基时会落切基流水，关联关系biz_no like '${order_no}'
                "select * from fmp_deposit_task_flow_${db_flag} where task_flow_id like '${date}%' and (trade_no = '${order_no}' or biz_no like '%${order_no}%')",
                //转入并冻结场景会有冻结单
                "select * from yebs_freeze_order_${db_flag} where order_no like '${date}%' and biz_order_no = '${order_no}'",
                "select * from fmp_yeb_fz_order_${db_flag} where order_no like '${date}%' and biz_order_no = '${order_no}'",

                //子卡交易单捞取
                "select * from fmp_yeb_trans_notrade_${db_flag} where id like '${date}%' and (biz_order_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yebs_transfer_order_${db_flag} where order_no like '${date}%' and (biz_order_no = '${order_no}' or cnl_no = '${order_no}')",

                //分账任务捞取
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_subcard_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "order_no->fc_combine_order_list,payment_order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                "order_no->fc_combine_order_list,payment_order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                //"mf_account_no->common_mf_account_no_list,af_account_no->common_mf_account_no_list",
                null, null,
                //冻结解冻表
                "order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list,cnl_no->fc_combine_order_list",
                "order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list,cnl_no->fc_combine_order_list",
                //子卡单据表
                "id->fc_combine_order_list",
                "order_no->fc_combine_order_list",
                //分账表
                "aftrans_account_no->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                "aftrans_account->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                "aftrans_account_no->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list"
        };
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[]{OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND};
    }


    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValue("fc_combine_order_list", "mftrans_combine_order_list");
        context.copyValue("fc_combine_order_list", "prodtrans_out_biz_no_list");
    }
}
