package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;

/**
 * @ClassName FinmemberTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 15:26
 * @Version V1.0
 **/
public class FinmemberTask extends AbstractQueryTask {
    public FinmemberTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINMEMBER;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from fincif_agreement_${db_flag} where principal_id = '${user_id}'",
                "select * from fincif_contract_${db_flag} where principal_id = '${user_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{null,null};
    }

    @Override
    public void handleContextBefore (QueryContext context) {
        String dbr = DbUtils.getDbrFromDbFlag(context.getValue("db_flag"));
        context.putGlobal("db_r",dbr);
    }
}
