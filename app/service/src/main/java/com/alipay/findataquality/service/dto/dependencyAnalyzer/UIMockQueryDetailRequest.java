package com.alipay.findataquality.service.dto.dependencyAnalyzer;

public class UIMockQueryDetailRequest {
    String id;
    String previewSprintId;
    String include;

    String sprintId;
    String projectId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSprintId() {
        return sprintId;
    }

    public void setSprintId(String sprintId) {
        this.sprintId = sprintId;
    }

    public String getPreviewSprintId() {
        return previewSprintId;
    }

    public void setPreviewSprintId(String previewSprintId) {
        this.previewSprintId = previewSprintId;
    }

    public String getInclude() {
        return include;
    }

    public void setInclude(String include) {
        this.include = include;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    @Override
    public String toString() {
        return "{"
                + "\"id\":\""
                + id + '\"'
                + ",\"previewSprintId\":\""
                + previewSprintId + '\"'
                + ",\"include\":\""
                + include + '\"'
                + "}";

    }
}
