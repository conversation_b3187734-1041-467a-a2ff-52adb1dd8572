package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.prodTransData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;

/**
 * @ClassName ProdtransData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 15:20
 * @Version V1.0
 **/
public class ProdtransData extends AssetRecordData {

    public static final String PROD_LOG = "prodtrans.prod_log";
    public static final String PROD_FLOW_RECORD = "prodtrans.prod_flow_record";
    public static final String FREEZE_LIFETIME = "prodtrans.prod_lifetime";

    @Override
    public String[] dataTable() {
        return new String[]{
                PROD_LOG,
                PROD_FLOW_RECORD,
                FREEZE_LIFETIME
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        return null;
    }
}
