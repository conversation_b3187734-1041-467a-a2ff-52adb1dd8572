package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreSuperTradeTask extends AbstractQueryTask {

    public YebcoreSuperTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
//        String commonCondition = " where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))";
        return new String[]{
//                //直代销转入
//                "select * from fmp_yeb_trans_in_order_${db_flag}"+commonCondition,
//                "select * from yebs_trans_in_order_${db_flag}"+commonCondition,
//                //直代销转出
//                "select * from fmp_yeb_trans_out_order_${db_flag}"+commonCondition,
//                "select * from yebs_trans_out_order_${db_flag}"+commonCondition,
//                //直代销冻结、解冻
//                "select * from yebs_freeze_order_${db_flag}"+commonCondition,
//                "select * from fmp_yeb_fz_order_${db_flag}"+commonCondition,
                //切基时会落切基流水，关联关系biz_no like '${order_no}'
                "select * from fmp_deposit_task_flow_${db_flag} where task_flow_id like '${date}%' and (trade_no in (${fc_combine_order_list}) or ${fc_combine_order_like_conditions})",
                //分账任务捞取
                "select * from yebs_subcard_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))"
        };
    }

    @Override
    public String[] outcomeValue() {
//        String fcOrderCombine = "order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list,cnl_no->fc_combine_order_list";
        //fcOrderCombine,fcOrderCombine,fcOrderCombine,fcOrderCombine,fcOrderCombine,fcOrderCombine
        return new String[]{
                null,
                "aftrans_account_no->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                "aftrans_account->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list",
                "aftrans_account_no->af_account_no_list,order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list"
        };
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        //OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,
        //                OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,
        return new OutTypeEnum[]{null,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND};
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAsLikeWrapHead("fc_combine_order_list","fc_combine_order_like_conditions","biz_no");
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValueAppend("fc_combine_order_list","mftrans_combine_order_list");
        context.copyValueAppend("fc_combine_order_list","prodtrans_out_biz_no_list");
    }
}
