package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName MftransLogTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 15:16
 * @Version V1.0
 **/
public class MftransLogCommonTask extends AbstractQueryTask {
    public MftransLogCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.MFTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //"select * from mini_account_log_mf_${db_flag} where mini_trans_log_id like '${date}%' and ${mf_account_like_conditions} and (order_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                //"select * from mini_account_freeze_log_mf_${db_flag} where id like '${date}%' and ${mf_mini_account_like_conditions} and (order_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))"
                "select * from mini_account_log_mf_${db_flag} where mini_trans_log_id like '${date}%' and (order_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))",
                "select * from mini_account_freeze_log_mf_${db_flag} where id like '${date}%' and (order_no in (${mftrans_combine_order_list}) or cnl_no in (${mftrans_combine_order_list}))"

        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[2];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[2];
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAsLikeType("common_mf_account_no_list","mf_account_like_conditions","account_no");
        context.copyValueAsLikeType("common_mf_account_no_list","mf_mini_account_like_conditions","mini_account_no");
    }
}
