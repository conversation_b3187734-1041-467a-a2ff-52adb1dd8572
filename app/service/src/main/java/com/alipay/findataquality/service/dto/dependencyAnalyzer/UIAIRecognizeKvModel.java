package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import java.util.List;

public class UIAIRecognizeKvModel {
    List<Area> areas;

    public class Area{
        float score;
        int domain_idx;
        List<Integer> bndbox;
        float recog_score;
        String name;
        float detection_score;
        String value;

        public float getScore() {
            return score;
        }

        public void setScore(float score) {
            this.score = score;
        }

        public int getDomain_idx() {
            return domain_idx;
        }

        public void setDomain_idx(int domain_idx) {
            this.domain_idx = domain_idx;
        }

        public List<Integer> getBndbox() {
            return bndbox;
        }

        public void setBndbox(List<Integer> bndbox) {
            this.bndbox = bndbox;
        }

        public float getRecog_score() {
            return recog_score;
        }

        public void setRecog_score(float recog_score) {
            this.recog_score = recog_score;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public float getDetection_score() {
            return detection_score;
        }

        public void setDetection_score(float detection_score) {
            this.detection_score = detection_score;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        @Override
        public String toString() {
            return "{"
                    + "\"score\":"
                    + score
                    + ",\"domain_idx\":"
                    + domain_idx
                    + ",\"bndbox\":"
                    + bndbox
                    + ",\"recog_score\":"
                    + recog_score
                    + ",\"name\":\""
                    + name + '\"'
                    + ",\"detection_score\":"
                    + detection_score
                    + ",\"value\":\""
                    + value + '\"'
                    + "}";

        }
    }
    public List<Area> getAreas() {
        return areas;
    }
    public void setAreas(List<Area> areas) {
        this.areas = areas;
    }
    @Override
    public String toString() {
        return "{"
                + "\"areas\":"
                + areas
                + "}";
    }
}
