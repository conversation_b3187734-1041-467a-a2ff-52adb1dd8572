package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreAccountTask extends AbstractQueryTask {

    public YebcoreAccountTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //"select mf_account_no,af_account_no from yebs_sub_contract_${db_flag} where user_id = '${user_id}' and mf_account_no is not null and af_account_no is not null",
                "select * from yebs_sub_contract_${db_flag} where user_id = '${user_id}'",
                "select * from yebs_main_contract_${db_flag} where user_id = '${user_id}'",
                "select * from fmp_user_activity_control_${db_flag} where user_id = '${user_id}'",
                "select * from yeb_thfund_sign_info_${db_flag} where user_id = '${user_id}'",
                "select * from sf_sign_contract_${db_flag} where user_id = '${user_id}'",
                "select * from sf_sign_apply_${db_flag} where user_id = '${user_id}'",
                "select * from fmp_yeb_user_bind_${db_flag} where user_id  = '${user_id}'",
                "select * from fmp_yeb_sign_subcard_${db_flag} where user_id  = '${user_id}'",
                "select * from yebs_contract_activity_task_${db_flag} where user_id  = '${user_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        String[] outcomeValues = new String[9];
        outcomeValues[0]="mf_account_no->mf_account_no_list,mf_account_no->prodtrans_principal_id_list,user_id->prodtrans_principal_id_list,af_account_no->af_account_no_list";
        return outcomeValues;
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        OutTypeEnum[] outTypeEnums = new OutTypeEnum[9];
        outTypeEnums[0]=OutTypeEnum.ARRAY_APPEND;
        return outTypeEnums;
    }
//
//    @Override
//    public int[] filterResultIndex() {
//        return new int[]{0};
//    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValue("prodtrans_principal_id_list","prodtrans_principal_id_list_life_time");
    }
}
