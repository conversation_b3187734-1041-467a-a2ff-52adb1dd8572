package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans;


import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName FaassetFincomeTradeTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 13:33
 * @Version V1.0
 **/
public class FaassetFainsTradeTask extends FaassetFincomeTradeTask {
    public FaassetFainsTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FAASSET_FAINS;
    }
}
