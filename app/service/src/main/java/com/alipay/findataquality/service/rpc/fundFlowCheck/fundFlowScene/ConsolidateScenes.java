package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import java.util.List;

/**
 * 过滤器模式：处理交易阶段场景的组合，目标是得到最终的组合场景
 */
public interface ConsolidateScenes {

    public TransactionComposedScene consolidateStageScenes(List<TransactionStageScene> transactionStageScenes);

}
