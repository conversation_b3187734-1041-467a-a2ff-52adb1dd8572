package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import static com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinFundTradeTask.fundCommonSet;

/**
 * @ClassName FinstrategyTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/17 21:30
 * @Version V1.0
 **/
public class FinstrategyTask extends AbstractQueryTask {
    public FinstrategyTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINSTRATEGY;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                genSql("finstrategy_main_order_${db_flag}","strategy_main_order_id",fundCommonSet,null,new String[]{"out_biz_no","out_order_no"}),
                genSql("finstrategy_sub_order_${db_flag}","strategy_sub_order_id",fundCommonSet,new String[]{"strategy_main_order_id","downstream_order_id"},new String[]{"out_biz_no","out_order_no"}),
                genSql("finstrategy_main_order_${db_flag}","strategy_main_order_id",fundCommonSet,null,new String[]{"out_biz_no","out_order_no"}),
                genSql("finstrategy_trade_plan_${db_flag}","trade_plan_id",fundCommonSet,new String[]{"original_order_id"},null),
                genSql("finstrategy_trade_terms_order_${db_flag}","terms_order_id",fundCommonSet,null,new String[]{"out_biz_no","down_stream_order_id"}),
                genSql("finstrategy_trade_exec_record_${db_flag}","idempotent_field",fundCommonSet,new String[]{"exec_plan_id","order_id"},new String[]{"refer_order_id","trade_rule_id"}),
                genSql("finstrategy_trade_rule_${db_flag}","trade_rule_id",fundCommonSet,null,null),
                genSql("finstrategy_trade_exec_plan_${db_flag}","idempotent_field",fundCommonSet,new String[]{"exec_plan_id"},null),
                genSql("finstrategy_trade_pay_${db_flag}","pay_order_id",fundCommonSet,new String[]{"original_order_id","fa_order_id"},new String[]{"out_biz_no"})

        };
    }

    @Override
    public int[] filterResultIndex() {
        return new int[]{0};
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "user_id->user_id_list,strategy_main_order_id|out_biz_no|out_order_no->"+fundCommonSet,
                "user_id->user_id_list,strategy_sub_order_id|strategy_main_order_id|out_biz_no|out_order_no->"+fundCommonSet,
                "user_id->user_id_list,strategy_main_order_id|out_biz_no|out_order_no->"+fundCommonSet,
                "user_id->user_id_list,trade_plan_id|original_order_id->"+fundCommonSet,
                "user_id->user_id_list,terms_order_id|out_biz_no|down_stream_order_id->"+fundCommonSet,
                "idempotent_field|exec_plan_id|order_id|refer_order_id|trade_rule_id->"+fundCommonSet,
                "trade_rule_id->"+fundCommonSet,
                "idempotent_field|exec_plan_id|trade_rule_id->"+fundCommonSet,
                "pay_order_id|original_order_id|fa_order_id|out_biz_no->"+fundCommonSet
        };
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        if(index==0){
            String extInfo = tableData.getFirstValue("extension_info");
            String value = StrUtils.getJsonValue(extInfo,"FA_PAY_ID",false);
            context.putGlobal("main_order_id",value);
        }
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAppend("strategy_main_order_id",fundCommonSet);
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        super.handleContextAfter(context);
        context.copyValueAppend(fundCommonSet,"fa_trans_and_order_list");
    }
}
