package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.financeasset;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.AccLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetAntTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetInsTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinstrategyTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAftransCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName FinanceAssetFinStrategyQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/8/17 21:48
 * @Version V1.0
 **/
public class FaFinStrategyQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FaFinStrategyQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                FinstrategyTask.class,
                FinanceAssetTask.class,
                FinanceAssetInsTask.class,
                FinanceAssetAntTask.class,
                FinancingcoreCommonTask.class,
                ProdtransTask.class,
                YebcoreAftransCommonTask.class,
                MftransLogCommonTask.class,
                AccLogCommonTask.class,
                ObcloudpayTask.class,
                InstpayCommonTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,1,1,1,2,2,2,2,2,3,3};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FINANCE_ASSET_FINSTRATEGY_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
        context.putGlobal("db_r",DbUtils.getDbrFromDbFlag(context.getValue("db_flag")));
    }
}
