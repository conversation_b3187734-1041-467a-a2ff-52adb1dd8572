package com.alipay.findataquality.service.dto.dependencyAnalyzer;

public class MockConfig {
    String api;
    String type;
    int latency;
    String data;

    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getLatency() {
        return latency;
    }

    public void setLatency(int latency) {
        this.latency = latency;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "{"
                + "\"api\":\""
                + api + '\"'
                + ",\"type\":\""
                + type + '\"'
                + ",\"latency\":"
                + latency
                + ",\"data\":\""
                + data + '\"'
                + "}";

    }
}
