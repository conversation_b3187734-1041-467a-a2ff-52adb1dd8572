package com.alipay.findataquality.service.ai.mcp;

import com.alipay.sofa.ai.agent.Agent;
import com.alipay.sofa.ai.agent.react.ReActAgent;
import com.alipay.sofa.ai.antllm.AntLLMChatModel;
import com.alipay.sofa.ai.antllm.AntLLMChatOptions;
import com.alipay.sofa.ai.mcp.SofaMcpToolCallbackProvider;
import com.alipay.sofa.ai.mcp.client.SofaMcpClientFactory;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/mcp/client")
public class McpClientController {

    @Autowired(required = false)
    private SofaMcpClientFactory sofaMcpClientFactory;

    @Autowired(required = false)
    private SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider;

    @Autowired(required = false)
    private AntLLMChatModel antLLMChatModel;

    @GetMapping("/tools")
    public List<ToolCallback> tools() {
        SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
        ToolCallback[] toolCallbacks = sofaMcpToolCallbackProvider.getToolCallbacks();
        if ( toolCallbacks != null ) {
            return Arrays.asList(toolCallbacks);
        } else {
            return Arrays.asList();
        }
    }

    @GetMapping("/chat")
    public String chatWithMcp(@RequestParam String input, HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("text/event-stream");
        SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
        Prompt prompt = new Prompt(input, AntLLMChatOptions.builder()
                .withModel("Qwen3-235B-A22B")
                .withToolCallbacks(sofaMcpToolCallbackProvider.getToolCallbacks()).build());
        ChatResponse response = antLLMChatModel.call(prompt);
        return response.getResult().getOutput().getText();
    }

    @GetMapping("/stream")
    public SseEmitter streamWithMcp(@RequestParam String input, HttpServletResponse httpServletResponse) {
        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setContentType("text/event-stream");
        SofaMcpToolCallbackProvider sofaMcpToolCallbackProvider =
                new SofaMcpToolCallbackProvider(sofaMcpClientFactory.listMcpClients());
        Prompt prompt = new Prompt(input, AntLLMChatOptions.builder()
                .withModel("Qwen3-235B-A22B")
                .withToolCallbacks(sofaMcpToolCallbackProvider.getToolCallbacks()).build());
        Flux<ChatResponse> fluxResponse = antLLMChatModel.stream(prompt);

        SseEmitter emitter = new SseEmitter();
        fluxResponse.subscribe(chatResponse -> {
            String response = generateResponse(chatResponse);
            SseEmitter.SseEventBuilder event = SseEmitter.event().data(response, MediaType.APPLICATION_JSON);
            try {
                emitter.send(event);
            } catch (IOException e) {
                emitter.completeWithError(e); // 发送错误事件
            }
        }, emitter::completeWithError, emitter::complete);

        return emitter;
    }

    private String generateResponse(ChatResponse chatResponse) {
        return chatResponse.getResult().getOutput().toString();
    }


}
