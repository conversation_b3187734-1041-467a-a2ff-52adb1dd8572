/*
 * Alipay.com Inc.
 * Copyright (c) 2004 All Rights Reserved.
 */
package com.alipay.findataquality.service.dto;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;

import java.util.Date;
import java.util.List;

/**
 * 全链路联调数据捞取记录DTO类
 */
public class FullTraceValidateDataFetchRecordDTO {
    private static final long serialVersionUID = 741231858441822688L;

	/** 主键id */
	private Long id;

	/** 创建时间 */
    private Date gmtCreate;

    /** 编辑时间 */
    private Date gmtModified;

    /** 取数场景码 */
    private String sceneCode;
    /** 取数环境 */
    private String env;
    /** 操作人 */
    private String operator;
    /** 操作人工号 */
    private String operatorId;
    /** 取数结果 */
    private String content;
    /** 取数时间 */
    private Date fetchTime;
    /** 归档状态 */
    private String status;
    /** 入口单号 */
    private String entranceCode;
    /** 分享单号 */
    private String shareCode;
    /** 归档相关标注及说明字段 */
    private String markContent;
    /** 扩展信息 */
    private String extInfo;
    /** 关键信息 */
    private String keyData;
    /** 关键信息总结 */
    private String keyDataSummary;

    /** 取数数据列表对象 */
    private List<TableData> tableDataList;
    /** 关键信息列表对象 */
    private List<TableData> keyDataList;
    /** 归档相关标注及说明字段对象 */
    private MarkContentData markContentData;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getFetchTime() {
        return fetchTime;
    }

    public void setFetchTime(Date fetchTime) {
        this.fetchTime = fetchTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getEntranceCode() {
        return entranceCode;
    }

    public void setEntranceCode(String entranceCode) {
        this.entranceCode = entranceCode;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public String getMarkContent() {
        return markContent;
    }

    public void setMarkContent(String markContent) {
        this.markContent = markContent;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getKeyData() {
        return keyData;
    }

    public void setKeyData(String keyData) {
        this.keyData = keyData;
    }

    public String getKeyDataSummary() {
        return keyDataSummary;
    }

    public void setKeyDataSummary(String keyDataSummary) {
        this.keyDataSummary = keyDataSummary;
    }

    public List<TableData> getTableDataList() {
        return tableDataList;
    }

    public void setTableDataList(List<TableData> tableDataList) {
        this.tableDataList = tableDataList;
    }

    public List<TableData> getKeyDataList() {
        return keyDataList;
    }

    public void setKeyDataList(List<TableData> keyDataList) {
        this.keyDataList = keyDataList;
    }

    public MarkContentData getMarkContentData() {
        return markContentData;
    }

    public void setMarkContentData(MarkContentData markContentData) {
        this.markContentData = markContentData;
    }
}
