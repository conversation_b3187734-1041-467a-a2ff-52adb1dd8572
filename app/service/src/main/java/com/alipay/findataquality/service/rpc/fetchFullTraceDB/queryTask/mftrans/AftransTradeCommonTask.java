package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName MftransTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 13:51
 * @Version V1.0
 **/
public class AftransTradeCommonTask extends AbstractQueryTask {
    public AftransTradeCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.AFTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from mini_account_af_${db_flag} where ${af_account_like_conditions}",
                "select * from mini_account_freeze_af_${db_flag} where ${af_account_like_conditions}",
                "select * from mini_main_account_af_${db_flag} where ${af_main_account_like_conditions}",
                "select * from mini_account_log_af_${db_flag} where mini_trans_log_id like '${date}%' and (order_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}))",
                "select * from mini_account_freeze_log_af_${db_flag} where id like '${date}%' and order_no in (${mftrans_combine_order_list})"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{null,null,null,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{null,null,null,null,null};
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAsLikeType("af_account_no_list","af_account_like_conditions","mini_account_no");
        context.copyValueAsLikeType("af_account_no_list","af_main_account_like_conditions","mini_main_account_no");
    }
}
