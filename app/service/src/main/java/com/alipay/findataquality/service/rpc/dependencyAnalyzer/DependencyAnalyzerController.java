package com.alipay.findataquality.service.rpc.dependencyAnalyzer;

import com.alibaba.fastjson.JSON;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.DependencyAnalyzerService;

import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.BklightMockData;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result.DependencyAnalyzerResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@RestController
@RequestMapping("/api/excel")
public class DependencyAnalyzerController{

    private static final Logger logger = LoggerFactory.getLogger(DependencyAnalyzerController.class);

    @Autowired
//    @Qualifier(value = "rpcConsumer")
    private DependencyAnalyzerService dependencyAnalyzerService;

    @PostMapping("/parseFile")
    public ResponseEntity<String> parseExcel(@RequestParam("file") MultipartFile file) throws IOException {
        if (file.isEmpty()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("请上传文件");
        }

        try (InputStream inputStream = file.getInputStream()) {
            // 使用 Apache POI 解析 XLSX 文件
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0); // 获取第一个Sheet

            // 遍历每一行
            List<BklightMockData> bklightMockDataList = new LinkedList<>();
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);

                BklightMockData bklightMockData = new BklightMockData();
                Map<String, Cell> cellMap = new HashMap<>();
                for (int columnIndex = 0; columnIndex < row.getPhysicalNumberOfCells(); columnIndex++) {
                    Cell cell = row.getCell(columnIndex);
                    cellMap.put(String.valueOf(columnIndex), cell);
                }
                bklightMockData.setRule_id(getCellValueAsString(cellMap.get("0")));
                bklightMockData.setCase_type( getCellValueAsString(cellMap.get("1")));
                bklightMockData.setCase_id(getCellValueAsString(cellMap.get("2")));
                bklightMockData.setStandard_instance_id(getCellValueAsString(cellMap.get("3")));
                bklightMockData.setUpper_node_id(getCellValueAsString(cellMap.get("4")));
                bklightMockData.setUpper_node_app(getCellValueAsString(cellMap.get("5")));
                bklightMockData.setUpper_node_name(getCellValueAsString(cellMap.get("6")));
                bklightMockData.setDown_node_inject_type(getCellValueAsString(cellMap.get("7")));
                bklightMockData.setGroup_id(getCellValueAsString(cellMap.get("8")));
                bklightMockData.setUpper_node_monitor_rule(getCellValueAsString(cellMap.get("9")));
                bklightMockData.setDown_node_id(getCellValueAsString(cellMap.get("10")));
                bklightMockData.setDown_node_service_name(getCellValueAsString(cellMap.get("11")));
                bklightMockData.setDown_node_app_name(getCellValueAsString(cellMap.get("12")));
                bklightMockData.setDown_inject_rule(getCellValueAsString(cellMap.get("13")));
                bklightMockData.setDown_inject_type_v2(getCellValueAsString(cellMap.get("14")));
                bklightMockData.setDown_error_rule_status(getCellValueAsString(cellMap.get("15")));
                bklightMockData.setDown_rule_hit_instance_id(getCellValueAsString(cellMap.get("16")));
                bklightMockData.setDependency_node_init_param(getCellValueAsString(cellMap.get("17")));
                bklightMockData.setDependency_node_rule_inject_param(getCellValueAsString(cellMap.get("18")));
                bklightMockData.setDependency_node_init_response(getCellValueAsString(cellMap.get("19")));
                bklightMockData.setDependency_node_rule_inject_response( getCellValueAsString(cellMap.get("20")));
                bklightMockData.setDependency_node_init_error_info(getCellValueAsString(cellMap.get("21")));
                bklightMockData.setDependency_node_rule_inject_error_info(getCellValueAsString(cellMap.get("22")));
                bklightMockData.setInstance_exec_context(getCellValueAsString(cellMap.get("23")));
                bklightMockData.setHit_instance_node_response(getCellValueAsString(cellMap.get("24")));
                logger.info("bklightMockData: {}", JSON.toJSONString(bklightMockData));// 打印bklightMockData
                bklightMockDataList.add(bklightMockData);
            }
            //解析并添加用例
            DependencyAnalyzerResult dependencyAnalyzerResult = dependencyAnalyzerService.analysisDependency(bklightMockDataList);
            logger.info("dependencyAnalyzerResult: {}", JSON.toJSONString(dependencyAnalyzerResult));// 打印分析结果
            workbook.close();
            return ResponseEntity.ok("文件解析成功，请查看控制台输出");
        } catch (IOException e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("解析文件失败");
        }
    }

//    @PostMapping("/parseAndAnalysis")
//    public DependencyAnalyzerResult analysis(@RequestParam("file") MultipartFile file) throws IOException {
//        DependencyAnalyzerResult result = new DependencyAnalyzerResult();
//        try {
//            byte[] byteArr = file.getBytes();
//            try (InputStream inputStream = new ByteArrayInputStream(byteArr)) {
//                EasyExcel.read(inputStream, BklightMockData.class,
//                        new PageReadListener<BklightMockData>(dataList -> {
//                                DependencyAnalyzerResult insertRet = dependencyAnalyzerService.analysisDependency(dataList);
//                                result.setSceneId(insertRet.getSceneId());
//                                result.setSceneName(insertRet.getSceneName());
//                                result.setIterateAddress(insertRet.getIterateAddress());
//                                result.setComparisonGroup(insertRet.getComparisonGroup());
//                                result.setTreatmentGroupList(insertRet.getTreatmentGroupList());
//                        })).sheet().doRead();
//            }
//            return result;
//        } catch (Exception e) {
//            logger.info("DCenterBizCaseController.uploadClapLinkExcel: {0}",
//                    JSON.toJSONString(e.getMessage()));
//            return result;
//        }
//}

    // 辅助方法：将单元格数据转为字符串
    private String getCellValueAsString(Cell cell) {
        DataFormatter dataFormatter = new DataFormatter();
        return switch (cell.getCellType()) {
            case STRING -> dataFormatter.formatCellValue(cell).trim();
            case NUMERIC -> String.valueOf(cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            case FORMULA -> cell.getCellFormula();
            default -> "";
        };
    }

}