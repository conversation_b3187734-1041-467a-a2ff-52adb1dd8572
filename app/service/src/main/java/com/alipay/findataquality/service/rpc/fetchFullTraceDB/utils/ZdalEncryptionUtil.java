package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * @ClassName EncryptionUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 13:36
 * @Version V1.0
 **/
public class ZdalEncryptionUtil {
    private static byte[] ENC_KEY_BYTES = "jaas is the way".getBytes();
    private static byte[] ENC_KEY_BYTES_PROD = k().getBytes();

    public String encode(String secret) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        return encode((String)null, secret);
    }

    public static String encode(String encKey, String secret) throws InvalidKeyException, NoSuchAlgorithmException, NoSuchPaddingException, IllegalBlockSizeException, BadPaddingException {
        byte[] kbytes = ENC_KEY_BYTES_PROD;
        if (isNotBlank(encKey)) {
            kbytes = encKey.getBytes();
        }

        try {
            return initEncode(kbytes, secret);
        } catch (InvalidKeyException var4) {
            kbytes = ENC_KEY_BYTES;
        } catch (NoSuchAlgorithmException var5) {
            kbytes = ENC_KEY_BYTES;
        } catch (NoSuchPaddingException var6) {
            kbytes = ENC_KEY_BYTES;
        } catch (IllegalBlockSizeException var7) {
            kbytes = ENC_KEY_BYTES;
        } catch (BadPaddingException var8) {
            kbytes = ENC_KEY_BYTES;
        }

        return initEncode(kbytes, secret);
    }

    static final String initEncode(byte[] kbytes, String secret) throws NoSuchAlgorithmException, NoSuchPaddingException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        SecretKeySpec key = new SecretKeySpec(kbytes, "Blowfish");
        Cipher cipher = Cipher.getInstance("Blowfish");
        cipher.init(1, key);
        byte[] encoding = cipher.doFinal(secret.getBytes());
        BigInteger n = new BigInteger(encoding);
        return n.toString(16);
    }

    public String decode(String secret) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        return decode((String)null, secret);
    }

    public static String decode(String encKey, String secret) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        byte[] kbytes = ENC_KEY_BYTES_PROD;
        if (isNotBlank(encKey)) {
            kbytes = encKey.getBytes();
        }

        try {
            return iniDecode(kbytes, secret);
        } catch (InvalidKeyException var4) {
            kbytes = ENC_KEY_BYTES;
        } catch (BadPaddingException var5) {
            kbytes = ENC_KEY_BYTES;
        } catch (IllegalBlockSizeException var6) {
            kbytes = ENC_KEY_BYTES;
        }

        return iniDecode(kbytes, secret);
    }

    static final String iniDecode(byte[] kbytes, String secret) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        SecretKeySpec key = new SecretKeySpec(kbytes, "Blowfish");
        BigInteger n = new BigInteger(secret, 16);
        byte[] encoding = n.toByteArray();
        if (encoding.length % 8 != 0) {
            int length = encoding.length;
            int newLength = (length / 8 + 1) * 8;
            int pad = newLength - length;
            byte[] old = encoding;
            encoding = new byte[newLength];

            for(int i = old.length - 1; i >= 0; --i) {
                encoding[i + pad] = old[i];
            }
        }

        Cipher cipher = Cipher.getInstance("Blowfish");
        cipher.init(2, key);
        byte[] decode = cipher.doFinal(encoding);
        return new String(decode);
    }

    static final boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    static final boolean isBlank(String str) {
        int strLen;
        if (str != null && (strLen = str.length()) != 0) {
            for(int i = 0; i < strLen; ++i) {
                if (!Character.isWhitespace(str.charAt(i))) {
                    return false;
                }
            }

            return true;
        } else {
            return true;
        }
    }

    public static String k() {
        return (new Object() {
            int t;

            public String toString() {
                byte[] buf = new byte[32];
                this.t = -876293729;
                buf[0] = (byte)(this.t >>> 2);
                this.t = -1464352662;
                buf[1] = (byte)(this.t >>> 23);
                this.t = 518547370;
                buf[2] = (byte)(this.t >>> 4);
                this.t = 710198395;
                buf[3] = (byte)(this.t >>> 12);
                this.t = -624534969;
                buf[4] = (byte)(this.t >>> 22);
                this.t = 674586069;
                buf[5] = (byte)(this.t >>> 16);
                this.t = 1483067432;
                buf[6] = (byte)(this.t >>> 10);
                this.t = 1421328623;
                buf[7] = (byte)(this.t >>> 24);
                this.t = -1066722702;
                buf[8] = (byte)(this.t >>> 11);
                this.t = -342754209;
                buf[9] = (byte)(this.t >>> 14);
                this.t = 267070964;
                buf[10] = (byte)(this.t >>> 13);
                this.t = -2015045304;
                buf[11] = (byte)(this.t >>> 9);
                this.t = -1840166205;
                buf[12] = (byte)(this.t >>> 16);
                this.t = -41626864;
                buf[13] = (byte)(this.t >>> 10);
                this.t = 1576648454;
                buf[14] = (byte)(this.t >>> 11);
                this.t = -1281125290;
                buf[15] = (byte)(this.t >>> 15);
                this.t = 1372627725;
                buf[16] = (byte)(this.t >>> 22);
                this.t = 852701731;
                buf[17] = (byte)(this.t >>> 8);
                this.t = 824646824;
                buf[18] = (byte)(this.t >>> 7);
                this.t = -1642081697;
                buf[19] = (byte)(this.t >>> 22);
                this.t = -1963535242;
                buf[20] = (byte)(this.t >>> 6);
                this.t = -71250299;
                buf[21] = (byte)(this.t >>> 19);
                this.t = -1054824852;
                buf[22] = (byte)(this.t >>> 4);
                this.t = -1596330918;
                buf[23] = (byte)(this.t >>> 5);
                this.t = 567425079;
                buf[24] = (byte)(this.t >>> 14);
                this.t = 250163802;
                buf[25] = (byte)(this.t >>> 13);
                this.t = -1269440909;
                buf[26] = (byte)(this.t >>> 16);
                this.t = -987001676;
                buf[27] = (byte)(this.t >>> 20);
                this.t = -2065416803;
                buf[28] = (byte)(this.t >>> 12);
                this.t = 1166442873;
                buf[29] = (byte)(this.t >>> 12);
                this.t = 1456111703;
                buf[30] = (byte)(this.t >>> 17);
                this.t = -1612376913;
                buf[31] = (byte)(this.t >>> 4);
                return new String(buf);
            }
        }).toString();
    }
}
