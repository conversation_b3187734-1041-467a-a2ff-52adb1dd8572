package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName OrapayTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 18:16
 * @Version V1.0
 **/
public class ObcloudpayTask extends AbstractQueryTask {
    private static final Logger logger = LoggerFactory.getLogger(ObcloudpayTask.class);

    public static final String paycoreCommonSet = "paycore_payment_id_list";

    public ObcloudpayTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        //NOTE: fluxid和paymentid倒数第9、10位：06是obcloudpay，09是obpt_pay，12是ORAPAY
        String paymentIds = this.getQueryContext().getValue("payment_id");
        //倒数9、10位，其中paymentIds格式最后一位为单引号，故向前取倒数11-10位
        String split = StrUtils.substrFromEnd(paymentIds,11,2);

        QueryDBEnum queryDBEnum = null;
        if("06".equals(split)){
            queryDBEnum = QueryDBEnum.OB_CLOUDPAY;
        }else if("12".equals(split)){
            queryDBEnum = QueryDBEnum.ORAPAY;
        }else{
            queryDBEnum = QueryDBEnum.OB_CLOUDPAY;
        }
        logger.info("{}动态DB路由计算，路由标{}，路由结果: {}",this.getClass().getSimpleName(),split,queryDBEnum);
        return queryDBEnum;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                genSql("pmt_pay_ord_${db_flag}","payment_id",paycoreCommonSet,null,new String[]{"biz_no","original_biz_no"}),
                //"select * from pmt_pay_ord_${db_flag} where payment_id in (${payment_id})",
                "select * from pmt_flux_ord_${db_flag} where flux_id in (${payment_id})",
                "select * from pmt_flux_cmd_${db_flag} where flux_id in (${payment_id})",
                "select * from pmt_exec_cmd_${db_flag} where order_id in (${payment_id})",
                "select * from pmt_comm_fd_dtl_${db_flag} where payment_id in (${payment_id})",
                "select * from pmt_comm_rfd_dtl_${db_flag} where refund_id in (${payment_id})"
        };
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAppend("payment_id",paycoreCommonSet);
        context.copyValueAppend("fc_combine_order_list",paycoreCommonSet);
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValueAppend("payment_id","acc_log_order_list");
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        if(index==3){
            //从pmt_exec_cmd中解析泛金融户分表位
            String[]principalList = tableData.getArrayValueList("asset_principal");
            String dbFlag = context.getValue("db_flag");
            Map<String,Boolean> uniqueDbFlagMap = new HashMap<>();
            for (String principalStr: principalList) {
                if(principalStr!=null&&principalStr.length()>=16){
                    String flag = StrUtils.substrFromStart(principalStr,13,2);
                    //同当前flag不同时，写入全局变量
                    if(!flag.equals(dbFlag)){
                        uniqueDbFlagMap.put(flag,true);
                    }
                }
            }
            //循环设定余额内部户取数任务db路由标，从1开始，当时放置3个任务，可支持基金内部户、红包内部户、对手方数据同时出现的情况
            int cnt = 1;
            for (String key: uniqueDbFlagMap.keySet()) {
                context.putGlobal("acc_inner"+cnt+"_db_flag",key);
                context.putGlobal("acc_inner"+cnt+"_db_schema",DbUtils.getDbSchemaFromDbFlag(key));
                cnt++;
            }
        }
    }

    @Override
    public String[] outcomeValue() {
        String[]result = new String[6];
        result[0] = "payment_id";
        result[2] = "result_id";
        return result;
    }
}
