package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;

/**
 * @ClassName InstpayTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/30 18:49
 * @Version V1.0
 **/
public class InstpayCommonTask extends AbstractQueryTask {
    public InstpayCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.MY_INSTPAY;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from ip_withdraw_command_${db_flag} where instpay_id like '${date}%' and (payment_id in (${payment_id}) or out_biz_no in (${instpay_combine_order_list}))",
                "select * from ip_refund_command_${db_flag} where instpay_id like '${date}%' and (payment_id in (${payment_id}) or out_biz_no in (${instpay_combine_order_list}) or biz_no in (${instpay_combine_order_list}))",
                "select * from ip_decrease_command_${db_flag} where instpay_id like '${date}%' and (payment_id in (${payment_id}) or biz_no in (${instpay_combine_order_list}) or cnl_no in (${instpay_combine_order_list}))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                null,
                "ant_ev_ctx.traceID->trace_id",
                "ant_ev_ctx.traceID->trace_id"
        };
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        //instpay的库路由规则，标识位有一位根据分片确定，[0,49]为0，[50,99]为1
        String dbFlag = context.getValue("db_flag");
        String instpaySchema = DbUtils.getInstpaySchemaFromDbFlag(dbFlag);
        context.putGlobal("instpay_db_schema",instpaySchema);
        //放置order_no
        context.copyValue("fc_combine_order_list","instpay_combine_order_list");
    }
}
