package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.fundFlow;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionGroup;

import java.util.List;

/**
 * @ClassName FundFlowBalanceCheck
 * @Description 资金平衡检查
 * <AUTHOR>
 * @Date 2025/3/5 22:06
 * @Version V1.0
 **/
public class FundFlowBalanceCheck {

    /**
     * 全部账户资金总流入=资金总流出
     * @param transactionGroupList
     */
    public void allInEqualsOutCheck(List<TransactionGroup> transactionGroupList){

    }

    /**
     * 转入并冻结场景，转入资金总和<=冻结资金
     * @param transactionGroupList
     */
    public void transInAndFreezeCheck(List<TransactionGroup> transactionGroupList){

    }

    /**
     * 解冻并转出场景，解冻资金总和>=转出资金
     * @param transactionGroupList
     */
    public void unfreezeAndTransOutCheck(List<TransactionGroup> transactionGroupList){

    }

    /**
     * 每个账户总流入=总流出
     * @param transactionGroupList
     */
    public void eachInEqualsOutCheck(List<TransactionGroup> transactionGroupList){

    }
}
