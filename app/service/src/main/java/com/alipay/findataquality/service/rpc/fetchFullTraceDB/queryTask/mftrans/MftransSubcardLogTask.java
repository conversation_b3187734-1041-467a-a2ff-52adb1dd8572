package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName MftransLogTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 15:16
 * @Version V1.0
 **/
public class MftransSubcardLogTask extends AbstractQueryTask {
    public MftransSubcardLogTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.MFTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from mini_account_log_mf_${db_flag} where mini_trans_log_id like '${date}%' and (account_no like '${mini_account_no}%' or account_no like '${from_mini_account_no}%') and (order_no in ('${order_no}','${biz_no}') or cnl_no in ('${order_no}','${biz_no}') or order_no in(${asset_order_id}))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[1];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[1];
    }
}
