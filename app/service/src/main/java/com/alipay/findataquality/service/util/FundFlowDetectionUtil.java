package com.alipay.findataquality.service.util;

import com.alibaba.common.lang.StringUtil;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.accTransData.AcctransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.prodTransData.ProdtransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;
import com.iwallet.biz.common.util.money.Money;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;

public class FundFlowDetectionUtil {

    private static final Logger logger = LoggerFactory.getLogger(FundFlowDetectionUtil.class);

    public static void main(String[] args) {
//        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
//        TransactionStageScene transactionStageScene1 = new TransactionStageScene();
//        transactionStageScene1.setFundFlowStageEnum(FundFlowStageEnum.BANK_TO_YEB);
//        transactionStageScene1.setAmount(new Money(100));
//        transactionStageSceneList.add(transactionStageScene1);
//
//        TransactionStageScene transactionStageScene2 = new TransactionStageScene();
//        transactionStageScene2.setFundFlowStageEnum(FundFlowStageEnum.COUPON_TO_YEB);
//        transactionStageScene2.setAmount(new Money(200));
//        transactionStageSceneList.add(transactionStageScene2);
//        test(transactionStageSceneList);
//
//        for (TransactionStageScene transactionStageScene:transactionStageSceneList){
//            System.out.println(transactionStageScene.toString());
//        }
    }

    /**********************************规则：资金平衡类************************************************/
    /**
     * 应用场景:通用检查，余额宝户账平衡检查
     * 入参：todo：待透出Sub_product_code
     * 出参：todo：待补充
     */
    public static void CommonCheckForYebYueAssetBalance(ProdtransData prodtransData){

    }

    /***********************************************************FundFlowStageEnum.YEB_CKK_TO_YEB的相关检查*************************************/

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的数据准备
     * 入参：FC单据，含1003减、1004减、0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    final static String yebAssetDecreaseOrder1003 = "yeb_asset_decrease_order.1003";
    final static String yebAssetDecreaseOrder1004 = "yeb_asset_decrease_order.1004";
    final static String yebAssetIncreaseOrder0100 = "yeb_asset_increase_order.0100";
    public static Map<String,Object> prepareDataForYebCkkToYebCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取存款卡虚拟主卡1003减需要校验的字段
        Map<String,String> conditionsMapFor1003Dec=new HashMap<>();
        conditionsMapFor1003Dec.put("asset_account_type","VIRTUAL_CKK_FUND_ACCT");
        List<String> dataColumnNameListFor1003Dec = new ArrayList<>();
        dataColumnNameListFor1003Dec.add("clear_dt");
        dataColumnNameListFor1003Dec.add("biz_dt");
        dataColumnNameListFor1003Dec.add("real_amount");
        String tableNameFor1003Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1003Dec,conditionsMapFor1003Dec,dataColumnNameListFor1003Dec);
        prepareDataResult.put(yebAssetDecreaseOrder1003,criteriaCheckDataFor1003Dec);

        //获取存款卡子卡1004减需要校验的字段
        Map<String,String> conditionsMapFor1004Dec=new HashMap<>();
        conditionsMapFor1004Dec.put("asset_account_type","CKK_FUND_ACCT");
        List<String> dataColumnNameListFor1004Dec=new ArrayList<>();
        dataColumnNameListFor1004Dec.add("clear_dt");
        dataColumnNameListFor1004Dec.add("biz_dt");
        dataColumnNameListFor1004Dec.add("real_amount");
        String tableNameFor1004Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>>criteriaCheckDataFor1004Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1004Dec,conditionsMapFor1004Dec,dataColumnNameListFor1004Dec);
        prepareDataResult.put(yebAssetDecreaseOrder1004,criteriaCheckDataFor1004Dec);

        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor1001Inc=new HashMap<>();
        conditionsMapFor1001Inc.put("sub_biz_type","010006");
        conditionsMapFor1001Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor1001Inc.put("inst_id","SHUMIJJ");
        conditionsMapFor1001Inc.put("business_type","OUTER_PURCHASE");
        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("biz_dt");
        dataColumnNameListFor1001Inc.add("real_amount");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Inc = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor1001Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Inc);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的资金检查
     * 检查规则：YEB_CKK_TO_YEB#1003减.real_amount==1004减.real_amount之和
     */
    public static TransactionRuleCheckResult innerBalance1003DecVS1004DecForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        //todo：此处前置到执行流程中
        if (prepareDataResult.isEmpty()){
            logger.error("未捞取到所需检查数据，请检查原数据");
            //todo：抛业务异常
        }

        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1003);
        List<List<CriteriaCheckData>> criteriaCheckDataFor1004Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1004);
        //前置检查
        if (criteriaCheckDataFor1003Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1003指定待检查数据集");
            //todo：抛业务异常
        }
        if (criteriaCheckDataFor1004Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1004指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中
        //执行校验
        Money realAmount_1003dec = new Money();
        CriteriaCheckData criteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "real_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (criteriaCheckData_1003Dec!=null){
            String realAmountStrFor1003Dec = criteriaCheckData_1003Dec.getDataValue();
            realAmount_1003dec.setAmount(new BigDecimal(realAmountStrFor1003Dec).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_1003Dec);
        }

        Money totalRealAmount_1004dec  = new Money();
        for (List<CriteriaCheckData> criteriaCheckDataList : criteriaCheckDataFor1004Dec) {
            CriteriaCheckData criteriaCheckData_1004Dec = criteriaCheckDataList.stream().filter(data -> "real_amount".equals(data.getDataName())).findFirst().get();
            if (criteriaCheckData_1004Dec!=null){
                totalRealAmount_1004dec.add(new Money(new BigDecimal(criteriaCheckData_1004Dec.getDataValue()).multiply(BigDecimal.valueOf(0.01))));

                criteriaCheckDataInfos.add(criteriaCheckData_1004Dec);
            }
        }
        if (realAmount_1003dec.compareTo(totalRealAmount_1004dec) != 0){
            logger.info("yeb_asset_decrease_order.1003.real_amount!= yeb_asset_decrease_order.1004.real_amount之和");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.1003.real_amount!= yeb_asset_decrease_order.1004.real_amount之和");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的资金检查
     * 检查规则：YEB_CKK_TO_YEB#1003减.real_amount==0100增.real_amount
     */
    public static TransactionRuleCheckResult innerBalance1003DecVS0100DecForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1003);
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor1003Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1003指定待检查数据集");
            //todo：抛业务异常
        }
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中
        //执行校验
        Money realAmount_1003dec = new Money();
        CriteriaCheckData criteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "real_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (criteriaCheckData_1003Dec!=null){
            String realAmountStrFor1003Dec = criteriaCheckData_1003Dec.getDataValue();
            realAmount_1003dec.setAmount(new BigDecimal(realAmountStrFor1003Dec).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_1003Dec);
        }

        Money realAmount_0100Inc  = new Money();
        CriteriaCheckData criteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "real_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (criteriaCheckData_0100Inc!=null){
            String realAmountStrFor0100Inc = criteriaCheckData_0100Inc.getDataValue();
            realAmount_0100Inc.setAmount(new BigDecimal(realAmountStrFor0100Inc).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_0100Inc);
        }
        if (realAmount_1003dec.compareTo(realAmount_0100Inc) != 0){
            logger.info("yeb_asset_decrease_order.1003.real_amount!= yeb_asset_increase_order.0100.real_amount");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.1003.real_amount!= yeb_asset_increase_order.0100.real_amount");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的清算日期检查
     * 检查规则：1003减.clear_dt==1003减.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1003DecSingleForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1003);
        //前置检查
        if (criteriaCheckDataFor1003Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1003指定待检查数据集");
            //todo：抛业务异常
        }
        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中
        //执行校验
        String bizDt_1003Dec = "";
        CriteriaCheckData bizDtCriteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (bizDtCriteriaCheckData_1003Dec!=null){
            bizDt_1003Dec = bizDtCriteriaCheckData_1003Dec.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_1003Dec);
        }

        String clearDt_1003Dec = "";
        CriteriaCheckData clearDtCriteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (clearDtCriteriaCheckData_1003Dec!=null){
            clearDt_1003Dec = clearDtCriteriaCheckData_1003Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_1003Dec);
        }

        if (StringUtil.isEmpty(bizDt_1003Dec) || StringUtil.isEmpty(clearDt_1003Dec) ||
                !(clearDt_1003Dec.equals(bizDt_1003Dec))){ //1003减.clear_dt!=1003减.biz_dt
            logger.info("yeb_asset_decrease_order.1003.clear_dt!= yeb_asset_decrease_order.1003.biz_dt");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.1003.clear_dt!= yeb_asset_decrease_order.1003.biz_dt");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的清算日期检查
     * 检查规则：1003减.clear_dt==1004所有减.clear_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1003DecVS1004DecForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){

        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1003);
        List<List<CriteriaCheckData>> criteriaCheckDataFor1004Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1004);
        //前置检查
        if (criteriaCheckDataFor1003Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1003指定待检查数据集");
            //todo：抛业务异常
        }
        if (criteriaCheckDataFor1004Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1004指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_1003Dec;
        CriteriaCheckData clearDtCriteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (clearDtCriteriaCheckData_1003Dec!=null){
            clearDt_1003Dec = clearDtCriteriaCheckData_1003Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_1003Dec);
        } else {
            clearDt_1003Dec = "";
        }

        List<String> clearDt_1004DecList = new ArrayList<>();
        for (List<CriteriaCheckData> criteriaCheckDataList : criteriaCheckDataFor1004Dec) {
            CriteriaCheckData criteriaCheckData_1004Dec = criteriaCheckDataList.stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();
            if (criteriaCheckData_1004Dec!=null){
                clearDt_1004DecList.add(criteriaCheckData_1004Dec.getDataValue());

                criteriaCheckDataInfos.add(criteriaCheckData_1004Dec);
            }
        }
        boolean isAllMathched = clearDt_1004DecList.stream()
                .allMatch(clearDt_1004Dec -> clearDt_1004Dec.equals(clearDt_1003Dec));
        if (!isAllMathched){
            logger.info("yeb_asset_decrease_order.1003.clear_dt != yeb_asset_decrease_order.1004.clear_dt不全相等");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.1003.clear_dt != yeb_asset_decrease_order.1004.clear_dt不全相等");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的清算日期检查
     * 检查规则：1003减.clear_dt==0100增.clear_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1003DecVS0100IncForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){

        List<List<CriteriaCheckData>> criteriaCheckDataFor1003Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder1003);
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor1003Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.1003指定待检查数据集");
            //todo：抛业务异常
        }
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_1003Dec;
        CriteriaCheckData clearDtCriteriaCheckData_1003Dec = criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor1003Dec的size=1
        if (clearDtCriteriaCheckData_1003Dec!=null){
            clearDt_1003Dec = clearDtCriteriaCheckData_1003Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_1003Dec);
        } else {
            clearDt_1003Dec = "";
        }

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc= criteriaCheckDataFor1003Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        if (StringUtil.isEmpty(clearDt_1003Dec) || StringUtil.isEmpty(clearDt_0100Inc) ||
                !(clearDt_0100Inc.equals(clearDt_1003Dec))){ //0100增.clear_dt!=1003减.clear_dt
            logger.info("yeb_asset_increase_order.0100.clear_dt!= yeb_asset_decrease_order.1003.clear_dt");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_increase_order.0100.clear_dt!= yeb_asset_decrease_order.1003.clear_dt");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB，含存款卡场景的清算日期检查
     * 检查规则：0100增.clear_dt==0100增.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck0100IncSingleForYebCkkToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc= criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        String bizDt_0100Inc;
        CriteriaCheckData bizDtCriteriaCheckData_0100Inc= criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            bizDt_0100Inc = bizDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Inc);
        } else {
            bizDt_0100Inc = "";
        }

        if (StringUtil.isEmpty(bizDt_0100Inc) || StringUtil.isEmpty(clearDt_0100Inc) ||
                !(clearDt_0100Inc.equals(bizDt_0100Inc))){ //0100增.clear_dt!=0100增.biz_dt
            logger.info("yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_CKK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /*******************************************************************FundFlowStageEnum.YEB_TO_FUND_DUMMY的相关检查*************************************/
    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY的相关检查，含存款卡场景的数据准备
     * 入参：FC单据，含0100减、0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    final static String yebAssetDecreaseOrder0100 = "yeb_asset_decrease_order.0100";
    public static Map<String,Object> prepareDataForYebToFundDummyCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100减需要校验的字段
        Map<String,String> conditionsMapFor0100Dec=new HashMap<>();
//        conditionsMapFor0100Dec.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\"");  todo：增加三级业务表示校验的优化，三级业务标识：fncpay20004||REDEEM|DISTRIBUTOR
        conditionsMapFor0100Dec.put("sub_biz_type","021001");
        conditionsMapFor0100Dec.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Dec.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Dec.put("business_type","T0_LOAN");
        List<String> dataColumnNameListFor1001Dec=new ArrayList<>();
        dataColumnNameListFor1001Dec.add("clear_dt");
        dataColumnNameListFor1001Dec.add("biz_dt");
        dataColumnNameListFor1001Dec.add("ext_info");
        String tableNameFor1001Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Dec,conditionsMapFor0100Dec,dataColumnNameListFor1001Dec);
        prepareDataResult.put(yebAssetDecreaseOrder0100,criteriaCheckDataFor1001Dec);

        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor1001Inc=new HashMap<>();
        conditionsMapFor1001Inc.put("sub_biz_type","010006");
        conditionsMapFor1001Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor1001Inc.put("inst_id","SHUMIJJ");
        conditionsMapFor1001Inc.put("business_type","OUTER_PURCHASE");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Inc = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor1001Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Inc);

        return prepareDataResult;
    }

    /**
     * todo:待废弃
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY，含余额宝申购基金的清算日期检查
     * 检查规则：0100减.clear_dt==0100增.clear_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecVS0100IncForYebToFundDummyCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }
        TransactionRuleCheckResult transactionRuleCheckResult = null;
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.info("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：此处不抛异常
        }else {
            List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

            String clearDt_0100Dec;
            CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
            if (clearDtCriteriaCheckData_0100Dec != null) {
                clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
                criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
            } else {
                clearDt_0100Dec = "";
            }

            String clearDt_0100Inc;
            CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
            if (clearDtCriteriaCheckData_0100Inc != null) {
                clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
                criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
            } else {
                clearDt_0100Inc = "";
            }

            if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(clearDt_0100Inc) ||
                    !(clearDt_0100Dec.equals(clearDt_0100Inc))) { //0100减.clear_dt!=0100增.clear_dt
                logger.info("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_increase_order.0100.clear_dt");
                //todo：抛业务异常
            }

            //todo：组装result优化
            transactionRuleCheckResult = new TransactionRuleCheckResult();
            transactionRuleCheckResult.setRule("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_increase_order.0100.clear_dt");//规则检测描述
            transactionRuleCheckResult.setRuleCheckResult(false);
            transactionRuleCheckResult.setRuleCheckResultType("error");
            transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY);//todo：外部传进来
            transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);
        }

        return transactionRuleCheckResult;
    }


    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY，含余额宝申购基金的清算日期检查
     * 检查规则：0100减.clear_dt==0100减.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecSingleForYebToFundDummyCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Dec;
        CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Dec!=null){
            clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
        } else {
            clearDt_0100Dec = "";
        }

        String bizDt_0100Dec;
        CriteriaCheckData bizDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Dec!=null){
            bizDt_0100Dec = bizDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Dec);
        } else {
            bizDt_0100Dec = "";
        }

        if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(bizDt_0100Dec) ||
                !(clearDt_0100Dec.equals(bizDt_0100Dec))){ //0100减.clear_dt!=0100减.biz_dt
            logger.info("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.biz_dt");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.biz_dt");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY，含余额宝申购基金的拓展单标识检查
     * 检查规则：拓展字段不含"yebSaleByShumi":"T" && 不含:"fcHighAvailable":"T"&& 不含isFundPay":"Y"&& 不含hasYebPay=Y
     */
    public static TransactionRuleCheckResult identifierCheck1001DecSingleForYebToFundDummyCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String extInfo_0100Dec;
        CriteriaCheckData extInfoCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "ext_info".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (extInfoCriteriaCheckData_0100Dec!=null){
            extInfo_0100Dec = extInfoCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(extInfoCriteriaCheckData_0100Dec);
        } else {
            extInfo_0100Dec = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"fcHighAvailable\":\"T\"")){ //0100减拓展字段不应"fcHighAvailable":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"fcHighAvailable\":\"T\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"yebSaleByShumi\":\"T\"")){ //0100减拓展字段不应包含"yebSaleByShumi":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"yebSaleByShumi\":\"T\"字段";
            logger.info(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"hasYebPay\":\"Y\"")){ //0100减拓展字段不应包含"hasYebPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"hasYebPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"isFundPay\":\"Y\"")){ //0100减拓展字段不应包含"isFundPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"isFundPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /***********************************************************FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ的相关检查*************************************/

    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ，含存款卡场景的数据准备
     * 入参：FC单据，含0100减、0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForYebToFundDummyHqCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100减需要校验的字段
        Map<String,String> conditionsMapFor0100Dec=new HashMap<>();
//        conditionsMapFor0100Dec.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20004||REDEEM|DISTRIBUTOR
        conditionsMapFor0100Dec.put("sub_biz_type","021001");
        conditionsMapFor0100Dec.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Dec.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Dec.put("business_type","T0_LOAN");
        List<String> dataColumnNameListFor1001Dec=new ArrayList<>();
        dataColumnNameListFor1001Dec.add("clear_dt");
        dataColumnNameListFor1001Dec.add("biz_dt");
        dataColumnNameListFor1001Dec.add("ext_info");
        String tableNameFor1001Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Dec,conditionsMapFor0100Dec,dataColumnNameListFor1001Dec);
        prepareDataResult.put(yebAssetDecreaseOrder0100,criteriaCheckDataFor1001Dec);

        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor1001Inc=new HashMap<>();
        conditionsMapFor1001Inc.put("sub_biz_type","010006");
        conditionsMapFor1001Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor1001Inc.put("inst_id","SHUMIJJ");
        conditionsMapFor1001Inc.put("business_type","OUTER_PURCHASE");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Inc = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor1001Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Inc);

        return prepareDataResult;
    }

    /**
     * todo:待废弃
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保非基金支付链路)的清算日期检查
     * 检查规则：0100减.clear_dt==0100增.clear_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecVS0100IncForYebToFundDummyHqCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }
        TransactionRuleCheckResult transactionRuleCheckResult = null;
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.info("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：此处不抛异常
        }else {
            List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

            String clearDt_0100Dec;
            CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
            if (clearDtCriteriaCheckData_0100Dec != null) {
                clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
                criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
            } else {
                clearDt_0100Dec = "";
            }

            String clearDt_0100Inc;
            CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
            if (clearDtCriteriaCheckData_0100Inc != null) {
                clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
                criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
            } else {
                clearDt_0100Inc = "";
            }

            if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(clearDt_0100Inc) ||
                    !(clearDt_0100Dec.equals(clearDt_0100Inc))) { //0100减.clear_dt!=0100增.clear_dt
                logger.info("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_increase_order.0100.clear_dt");
                //todo：抛业务异常
            }

            //todo：组装result优化
            transactionRuleCheckResult = new TransactionRuleCheckResult();
            transactionRuleCheckResult.setRule("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_increase_order.0100.clear_dt");//规则检测描述
            transactionRuleCheckResult.setRuleCheckResult(false);
            transactionRuleCheckResult.setRuleCheckResultType("error");
            transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ);//todo：外部传进来
            transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);
        }

        return transactionRuleCheckResult;
    }


    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保非基金支付链路)的清算日期检查
     * 检查规则：0100减.clear_dt==0100减.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecSingleForYebToFundDummyHqCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Dec;
        CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Dec!=null){
            clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
        } else {
            clearDt_0100Dec = "";
        }

        String bizDt_0100Dec;
        CriteriaCheckData bizDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Dec!=null){
            bizDt_0100Dec = bizDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Dec);
        } else {
            bizDt_0100Dec = "";
        }

        if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(bizDt_0100Dec) ||
                !(clearDt_0100Dec.equals(bizDt_0100Dec))){ //0100减.clear_dt!=0100减.biz_dt
            logger.info("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.biz_dt");
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule("yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.biz_dt");//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保非基金支付链路)的拓展单标识检查
     * 检查规则：拓展字段（包含"fcHighAvailable":"T" && hasYebPay=Y) && (不含"yebSaleByShumi":"T" && 不含isFundPay":"Y")
     */
    public static TransactionRuleCheckResult identifierCheck1001DecSingleForYebToFundDummyHqCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String extInfo_0100Dec;
        CriteriaCheckData extInfoCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "ext_info".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (extInfoCriteriaCheckData_0100Dec!=null){
            extInfo_0100Dec = extInfoCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(extInfoCriteriaCheckData_0100Dec);
        } else {
            extInfo_0100Dec = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                (!extInfo_0100Dec.contains("\"fcHighAvailable\":\"T\""))){ //0100减拓展字段没包含"fcHighAvailable":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info应包含\"fcHighAvailable\":\"T\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                (!extInfo_0100Dec.contains("\"hasYebPay\":\"Y\""))){ //0100减拓展字段没包含"hasYebPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info应包含\"hasYebPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"yebSaleByShumi\":\"T\"")){ //0100减拓展字段不应包含"yebSaleByShumi":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"yebSaleByShumi\":\"T\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"isFundPay\":\"Y\"")){ //0100减拓展字段不应包含"isFundPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"isFundPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /***********************************************************FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ的相关检查*************************************/

    /**
     * 应用场景：FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保基金支付链路)场景的数据准备
     * 入参：FC单据，含0100减
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    final static String iwAccountLogYueInc = "iw_account_log_yue_inc";//余额增
    final static String iwAccountLogYueDec = "iw_account_log_yue_dec";//余额减
    public static Map<String,Object> prepareDataForYebYueToFundDummyHqCheck(AcctransData acctransData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = acctransData.getDataList();
        //获取余额宝主卡0100减需要校验的字段
        Map<String,String> conditionsMapFor0100Dec=new HashMap<>();
//        conditionsMapFor0100Dec.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20102|||PAY
        conditionsMapFor0100Dec.put("sub_biz_type","050001");
        conditionsMapFor0100Dec.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Dec.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Dec.put("business_type","T0_LOAN");
        List<String> dataColumnNameListFor1001Dec=new ArrayList<>();
        dataColumnNameListFor1001Dec.add("clear_dt");
        dataColumnNameListFor1001Dec.add("gmt_commit");
        dataColumnNameListFor1001Dec.add("real_amount");
        dataColumnNameListFor1001Dec.add("ext_info");
        String tableNameFor1001Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Dec,conditionsMapFor0100Dec,dataColumnNameListFor1001Dec);
        prepareDataResult.put(yebAssetDecreaseOrder0100,criteriaCheckDataFor1001Dec);

        //获取余额增需要校验的字段
        Map<String,String> conditionsMapForYueInc=new HashMap<>();
        conditionsMapForYueInc.put("trans_code","3017");
        conditionsMapForYueInc.put("sub_trans_code","301706");
        conditionsMapForYueInc.put("asset_code","ALIPAYACCOUNT");
        conditionsMapForYueInc.put("biz_pd_code","********************");
        conditionsMapForYueInc.put("biz_ev_code","********");
        List<String> dataColumnNameListForYueInc=new ArrayList<>();
        dataColumnNameListForYueInc.add("trans_amount");
        String tableNameForYueInc="iw_account_log";
        List<List<CriteriaCheckData>> criteriaCheckDataForYueInc = fetchDataIndexByConditions(tableDataList,tableNameForYueInc,conditionsMapForYueInc,dataColumnNameListForYueInc);
        prepareDataResult.put(iwAccountLogYueInc,criteriaCheckDataForYueInc);

        //获取余额减需要校验的字段
        Map<String,String> conditionsMapForYueDec=new HashMap<>();
        conditionsMapForYueDec.put("trans_code","3018");
        conditionsMapForYueDec.put("sub_trans_code","301866");
        conditionsMapForYueDec.put("asset_code","ALIPAYACCOUNT");
        conditionsMapForYueDec.put("biz_pd_code","********************");
        conditionsMapForYueDec.put("biz_ev_code","********");
        List<String> dataColumnNameListForYueDec=new ArrayList<>();
        dataColumnNameListForYueInc.add("trans_amount");
        String tableNameForYueDec="iw_account_log";
        List<List<CriteriaCheckData>> criteriaCheckDataForYueDec = fetchDataIndexByConditions(tableDataList,tableNameForYueDec,conditionsMapForYueDec,dataColumnNameListForYueDec);
        prepareDataResult.put(iwAccountLogYueDec,criteriaCheckDataForYueDec);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保非基金支付链路)场景的资金检查
     * 检查规则：fc.0100减.real_amount=余额增.trans_amount
     */
    public static TransactionRuleCheckResult yueBalanceForYebYueToFundDummyHqCheck(AcctransData acctransData, Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        List<List<CriteriaCheckData>> criteriaCheckDataForYueInc = (List<List<CriteriaCheckData>>) prepareDataResult.get(iwAccountLogYueInc);
        List<List<CriteriaCheckData>> criteriaCheckDataForYueDec = (List<List<CriteriaCheckData>>) prepareDataResult.get(iwAccountLogYueDec);

        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }
        if (criteriaCheckDataForYueInc.isEmpty() || criteriaCheckDataForYueDec.isEmpty()){
            logger.error("未捞取到所需检查数据：iw_account_log_yue.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中
        //执行校验
        Money realAmount_0100Dec = new Money();
        CriteriaCheckData criteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "real_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (criteriaCheckData_0100Dec!=null){
            String realAmountStrFor0100Dec = criteriaCheckData_0100Dec.getDataValue();
            realAmount_0100Dec.setAmount(new BigDecimal(realAmountStrFor0100Dec).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_0100Dec);
        }

        Money transAmount_YueInc  = new Money();
        CriteriaCheckData criteriaCheckData_YueInc = criteriaCheckDataForYueInc.get(0).stream().filter(data -> "trans_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataForYueInc的size=1
        if (criteriaCheckData_YueInc!=null){
            String realAmountStrForYueInc = criteriaCheckData_YueInc.getDataValue();
            transAmount_YueInc.setAmount(new BigDecimal(realAmountStrForYueInc).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_YueInc);
        }

        String errorRule = "";
        //具体规则：yeb_asset_decrease_order.0100.real_amount == iw_account_log.余额增.trans_amount
        if (realAmount_0100Dec.compareTo(transAmount_YueInc) != 0){
            errorRule = "yeb_asset_decrease_order.0100.real_amount!= iw_account_log.余额增.trans_amount";
            logger.info(errorRule);
            //todo：抛业务异常
        }


        Money transAmount_YueDec  = new Money();
        CriteriaCheckData criteriaCheckData_YueDec = criteriaCheckDataForYueDec.get(0).stream().filter(data -> "trans_amount".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataForYueInc的size=1
        if (criteriaCheckData_YueDec!=null){
            String realAmountStrForYueDec = criteriaCheckData_YueDec.getDataValue();
            transAmount_YueDec.setAmount(new BigDecimal(realAmountStrForYueDec).multiply(BigDecimal.valueOf(0.01)));

            criteriaCheckDataInfos.add(criteriaCheckData_YueDec);
        }

        //具体规则：iw_account_log.余额增.trans_amount == iw_account_log.余额减.trans_amount
        if (transAmount_YueInc.compareTo(transAmount_YueDec) != 0){
            errorRule = "iw_account_log.余额增.trans_amount == iw_account_log.余额减.trans_amount";
            logger.info(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /**
     * 应用场景：FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保基金支付链路)的清算日期检查
     * 检查规则：0100减.clear_dt==0100减.gmt_commit
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecSingleForYebYueToFundDummyHqCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Dec;
        CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Dec!=null){
            clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
        } else {
            clearDt_0100Dec = "";
        }

        String gmtCommit_0100Dec;
        CriteriaCheckData gmtCommitCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "gmt_commit".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (gmtCommitCriteriaCheckData_0100Dec!=null){
            gmtCommit_0100Dec = gmtCommitCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(gmtCommitCriteriaCheckData_0100Dec);
        } else {
            gmtCommit_0100Dec = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(gmtCommit_0100Dec) ||
                !(clearDt_0100Dec.equals(gmtCommit_0100Dec))){ //0100减.clear_dt!=0100减.biz_dt
            erroRule = "yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.gmt_commit";
            logger.info(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ，含余额宝申购基金(高保基金支付链路)的拓展单标识检查
     * 检查规则：拓展字段（包含"fcHighAvailable":"T" && hasYebPay=Y) && (不含"yebSaleByShumi":"T" && 不含isFundPay":"Y")
     */
    public static TransactionRuleCheckResult identifierCheck1001DecSingleForYebYueToFundDummyHqCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String extInfo_0100Dec;
        CriteriaCheckData extInfoCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "ext_info".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (extInfoCriteriaCheckData_0100Dec!=null){
            extInfo_0100Dec = extInfoCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(extInfoCriteriaCheckData_0100Dec);
        } else {
            extInfo_0100Dec = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                (!extInfo_0100Dec.contains("\"fcHighAvailable\":\"T\""))){ //0100减拓展字段应包含"fcHighAvailable":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info应包含\"fcHighAvailable\":\"T\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                (!extInfo_0100Dec.contains("\"isFundPay\":\"Y\""))){ //0100减拓展字段应包含"isFundPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不应包含\"isFundPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                (!extInfo_0100Dec.contains("\"yebSaleByShumi\":\"T\""))){ //0100减拓展字段应包含"yebSaleByShumi":"T"
            erroRule = "yeb_asset_decrease_order.0100.ext_info应包含\"yebSaleByShumi\":\"T\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(extInfo_0100Dec) ||
                extInfo_0100Dec.contains("\"hasYebPay\":\"Y\"")){ //0100减拓展字段不需要包含"hasYebPay":"Y"
            erroRule = "yeb_asset_decrease_order.0100.ext_info不需要包含\"hasYebPay\":\"Y\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_YUE_TO_FUND_DUMMY_HQ);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /***********************************************************FundFlowStageEnum.COUPON_TO_YEB的相关检查*************************************/

    /**
     * 应用场景：FundFlowStageEnum.COUPON_TO_YEB，含"红包奖励发放到余额宝"场景的数据准备
     * 入参：FC单据，含0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForCouponToYebCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Inc=new HashMap<>();
//        conditionsMapFor0100Inc.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20003|FP_SENIOR|PAY|FCTRANSFER
        conditionsMapFor0100Inc.put("sub_biz_type","060001");
        conditionsMapFor0100Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Inc.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Inc.put("business_type","T1_CHANGE");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("pmt_dt");
        dataColumnNameListFor1001Inc.add("biz_dt");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor0100Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.COUPON_TO_YEB，含"红包奖励发放到余额宝"场景的清算日期检查
     * 检查规则：0100增.clear_dt==0100增.pmt_dt==0100增.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001IncForCouponToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        String bizDt_0100Inc;
        CriteriaCheckData bizDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Inc!=null){
            bizDt_0100Inc = bizDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Inc);
        } else {
            bizDt_0100Inc = "";
        }

        String pmtDt_0100Inc;
        CriteriaCheckData pmtDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "pmt_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (pmtDtCriteriaCheckData_0100Inc!=null){
            pmtDt_0100Inc = pmtDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(pmtDtCriteriaCheckData_0100Inc);
        } else {
            pmtDt_0100Inc = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Inc) || StringUtil.isEmpty(bizDt_0100Inc) ||
            !(clearDt_0100Inc.equals(bizDt_0100Inc))){ //0100增.clear_dt!=0100增.biz_dt
            errorRule = "yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(pmtDt_0100Inc)  || (!bizDt_0100Inc.equals(pmtDt_0100Inc))){
            errorRule = "yeb_asset_increase_order.0100.biz_dt!= yeb_asset_increase_order.0100.pmt_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.COUPON_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /***********************************************************FundFlowStageEnum.BANK_TO_YEB的相关检查*************************************/

    /**
     * 应用场景：FundFlowStageEnum.BANK_TO_YEB，含"银行卡申购余额宝"场景的数据准备
     * 入参：FC单据，含0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForBankToYebCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Inc=new HashMap<>();
//        conditionsMapFor0100Inc.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20003|FP_SENIOR|PAY|FP_SENIOR
        conditionsMapFor0100Inc.put("sub_biz_type","010001");
        conditionsMapFor0100Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Inc.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Inc.put("business_type","FINANCE_PURCHASE");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("pmt_dt");
        dataColumnNameListFor1001Inc.add("biz_dt");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor0100Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.COUPON_TO_YEB，含"红包奖励发放到余额宝"场景的清算日期检查
     * 检查规则：0100增.clear_dt==0100增.pmt_dt==0100增.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001IncForBankToYebCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        String bizDt_0100Inc;
        CriteriaCheckData bizDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Inc!=null){
            bizDt_0100Inc = bizDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Inc);
        } else {
            bizDt_0100Inc = "";
        }

        String pmtDt_0100Inc;
        CriteriaCheckData pmtDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "pmt_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (pmtDtCriteriaCheckData_0100Inc!=null){
            pmtDt_0100Inc = pmtDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(pmtDtCriteriaCheckData_0100Inc);
        } else {
            pmtDt_0100Inc = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Inc) || StringUtil.isEmpty(bizDt_0100Inc) ||
                !(clearDt_0100Inc.equals(bizDt_0100Inc))){ //0100增.clear_dt!=0100增.biz_dt
            errorRule = "yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }
        if (StringUtil.isEmpty(pmtDt_0100Inc)  || (!bizDt_0100Inc.equals(pmtDt_0100Inc))){
            errorRule = "yeb_asset_increase_order.0100.biz_dt!= yeb_asset_increase_order.0100.pmt_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.BANK_TO_YEB);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /***********************************************************FundFlowStageEnum.YEB_TO_COUPON的相关检查*************************************/
    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_COUPON，含"余额宝红包奖励发放退回"场景的数据准备
     * 入参：FC单据，含0100减
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForYebToCouponCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Dec=new HashMap<>();
//        conditionsMapFor0100Dec.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20003|FP_SENIOR|REFUND|FCTRANSFER
        conditionsMapFor0100Dec.put("sub_biz_type","061001");
        conditionsMapFor0100Dec.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Dec.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Dec.put("business_type","T1_CHANGE");

        List<String> dataColumnNameListFor1001Dec=new ArrayList<>();
        dataColumnNameListFor1001Dec.add("clear_dt");
        dataColumnNameListFor1001Dec.add("pmt_dt");
        String tableNameFor1001Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Dec,conditionsMapFor0100Dec,dataColumnNameListFor1001Dec);
        prepareDataResult.put(yebAssetDecreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_COUPON，含"余额宝红包奖励发放退回"场景的清算日期检查
     * 检查规则：0100减.clear_dt==0100减.pmt_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecForYebToCouponCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Dec;
        CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Dec!=null){
            clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
        } else {
            clearDt_0100Dec = "";
        }

        String pmtDt_0100Dec;
        CriteriaCheckData pmtDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "pmt_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (pmtDtCriteriaCheckData_0100Dec!=null){
            pmtDt_0100Dec = pmtDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(pmtDtCriteriaCheckData_0100Dec);
        } else {
            pmtDt_0100Dec = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(pmtDt_0100Dec) ||
                !(clearDt_0100Dec.equals(pmtDt_0100Dec))){ //0100减.clear_dt!=0100减.pmt_dt
            errorRule = "yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.pmt_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_COUPON);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /***********************************************************FundFlowStageEnum.YEB_TO_BANK的相关检查*************************************/
    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_BANK，含"余额宝退回到银行卡"场景的数据准备
     * 入参：FC单据，含0100减
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForYebToBankCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Dec=new HashMap<>();
//        conditionsMapFor0100Dec.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20003|FP_SENIOR|REFUND|FP_SENIOR
        conditionsMapFor0100Dec.put("sub_biz_type","059001");
        conditionsMapFor0100Dec.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Dec.put("inst_id","SHUMIJJ");
        conditionsMapFor0100Dec.put("business_type","T1_CHANGE");

        List<String> dataColumnNameListFor1001Dec=new ArrayList<>();
        dataColumnNameListFor1001Dec.add("clear_dt");
        dataColumnNameListFor1001Dec.add("pmt_dt");
        String tableNameFor1001Dec="yeb_asset_decrease_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Dec,conditionsMapFor0100Dec,dataColumnNameListFor1001Dec);
        prepareDataResult.put(yebAssetDecreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;
    }

    /**
     * 应用场景：FundFlowStageEnum.YEB_TO_BANK，含"余额宝退回到银行卡"场景的清算日期检查
     * 检查规则：0100减.clear_dt==0100减.pmt_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecForYebToBankCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Dec = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetDecreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Dec.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_decrease_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Dec;
        CriteriaCheckData clearDtCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Dec!=null){
            clearDt_0100Dec = clearDtCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Dec);
        } else {
            clearDt_0100Dec = "";
        }

        String gmtCommit_0100Dec;
        CriteriaCheckData gmtCommitCriteriaCheckData_0100Dec = criteriaCheckDataFor0100Dec.get(0).stream().filter(data -> "gmt_commit".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (gmtCommitCriteriaCheckData_0100Dec!=null){
            gmtCommit_0100Dec = gmtCommitCriteriaCheckData_0100Dec.getDataValue();
            criteriaCheckDataInfos.add(gmtCommitCriteriaCheckData_0100Dec);
        } else {
            gmtCommit_0100Dec = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Dec) || StringUtil.isEmpty(gmtCommit_0100Dec) ||
                !(clearDt_0100Dec.equals(gmtCommit_0100Dec))){ //0100减.clear_dt!=0100减.gmt_commit
            errorRule = "yeb_asset_decrease_order.0100.clear_dt!= yeb_asset_decrease_order.0100.gmt_commit";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_BANK);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /****************************FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL*************/
    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL，含"基金撤单后资金退回余额宝"场景的数据准备
     * 入参：FC单据，含0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForFundDummyToYebCancelCheck(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Inc=new HashMap<>();
//        conditionsMapFor0100Inc.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20004||REVOKEPAY|DISTRIBUTOR
        conditionsMapFor0100Inc.put("sub_biz_type","018001");
        conditionsMapFor0100Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Inc.put("inst_id","SHUMIJJ");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("biz_dt");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor0100Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;

    }

    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL，含"基金撤单后资金退回余额宝"场景的清算日期检查
     * 检查规则：0100增.clear_dt==0100增.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001IncForFundDummyToYebCancelCheck(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Inc的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        String bizDt_0100Inc;
        CriteriaCheckData bizDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Inc!=null){
            bizDt_0100Inc = bizDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Inc);
        } else {
            bizDt_0100Inc = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Inc) || StringUtil.isEmpty(bizDt_0100Inc) ||
                !(clearDt_0100Inc.equals(bizDt_0100Inc))){ //0100增.clear_dt!=0100增.biz_dt
            errorRule = "yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /****************************FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1 && FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1*************/
    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1，含"基金全部退款后资金退回余额宝(03T1)"场景的数据准备
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1，含"基金部分退款后资金退回余额宝(03T1)"场景的数据准备
     * 入参：FC单据，含0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForYebRefund03T1Check(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Inc=new HashMap<>();
//        conditionsMapFor0100Inc.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20004||BATCHPAY|DISTRIBUTOR|100
        conditionsMapFor0100Inc.put("sub_biz_type","016101");
        conditionsMapFor0100Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Inc.put("inst_id","SHUMIJJ");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("ext_info");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor0100Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;

    }

    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1，含"基金全部退款后资金退回余额宝(03T1)"场景的清算日期检查
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1，含"基金部分退款后资金退回余额宝(03T1)"场景的清算日期检查
     * 检查规则：0100增.clear_dt==“14:59:59”
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecForYebRefund03T1Check(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Inc) || !(clearDt_0100Inc.contains("14:59:59"))){ //0100增.clear_dt!=“xxx 14:59:59”
            errorRule = "yeb_asset_increase_order.0100.clear_dt!= 'xxx 14:59:59'";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1，含"基金全部退款后资金退回余额宝(03T1)"场景的清算日期检查
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1，含"基金部分退款后资金退回余额宝(03T1)"场景的清算日期检查
     * 检查规则：拓展字段包含“\"BIZ_ACTION_TYPE\":\"BATCHPAY\"
     */
    public static TransactionRuleCheckResult identifierCheck1001IncForYebRefund03T1Check(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String extInfo_0100Inc;
        CriteriaCheckData extInfoCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "ext_info".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (extInfoCriteriaCheckData_0100Inc!=null){
            extInfo_0100Inc = extInfoCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(extInfoCriteriaCheckData_0100Inc);
        } else {
            extInfo_0100Inc = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(extInfo_0100Inc) ||
                (!extInfo_0100Inc.contains("\"BIZ_ACTION_TYPE\":\"BATCHPAY\""))){ //0100减拓展字段应包含"BIZ_ACTION_TYPE":"BATCHPAY"
            erroRule = "yeb_asset_increase_order.0100.ext_info应包含\"BIZ_ACTION_TYPE\":\"BATCHPAY\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /****************************FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2 && FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2*************/
    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2，含"基金全部退款后资金退回余额宝(03T2)"场景的数据准备
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2，含"基金部分退款后资金退回余额宝(03T2)"场景的数据准备
     * 入参：FC单据，含0100增
     * todo：整合进checker完成数据捞取
     * @param financingcoreData
     * @return
     */
    public static Map<String,Object> prepareDataForYebRefund03T2Check(FinancingcoreData financingcoreData){
        Map<String,Object> prepareDataResult = new HashMap<>();
        List<TableData> tableDataList = financingcoreData.getDataList();
        //获取余额宝主卡0100增需要校验的字段
        Map<String,String> conditionsMapFor0100Inc=new HashMap<>();
//        conditionsMapFor0100Inc.put("ext_info#1","\"BIZ_PROD\": \"fncpay20004\""); todo：增加三级业务表示校验的优化，fncpay20004||BATCHPAY|DISTRIBUTOR|100
        conditionsMapFor0100Inc.put("sub_biz_type","016101");//todo：待确认
        conditionsMapFor0100Inc.put("asset_account_type","fundpay_share");
        conditionsMapFor0100Inc.put("inst_id","SHUMIJJ");

        List<String> dataColumnNameListFor1001Inc=new ArrayList<>();
        dataColumnNameListFor1001Inc.add("clear_dt");
        dataColumnNameListFor1001Inc.add("ext_info");
        String tableNameFor1001Inc="yeb_asset_increase_order";
        List<List<CriteriaCheckData>> criteriaCheckDataFor1001Dec = fetchDataIndexByConditions(tableDataList,tableNameFor1001Inc,conditionsMapFor0100Inc,dataColumnNameListFor1001Inc);
        prepareDataResult.put(yebAssetIncreaseOrder0100,criteriaCheckDataFor1001Dec);

        return prepareDataResult;

    }

    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2，含"基金全部退款后资金退回余额宝(03T2)"场景的清算日期检查
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2，含"基金部分退款后资金退回余额宝(03T2)"场景的清算日期检查
     * 检查规则：0100增.clear_dt==0100增.biz_dt
     */
    public static TransactionRuleCheckResult clearDtCheck1001DecForYebRefund03T2Check(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String clearDt_0100Inc;
        CriteriaCheckData clearDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "clear_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (clearDtCriteriaCheckData_0100Inc!=null){
            clearDt_0100Inc = clearDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(clearDtCriteriaCheckData_0100Inc);
        } else {
            clearDt_0100Inc = "";
        }
        String bizDt_0100Inc;
        CriteriaCheckData bizDtCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "biz_dt".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (bizDtCriteriaCheckData_0100Inc!=null){
            bizDt_0100Inc = bizDtCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(bizDtCriteriaCheckData_0100Inc);
        } else {
            bizDt_0100Inc = "";
        }

        String errorRule = "";
        if (StringUtil.isEmpty(clearDt_0100Inc) || !(clearDt_0100Inc.equals(bizDt_0100Inc))){ //0100增.clear_dt!=0100增.biz_dt
            errorRule = "yeb_asset_increase_order.0100.clear_dt!= yeb_asset_increase_order.0100.biz_dt";
            logger.error(errorRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(errorRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }


    /**
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2，含"基金全部退款后资金退回余额宝(03T2)"场景的清算日期检查
     * 应用场景：FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2，含"基金部分退款后资金退回余额宝(03T2)"场景的清算日期检查
     * 检查规则：拓展字段包含“\"BIZ_ACTION_TYPE\":\"SINGLEPAY\"
     */
    public static TransactionRuleCheckResult identifierCheck1001IncForYebRefund03T2Check(FinancingcoreData financingcoreData,Map<String,Object> prepareDataResult){
        List<List<CriteriaCheckData>> criteriaCheckDataFor0100Inc = (List<List<CriteriaCheckData>>) prepareDataResult.get(yebAssetIncreaseOrder0100);
        //前置检查
        if (criteriaCheckDataFor0100Inc.isEmpty()){
            logger.error("未捞取到所需检查数据：yeb_asset_increase_order.0100指定待检查数据集");
            //todo：抛业务异常
        }

        List<CriteriaCheckData> criteriaCheckDataInfos = new ArrayList<>();//将所检测的字段放入组装结果中

        String extInfo_0100Inc;
        CriteriaCheckData extInfoCriteriaCheckData_0100Inc = criteriaCheckDataFor0100Inc.get(0).stream().filter(data -> "ext_info".equals(data.getDataName())).findFirst().get();//正常情况下criteriaCheckDataFor0100Dec的size=1
        if (extInfoCriteriaCheckData_0100Inc!=null){
            extInfo_0100Inc = extInfoCriteriaCheckData_0100Inc.getDataValue();
            criteriaCheckDataInfos.add(extInfoCriteriaCheckData_0100Inc);
        } else {
            extInfo_0100Inc = "";
        }

        String erroRule = "";
        if (StringUtil.isEmpty(extInfo_0100Inc) ||
                (!extInfo_0100Inc.contains("\"BIZ_ACTION_TYPE\":\"SINGLEPAY\""))){ //0100减拓展字段应包含"BIZ_ACTION_TYPE":"SINGLEPAY"
            erroRule = "yeb_asset_increase_order.0100.ext_info应包含\"BIZ_ACTION_TYPE\":\"SINGLEPAY\"字段";
            logger.error(erroRule);
            //todo：抛业务异常
        }

        //todo：组装result优化
        TransactionRuleCheckResult transactionRuleCheckResult = new TransactionRuleCheckResult();
        transactionRuleCheckResult.setRule(erroRule);//规则检测描述
        transactionRuleCheckResult.setRuleCheckResult(false);
        transactionRuleCheckResult.setRuleCheckResultType("error");
        transactionRuleCheckResult.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2);//todo：外部传进来
        transactionRuleCheckResult.setCriteriaCheckDatas(criteriaCheckDataInfos);

        return transactionRuleCheckResult;
    }

    /*******************************************************************测试工具************************************************************************/

    /**
     * 指定表名、捞取数据需要满足的条件<key字段，value字段对应值>、捞取的数据字段名，去捞取指定字段名所属的tableDataList.columnData的横纵坐标
     * @param tableDataList
     * @param tableName
     * @param conditionsMap
     * @param dataColumnNameList
     * @return
     */
    public static List<List<CriteriaCheckData>> fetchDataIndexByConditions(List<TableData> tableDataList,String tableName,Map<String,String> conditionsMap,List<String> dataColumnNameList){
        if (tableDataList.isEmpty()){
            logger.error("原始数据为空，无法捞取字段！");
        }
        if (StringUtil.isEmpty(tableName)){
            logger.error("指定的表名为空或空字符串，无法捞取字段！");
        }
        if (conditionsMap.isEmpty()){
            logger.error("捞取数据需要满足的条件为空，无法捞取字段！");
        }
        if (dataColumnNameList.isEmpty()){
            logger.error("捞取的数据字段名List为空，无法捞取字段！");
        }
        List<List<CriteriaCheckData>> criteriaCheckDataResult = new LinkedList<>();

        TableData tableData = null;
        int tableIndex = 0;//确认表具体所在tableDataList所在的索引
        for (int i = 0; i < tableDataList.size(); i++) {
            if (tableDataList.get(i).getTableName().contains(tableName)){ //此处用contains而不是equals
                tableData = tableDataList.get(i);
                tableIndex = i;
                break;
            }
        }
        //        TableData tableData = tableDataList.stream().filter(tb -> tb.getTableName().contains(tableName)).findFirst().get();//获取对应表数据
        if (tableData == null){
            return null;
        }
        //查询条件和结果返回索引
        int columnWidthIndex = 0;//识别字段在tableData.columnData中的所在行
        for (List<String> column : tableData.getColumnData()) {
            TableData finalTableData = tableData;
            boolean isEqualMatchConditions = conditionsMap.entrySet().stream()
                    .allMatch(entry -> entry.getValue().equals(column.get(finalTableData.getColumnIndex(entry.getKey()))));
            if (isEqualMatchConditions){
                List<CriteriaCheckData> criteriaCheckDataList = new LinkedList<>();
                for (String dataColumnName : dataColumnNameList) {
                    int dataColumnDepthIndex = tableData.getColumnIndex(dataColumnName);
                    int[] dataColumnIndex = {columnWidthIndex,dataColumnDepthIndex};//识别字段在tableData.columnData中的所在行和列
                    CriteriaCheckData criteriaCheckData = new CriteriaCheckData();
                    criteriaCheckData.setDataName(dataColumnName);
                    criteriaCheckData.setDataValue(column.get(dataColumnDepthIndex));//dataValue所在的表在tableList上的具体坐标
                    criteriaCheckData.setDataLocatedIndex(dataColumnIndex);//dataValue所在columnData的横纵坐标,例子real_amount[0，7],单表
                    criteriaCheckData.setTableNameIndex(tableIndex);
//                    criteriaCheckData.setOriTableData(tableData);
                    criteriaCheckDataList.add(criteriaCheckData);
                }
                if (!criteriaCheckDataList.isEmpty()){
                    criteriaCheckDataResult.add(criteriaCheckDataList);
                }
            }
            columnWidthIndex++;
        }

        return criteriaCheckDataResult;
    }

    /**
     * 根据指定字段名获取在tableData.columnName里的索引
     * @param tableData
     * @param columnName
     * @return
     */
    public static int locateColumnNameIndex(TableData tableData,String columnName){
        int columnNameIndex = 0;
        try{
            columnNameIndex = tableData.getColumnName().indexOf(columnName);
        }catch (Exception e){
            logger.error("获取表column数据失败，索引{}异常：{}",columnNameIndex,e);
        }
        return columnNameIndex;
    }

    public static void test(List<TransactionStageScene> transactionStageSceneList ){
        for (TransactionStageScene transactionStageScene:transactionStageSceneList){
            if (transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.BANK_TO_YEB.getFlow())){
                transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL);
            }
        }
    }


}
