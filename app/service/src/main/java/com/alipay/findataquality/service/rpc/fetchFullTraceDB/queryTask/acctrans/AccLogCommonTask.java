package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName AccLogTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/28 12:41
 * @Version V1.0
 **/
public class AccLogCommonTask extends AbstractQueryTask {
    public AccLogCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.ACCTRANS_LOG;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //查询余额账流水，关联字段包含payment_id、fa主单号、fa双边单号等
                "select * from iw_account_log_${db_flag} where payment_id in(${acc_log_order_list}) or (payment_id like '${date}%' and (cnl_no in(${acc_log_order_list}) or out_biz_no in (${acc_log_order_list}) or trans_out_order_no in(${acc_log_order_list})))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[1];
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        String[] extList = tableData.getArrayValueList("ext_params");
        if(extList!=null && extList.length>0){
            for (String ext : extList) {
                if(ext.contains("traceId=")){
                    String traceId = ext.split("traceId=")[1].split("\\^")[0];
                    context.appendAsArrayUnique("trace_id",traceId);
                }
            }
        }
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[1];
    }
}
