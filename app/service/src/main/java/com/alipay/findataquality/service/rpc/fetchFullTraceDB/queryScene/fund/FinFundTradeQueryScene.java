package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetAntTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetInsTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset.FinanceAssetTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinFundTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinTradeRecordTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinstrategyTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FundTransCoreTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.FinancingcoreCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAftransCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAftransCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName FinFundTradeQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/6 16:38
 * @Version V1.0
 **/
public class FinFundTradeQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FinFundTradeQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                //0
                FinFundTradeTask.class,

                //1
                FundTransCoreTask.class,
                FinTradeRecordTask.class,

                //2
                FinstrategyTask.class,
                FaassetTradeTask.class,

                //fa业务,2
                FinanceAssetTask.class,
                FinanceAssetInsTask.class,
                FinanceAssetAntTask.class,

                //3
                FinancingcoreCommonTask.class,
                YebcoreAftransCommonTask.class,
                AccLogCommonTask.class,
                //AccLogEpTask.class,
                //AccLogFundEpTask.class,

                //4
                MftransLogCommonTask.class,
                InstpayCommonTask.class,
                ProdtransTask.class,
                FinancingcoreCommonOpTask.class,
                MftransLogCommonOpTask.class,

                //5
                YebcoreAftransCommonOpTask.class,
                ObcloudpayTask.class,

                //6
                AccLogInnerAccountTask.class,
                AccLogInnerAccount2Task.class,
                AccLogInnerAccount3Task.class

        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{
                0,1,1,2,2,
                //fa
                2,2,2,
                3,3,3,//3,3,
                4,4,4,4,4,
                5,5,
                6,6,6
        };
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FINFUNDTRADE_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("db_r",DbUtils.getDbrFromDbFlag(context.getValue("db_flag")));
    }
}
