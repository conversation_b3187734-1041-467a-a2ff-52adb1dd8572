package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;

import java.util.*;

/**
 * @ClassName StrUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/30 15:12
 * @Version V1.0
 **/
public class StrUtils {
    public static String getJsonValue(String jsonData, String key, boolean CaseSensitive){
        if(StringUtil.isBlank(jsonData)||StringUtil.isBlank(key)){
            return null;
        }
        try{
            //将String数据转换为Json格式
            JSONObject obj = (JSONObject)JSONObject.parse(jsonData);
            if (CaseSensitive == false) {
                Set<String> keys = obj.keySet();
                for (String objKey:keys) {
                    if(key.equalsIgnoreCase(objKey)){
                        return obj.get(objKey).toString();
                    }
                }
                return null;
            }else{
                Object valueNode = obj.get(key);
                return valueNode == null ? null : valueNode.toString();
            }
        }catch (Exception e){
            return null;
        }
    }

    public static String getUUID(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }

    /**
     * 将primaryVar添加到varsHasIndex中，并去重，返回新的数组
     * @param varsHasIndex
     * @param primaryVar
     * @return
     */
    public static String[] addAndRemoveDuplicates(String[] varsHasIndex, String primaryVar) {
        if(varsHasIndex==null||varsHasIndex.length==0){
            return new String[]{primaryVar};
        }
        // 使用 LinkedHashSet 去重并保持顺序
        Set<String> set = new LinkedHashSet<>(Arrays.asList(varsHasIndex));
        set.add(primaryVar);

        // 将 Set 转换回数组
        return set.toArray(new String[0]);
    }

    public static String getUniqueTraceIds(String traceIds){
        if(StringUtil.isBlank(traceIds)){
            return null;
        }
        String[] list = traceIds.replaceAll("'","").replaceAll("、",",").split(",");
        Map<String,Boolean> map = new HashMap<>();
        for (String item: list) {
            String trace = item.trim();
            map.put(trace,true);
        }
        Set<String> keys = map.keySet();
        StringBuilder sb = new StringBuilder();
        int cnt = 0;
        for (String key: keys
             ) {
            if(cnt++==0){
                sb.append(key);
            }else{
                sb.append(","+key);
            }
        }
        return sb.toString();
    }

    /**
     * 返回执行字符串从倒数第endIndex位，截取length长度的字符串
     * 如，倒数2、3位，调用方式为substringFromEnd(input, 3, 2)
     * @param input
     * @param endIndex
     * @param length
     * @return
     */
    public static String substrFromEnd(String input, int endIndex, int length) {
        if(input==null){
            return null;
        }

        int startIndex = input.length() - endIndex;
        if (startIndex < 0) {
            startIndex = 0;
        }
        if (startIndex + length > input.length()) {
            length = input.length() - startIndex;
        }
        return input.substring(startIndex, startIndex + length);
    }

    public static String substrFromStart(String input, int startIndex, int length) {
        if(input==null){
            return null;
        }
        if (startIndex + length > input.length()) {
            length = input.length() - startIndex;
        }
        return input.substring(startIndex, startIndex + length);
    }

    public static String fillBlank(String input,char wrap, int totalLength){
        StringBuilder sb = new StringBuilder();
        int blankCnt = totalLength-input.length();
        for (int i = 0; i < blankCnt; i++) {
            sb.append(wrap);
        }
        sb.append(input);
        return sb.toString();
    }

    public static String wrapStr(String str, char wrap){
        return wrap+str+wrap;
    }

    /**
     * 去除给定str的前后连续的wrap字符，如输入abba和a，则返回bb
     * @param str
     * @param wrap
     * @return
     */
    public static String removeWrap(String str, char wrap){
        if (str==null||str.length() == 0) {
            return str;
        }
        int startIndex = 0;
        int endIndex = str.length() - 1;

        while (startIndex < str.length() && str.charAt(startIndex) == wrap) {
            startIndex++;
        }

        while (endIndex >= 0 && str.charAt(endIndex) == wrap) {
            endIndex--;
        }

        if (startIndex > endIndex) {
            return "";
        }
        return str.substring(startIndex, endIndex + 1);
    }

    /**
     * 给定一个逗号分隔的String，将其转换为一个like语句的条件
     * 如输入 mf_account_no和 'a','b'，则返回 (mf_account_no like 'a%' or mf_account_no like 'b%')
     * @param likeVar
     * @param value
     * @return
     */
    public static String autoJoinSqlLikeCondition(String likeVar, String value, boolean wrapHead){
        if(StringUtil.isBlank(likeVar)||StringUtil.isBlank(value)){
            return null;
        }
        String[] values = value.split(",");
        StringBuilder sb = new StringBuilder();
        int cnt=0;
        for (String valueItem: values){
            String removeWrapStr = removeWrap(valueItem.trim(),'\'').trim();
            if(wrapHead){
                removeWrapStr = "%"+removeWrapStr;
            }
            if(StringUtil.isNotBlank(removeWrapStr)){
                if(cnt++==0){
                    sb.append(likeVar + " like '"+removeWrapStr+"%'");
                }else{
                    sb.append(" or "+likeVar + " like '"+removeWrapStr+"%'");
                }
            }
        }
        if(StringUtil.isBlank(sb.toString())){
            return null;
        }
        sb.insert(0,'(');
        sb.append(')');
        return sb.toString();
    }

    public static String autoJoinArrayUnique(String str1,String str2,char wrap){
        Map<String,Boolean> map = new HashMap<>();
        List<String>result = new ArrayList<>();
        fillList(str1,result,map,wrap);
        fillList(str2,result,map,wrap);
        if(map.size()==0){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int cnt = 0;
        for (String value: result){
            String wrapStr = wrapStr(value,wrap);
            if(cnt++==0){
                sb.append(wrapStr);
            }else{
                sb.append(","+wrapStr);
            }
        }
        return sb.toString();
    }

    /**
     * 从逗号分隔的字符串中，获取第一个非空字符串，如 'a','b','c' 返回a
     * @param str
     * @return
     */
    public static String getFirstItem(String str){
        if(StringUtil.isBlank(str)){
            return null;
        }
        String strs[] = str.split(",");
        for (String strItem: strs){
            String removeWrap = removeWrap(strItem.trim(),'\'').trim();
            if(StringUtil.isNotBlank(removeWrap)){
                return removeWrap;
            }
        }
        return null;
    }

    public static void fillList(String listStr, List<String> list, Map<String, Boolean> map, char wrap){
        if(!StringUtil.isBlank(listStr)){
            String[]strSplit = listStr.split(",");
            for (String str: strSplit){
                String key = removeWrap(str,wrap);
                if(StringUtil.isBlank(key)){
                    continue;
                }
                if(!map.containsKey(key)){
                    list.add(key);
                    map.put(key,true);
                }
            }
        }
    }

    /**
     * 自动在前后添加字符串
     * @param str
     * @param wrap
     * @return
     */
    public static String autoWrapStr(String str, String wrap){
        if(str==null||wrap==null){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        if(!str.startsWith(wrap)){
            sb.append(wrap);
        }
        sb.append(str);
        if(!str.endsWith(wrap)){
            sb.append(wrap);
        }
        return sb.toString();
    }

    public static String joinArray(String[]array,String separator){
        if(array==null||array.length==0){
            return null;
        }
        StringBuilder sb = new StringBuilder();
        int cnt=0;
        for (String item:array) {
            if(cnt++==0){
                sb.append(item);
            }else{
                sb.append(separator+item);
            }
        }
        return sb.toString();
    }

    public static String getKeyDataSummary(List<TableData> tableDataList){
        StringBuilder sb = new StringBuilder();
        for (TableData tableData:tableDataList){
            sb.append("数据表:"+tableData.getDbName()+"."+tableData.getTableName()+"共包含"+tableData.getColumnData().size()+"条数据，关键字段值如下：\n");
            int columnSize = tableData.getColumnName().size();
            for(int i=0;i<tableData.getColumnData().size();i++){
                //超过1条提示当前所属的条数
                if(tableData.getColumnData().size()>1){
                    if(i>0){
                        sb.append("\n");
                    }
                    sb.append("第"+(i+1)+"条数据：\n");
                }
                StringBuilder emptyVars = new StringBuilder();
                for(int j=0;j<columnSize;j++){
                    String varName = tableData.getColumnName().get(j);
                    String value = tableData.getColumnData().get(i).get(j);
                    if(value!=null){
                        sb.append(varName+":"+value+"\n");
                    }else{
                        if(emptyVars.length()==0){
                            emptyVars.append(varName);
                        }else{
                            emptyVars.append(","+varName);
                        }
                    }
                }
                if(emptyVars.length()>0){
                    sb.append("本条数据中为空的关键字段有:"+emptyVars.toString()+"\n");
                }
            }
            sb.append("\n");
        }
        return sb.toString();
    }
}
