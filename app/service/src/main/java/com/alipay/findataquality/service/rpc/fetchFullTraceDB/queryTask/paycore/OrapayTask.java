package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName OrapayTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 18:16
 * @Version V1.0
 **/
public class OrapayTask extends ObcloudpayTask {
    public OrapayTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.ORAPAY;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from pmt_pay_ord_${db_flag} where payment_id in (${payment_id})",
                "select * from pmt_flux_ord_${db_flag} where flux_id in (${payment_id})",
                "select * from pmt_flux_cmd_${db_flag} where flux_id in (${payment_id})",
                "select * from pmt_exec_cmd_${db_flag} where order_id in (${payment_id})",
                "select * from pmt_comm_fd_dtl_${db_flag} where payment_id in (${payment_id})",
                "select * from pmt_comm_rfd_dtl_${db_flag} where refund_id in (${payment_id})"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[6];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[6];
    }
}
