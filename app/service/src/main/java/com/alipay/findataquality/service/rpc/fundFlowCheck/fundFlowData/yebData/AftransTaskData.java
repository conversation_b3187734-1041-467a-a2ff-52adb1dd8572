package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;


/**
 * @ClassName AftransTaskData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:22
 * @Version V1.0
 **/
public class AftransTaskData extends AssetRecordData {

    public static final String AFTRANS_3008 = "yebcore.yeb_aftrans_task";
    public static final String AFTRANS_PLUS = "yebcore.yebs_aftrans_task";
    public static final String AFTRANS_SUBCARD = "yebcore.yebs_subcard_aftrans_task";

    @Override
    public String[] dataTable() {
        return new String[]{
                AFTRANS_3008,
                AFTRANS_PLUS,
                AFTRANS_SUBCARD
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        return null;
    }
}
