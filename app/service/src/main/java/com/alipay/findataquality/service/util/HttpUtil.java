package com.alipay.findataquality.service.util;


import com.alibaba.fastjson.JSON;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.concurrent.TimeUnit;
import static com.alipay.findataquality.service.constant.DependencyAnalyzerConstant.BAKERY_TOKEN_PROD;

public class HttpUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);


    /**
     * http get请求
     * @param url
     * @return
     * @throws Exception
     */
    public static String sendHttpGetForUIMock(String url) throws Exception {
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .connectTimeout(60000, TimeUnit.MILLISECONDS)
                .readTimeout(60000, TimeUnit.MILLISECONDS)
                .writeTimeout(60000, TimeUnit.MILLISECONDS)
                .build();
        Request request = new Request.Builder().url(url).addHeader("bakery-token", BAKERY_TOKEN_PROD)
                .build();

        Response response = okHttpClient.newCall(request).execute();
//        System.out.println("response:"+response);

        return response.body().string();
    }

    /**
     * http post请求
     * @param requestUrl
     * @param jsonObject
     * @return
     */
    public static String sendHttpPostForUIMock(String requestUrl, String jsonObject) {
        HttpResponse httpResponse;
        Integer timeOutSpace = 360000;
        String retSrc = "";
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeOutSpace)
                .setConnectionRequestTimeout(timeOutSpace)
                .setSocketTimeout(timeOutSpace).build();
        CloseableHttpClient httpClient = HttpClientBuilder.create().setDefaultRequestConfig(config).build();
        HttpPost httpPost = new HttpPost(requestUrl);
        try {
            if (jsonObject != null) {
                StringEntity requestEntity = new StringEntity(jsonObject, "utf-8");
//                requestEntity.setContentEncoding("UTF-8");
                httpPost.addHeader("Accept", "application/json");
                httpPost.addHeader("Content-Type", "application/json");
                httpPost.setHeader("bakery-token",BAKERY_TOKEN_PROD);
                httpPost.setEntity(requestEntity);
                httpResponse = (HttpResponse) httpClient.execute(httpPost);
                HttpEntity httpEntity = httpResponse.getEntity();
                //                retSrc = EntityUtils.toString(httpEntity);
                retSrc = EntityUtils.toString(httpEntity, "utf-8");
//                System.out.println("retSrc:"+retSrc);
            }
        } catch (ClientProtocolException e) {
            logger.error("ClientProtocolException!!!e:{}",e);
        } catch (UnsupportedEncodingException e) {
            logger.error("UnsupportedEncodingException!!!e:{}",e);
        } catch (IOException e) {
            logger.error("IOException!!!e:{}",e);
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                logger.error("http close exception!!!e:{}",e);
            }
        }
        return retSrc;
    }


    /**
     * http post请求
     * @return
     */
    public static String sendHttpPostCommon(String url, String JSONBody){
        String result = "";
        // 构造HttpClient客户端
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // 创建Post请求对象
        HttpPost httpPost = new HttpPost(url);
        // HttpGet返回对象
        CloseableHttpResponse response = null;
        httpPost.addHeader("Accept", "application/json");
        httpPost.addHeader("Content-Type", "application/json");

        HttpEntity entity = new StringEntity(JSONBody, "UTF-8");
        httpPost.setEntity(entity);
        try {
            System.out.println("httpPost request:"+ JSON.toJSONString(httpPost));
            response = httpclient.execute(httpPost);
            StatusLine status = response.getStatusLine();
            int state = status.getStatusCode();
            if (state == HttpStatus.SC_OK) {
                HttpEntity entityResult = response.getEntity();
                result = EntityUtils.toString(entityResult, "UTF-8");
            }
        } catch (IOException e) {
            logger.error("sendHttpPostCommon exception!!!e:{}",e);
        }

        return result;
    }

}
