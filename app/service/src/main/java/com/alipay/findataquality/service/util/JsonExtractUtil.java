package com.alipay.findataquality.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.rpc.common.utils.StringUtils;
import com.google.common.collect.ImmutableMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class JsonExtractUtil {
    private static final Logger logger = LoggerFactory.getLogger(JsonExtractUtil.class);

    /**
     * 基于json路径和对应值的键值对，生成json。不支持根json是数组的情况。
     * json路径样例：$.aaa.bbb.ccc，$.userList[1].age，$.userList[2][0].class.name等
     * @param pathAndValueMap
     * @return
     */
    public static JSONObject combinePathAndValuePairToJson(Map<String, String> pathAndValueMap) {
        JSONObject result = new JSONObject();

        List<String> keys = new ArrayList<>(pathAndValueMap.keySet());
        keys.forEach(key -> {
            String value = pathAndValueMap.get(key);
            String[] pathFields = key.split("\\.");

            if (pathFields.length == 1) {
                if (isValidJsonObject(value)) {
                    populateValueByPathAndObj(pathFields[0], result, JSONObject.parseObject(value));
                } else if (isValidJsonArray(value)) {
                    populateValueByPathAndObj(pathFields[0], result, JSONObject.parseArray(value));
                } else {
                    populateValueByPathAndObj(pathFields[0], result, value);
                }
            } else {
                JSONObject nextLevelObj = null;
                for (int i = 0; i < pathFields.length; i++) {
                    String pathField = pathFields[i];
                    if (i == 0) {
                        //第一层
                        nextLevelObj = populateValueByPathAndObj(pathField, result, new JSONObject());
                    } else if(i == pathFields.length - 1) {
                        //最后一层
                        JSONObject currLevelObj = nextLevelObj;
                        populateValueByPathAndObj(pathField, currLevelObj, value);
                    } else {
                        //中间层
                        JSONObject currLevelObj = nextLevelObj;
                        nextLevelObj = populateValueByPathAndObj(pathField, currLevelObj, new JSONObject());
                    }
                }
            }
        });
        return result;
    }


    /**
     * 基于数据路径字段（"."与"."之间的部分，如name、userList[0]、bklightList[0][1]）和路径字段的上层对象，将value填充进指定路径
     * @param pathField
     * @param currentObj
     * @param value
     */
    private static JSONObject populateValueByPathAndObj(String pathField, JSONObject currentObj, Object value) {
        JSONObject returnObj = null;
        if (pathField.contains("[") && pathField.endsWith("]")) {
            //含有数组
            int indexStart = 0;
            int indexEnd = 0;

            JSONArray tempArray = null;
            while (indexEnd < pathField.length() - 1) {
                indexStart = pathField.indexOf("[", indexStart+1);
                indexEnd = pathField.indexOf("]", indexEnd+1);
                Integer arrIndex = Integer.parseInt(pathField.substring(indexStart + 1, indexEnd));

                if (tempArray == null) {
                    //当前是字段下第一层数组
                    String arrFieldName = pathField.substring(0, indexStart);
                    if (currentObj.containsKey(arrFieldName) && currentObj.getJSONArray(arrFieldName) != null) {
                        JSONArray existJsonArray = currentObj.getJSONArray(arrFieldName);
                        if (existJsonArray.size() <= arrIndex) {
                            //历史已存在的没有对应下标
                            if (indexEnd < pathField.length() - 1) {
                                //还有下一层
                                tempArray = new JSONArray();
                                existJsonArray.set(arrIndex, tempArray);
                            } else {
                                //没有下一层了
                                existJsonArray.set(arrIndex, value);
                                returnObj = value instanceof JSONObject ? (JSONObject)value : null;
                            }
                        } else {
                            //历史已存在的有对应下标
                            if (indexEnd < pathField.length() - 1) {
                                //还有下一层
                                tempArray = existJsonArray.getJSONArray(arrIndex);
                                if (tempArray == null) {
                                    tempArray = new JSONArray();
                                    existJsonArray.set(arrIndex, tempArray);
                                }
                            } else {
                                //没有下一层了，如果已有对应下标且对应下标值不为null，则不替换
                                Object nextLevelObj = existJsonArray.get(arrIndex);
                                if (nextLevelObj == null) {
                                    existJsonArray.set(arrIndex, value);
                                    returnObj = value instanceof JSONObject ? (JSONObject)value : null;
                                } else {
                                    returnObj = nextLevelObj instanceof JSONObject ? (JSONObject)nextLevelObj : null;
                                }
                            }
                        }
                        currentObj.put(arrFieldName, existJsonArray);
                    } else {
                        JSONArray newJsonArray = new JSONArray();
                        if (indexEnd < pathField.length() - 1) {
                            //还有下一层
                            tempArray = new JSONArray();
                            newJsonArray.set(arrIndex, tempArray);
                        } else {
                            //没有下一层了
                            newJsonArray.set(arrIndex, value);
                            returnObj = value instanceof JSONObject ? (JSONObject)value : null;
                        }

                        currentObj.put(arrFieldName, newJsonArray);
                    }
                } else {
                    //当前是字段下非第一层数组
                    JSONArray existJsonArray = tempArray;
                    if (existJsonArray.size() <= arrIndex) {
                        //历史已存在的没有对应下标
                        if (indexEnd < pathField.length() - 1) {
                            //还有下一层
                            tempArray = new JSONArray();
                            existJsonArray.set(arrIndex, tempArray);
                        } else {
                            //没有下一层了
                            existJsonArray.set(arrIndex, value);
                            returnObj = value instanceof JSONObject ? (JSONObject)value : null;
                        }
                    } else {
                        //历史已存在的有对应下标
                        if (indexEnd < pathField.length() - 1) {
                            //还有下一层
                            tempArray = existJsonArray.getJSONArray(arrIndex);
                            if (tempArray == null) {
                                tempArray = new JSONArray();
                                existJsonArray.set(arrIndex, tempArray);
                            }
                        } else {
                            //没有下一层了，如果已有对应下标且对应下标值不为null，则不替换
                            Object nextLevelObj = existJsonArray.get(arrIndex);
                            if (nextLevelObj == null) {
                                existJsonArray.set(arrIndex, value);
                                returnObj = value instanceof JSONObject ? (JSONObject)value : null;
                            } else {
                                returnObj = nextLevelObj instanceof JSONObject ? (JSONObject)nextLevelObj : null;
                            }
                        }
                    }
                }
            }
        } else {
            //不含数组
            Object nextLevelObj = currentObj.get(pathField);
            if (nextLevelObj == null) {
                currentObj.put(pathField, value);
                returnObj = value instanceof JSONObject ? (JSONObject)value : null;
            } else {
                returnObj = nextLevelObj instanceof JSONObject ? (JSONObject)nextLevelObj : null;
            }
        }
        return returnObj;
    }

    /**
     * 根据一个数据对象，解析出全量的路径和值的pair，即Map<path路径，对应值>
     * key--某个路径, xx.xx.xxx[xx].xxx；
     * value--对应值
     * @param json
     */
    public static Map<String, String> extractPathAndValuePair(JSON json, String prefix, boolean shouldStrToJson, Map<String, Object> boolEmptyStrPathAndValueMap) {
        Map<String, String> result = new HashMap<>();
        if (json instanceof JSONObject) {
            JSONObject jsonObject = ((JSONObject) json);
            Set<String> keys = jsonObject.keySet();
            for (String key : keys) {
                Object valueObj = jsonObject.get(key);
                String value = jsonObject.getString(key);
                String currentPrefix = key;
                if (StringUtils.isNotEmpty(prefix)) {
                    currentPrefix = prefix + "." + key;
                }

                if (value != null && value.startsWith("{") && !value.startsWith("{{") && (!(valueObj instanceof String) || shouldStrToJson)) {
                    //下层是对象
                    try {
                        Map<String, String> subResult = extractPathAndValuePair(jsonObject.getJSONObject(key), currentPrefix, shouldStrToJson, boolEmptyStrPathAndValueMap);
                        if (subResult.isEmpty()) {
                            result.putAll(ImmutableMap.of(currentPrefix, value));
                        } else {
                            result.putAll(subResult);
                        }
                    } catch (Exception e) {
                        //当作普通非json字符串处理
                       logger.warn("JsonExtractUtil.extractPathAndValuePair {0} 不可解析为json，被当作普通字符串处理.", value);
                    }
                } else if (value != null && value.startsWith("[") && value.endsWith("]") && (!(valueObj instanceof String) || shouldStrToJson)) {
                    //下层是数组
                    try {
                        Map<String, String> subResult = extractPathAndValuePair(jsonObject.getJSONArray(key), currentPrefix, shouldStrToJson, boolEmptyStrPathAndValueMap);
                        if (subResult.isEmpty()) {
                            result.putAll(ImmutableMap.of(currentPrefix, value));
                        } else {
                            result.putAll(subResult);
                        }
                    } catch (Exception e) {
                        //当作普通非json字符串处理
                        logger.warn( "JsonExtractUtil.extractPathAndValuePair {0} 不可解析为json，被当作普通字符串处理.", value);
                    }
                } else {
                    //下层是具体值
                    result.put(currentPrefix, value);

                    if (boolEmptyStrPathAndValueMap != null) {
                        if (valueObj instanceof Boolean) {
                            boolEmptyStrPathAndValueMap.put(currentPrefix, (Boolean)valueObj);
                        }
                        if (valueObj == null || "".equals(valueObj)) {
                            boolEmptyStrPathAndValueMap.put(currentPrefix, valueObj);
                        }
                    }
                }
            }
        }
        if (json instanceof JSONArray) {
            JSONArray jsonArray = ((JSONArray) json);
            for (int i=0; i<jsonArray.size(); i++) {
                String value = jsonArray.getString(i);
                String currentPrefix = prefix + "[" + i + "]";
                if (value != null && value.startsWith("{") && !value.startsWith("{{")) {
                    try {
                        Map<String, String> subResult = extractPathAndValuePair(jsonArray.getJSONObject(i), currentPrefix, shouldStrToJson, boolEmptyStrPathAndValueMap);
                        if (subResult.isEmpty()) {
                            result.putAll(ImmutableMap.of(currentPrefix, value));
                        } else {
                            result.putAll(subResult);
                        }
                    } catch (Exception e) {
                        //当作普通非json字符串处理
                        logger.warn( "JsonExtractUtil.extractPathAndValuePair {0} 不可解析为json，被当作普通字符串处理.", value);
                    }
                } else if (value != null && value.startsWith("[") && value.endsWith("]")) {
                    try {
                        Map<String, String> subResult = extractPathAndValuePair(jsonArray.getJSONArray(i), currentPrefix, shouldStrToJson, boolEmptyStrPathAndValueMap);
                        if (subResult.isEmpty()) {
                            result.putAll(ImmutableMap.of(currentPrefix, value));
                        } else {
                            result.putAll(subResult);
                        }
                    } catch (Exception e) {
                        //当作普通非json字符串处理
                        logger.warn( "JsonExtractUtil.extractPathAndValuePair {0} 不可解析为json，被当作普通字符串处理.", value);
                    }
                } else {
                    //下层是具体值
                    result.put(currentPrefix, value);
                }
            }
        }
        return result;
    }

    public static boolean isValidJson(String jsonStr) {
        return (isValidJsonObject(jsonStr) || isValidJsonArray(jsonStr));
    }

    public static boolean isValidJsonObject(String jsonStr) {
        if (StringUtils.isNotEmpty(jsonStr) && jsonStr.startsWith("{") && !jsonStr.startsWith("{{") && jsonStr.endsWith("}")) {
            return true;
        }
        return false;
    }

    public static boolean isValidJsonArray(String jsonStr) {
        if (StringUtils.isNotEmpty(jsonStr) && jsonStr.startsWith("[") && jsonStr.endsWith("]")) {
            return true;
        }
        return false;
    }
}
