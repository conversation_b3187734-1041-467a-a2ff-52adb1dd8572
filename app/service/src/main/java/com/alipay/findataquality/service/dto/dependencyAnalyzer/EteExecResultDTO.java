package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import java.util.Map;

/**
 * 端到端执行结果DTO
 * @auther lidengke.ldk
 */
public class EteExecResultDTO {
    /**
     * traceId
     */
    private String traceId;
    /**
     * 执行详情描述
     */
    private String message;
    /**
     * 记录额外信息
     */
    private Map<String, Object> recordExt;

    private String flowInstanceCode;

    private String flowRecordCode;

    private String env;

    private String linkdEnv;

    private Map<String,Object> requestParams;

    private String response;

    private String taskUrl;

    private String taskId;

    private boolean success;

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, Object> getRecordExt() {
        return recordExt;
    }

    public void setRecordExt(Map<String, Object> recordExt) {
        this.recordExt = recordExt;
    }

    public String getFlowInstanceCode() {
        return flowInstanceCode;
    }

    public void setFlowInstanceCode(String flowInstanceCode) {
        this.flowInstanceCode = flowInstanceCode;
    }

    public String getFlowRecordCode() {
        return flowRecordCode;
    }

    public void setFlowRecordCode(String flowRecordCode) {
        this.flowRecordCode = flowRecordCode;
    }

    public String getEnv() {
        return env;
    }

    public String getLinkdEnv() {
        return linkdEnv;
    }

    public void setLinkdEnv(String linkdEnv) {
        this.linkdEnv = linkdEnv;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public Map<String, Object> getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(Map<String, Object> requestParams) {
        this.requestParams = requestParams;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getTaskUrl() {
        return taskUrl;
    }

    public void setTaskUrl(String taskUrl) {
        this.taskUrl = taskUrl;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public boolean getSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
