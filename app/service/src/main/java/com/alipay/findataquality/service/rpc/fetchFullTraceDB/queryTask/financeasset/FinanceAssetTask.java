package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.financeasset;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

import java.util.List;

/**
 * @ClassName FinanceAssetTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/30 13:57
 * @Version V1.0
 **/
public class FinanceAssetTask extends AbstractQueryTask {
    public FinanceAssetTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String[] queryDependency() {
        return new String[]{"db_flag", "db_schema", "date", "main_order_id"};
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCE_ASSET;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from fa_main_order_${db_flag} where order_id = '${main_order_id}'",
                "select * from fa_bilateral_order_${db_flag} where order_id like '${date}%' and main_order_id = '${main_order_id}'",
                "select * from fa_unilateral_order_${db_flag} where order_id like '${date}%' and main_order_id = '${main_order_id}'",
                "select * from fa_async_task_${db_flag} where task_id like '${date}%' and (main_order_id = '${main_order_id}' or ext_info like '%${main_order_id}%')",
                "select * from fa_credit_account_order_${db_flag} where order_id in (${credit_order_id})",
                "select * from fa_financing_order_${db_flag} where main_order_id = '${main_order_id}'",
                "select * from fa_async_trade_detail_${db_flag} where main_order_id = '${main_order_id}'",
                "select * from fa_com_order_${db_flag} where main_order_id = '${main_order_id}'",
                "select * from fa_pay_order_${db_flag} where main_order_id = '${main_order_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        String[] result = new String[9];
        result[0] = "user_id";
        result[1] = "ext_info.traceID->trace_id";
        return result;
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        OutTypeEnum[] result = new OutTypeEnum[9];
        result[0] = OutTypeEnum.SINGLE;
        result[1] = OutTypeEnum.ARRAY_APPEND;
        return result;
    }

    @Override
    public String[] keyData() {
        return new String[]{
                "user_id, amount, status, order_id, biz_no",
                "user_id, amount, status, stage_no, channel_id, order_id, biz_no, ext_info.passThroughInfo, ext_info.payerAssetInfo, ext_info.fincoreId, ext_info.transOutDest, ext_info.balOptFlag, ext_info.financingDest, ext_info.LIMIT_DATA_SUB_TYPE, ext_info.LIMIT_DATA_DESC",
                "user_id, amount, status, order_id, biz_no, asset_type, asset_type_code, principal_id, node_id, ext_info"
        };
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        /**
         * 设置余额日志关联单号
         */
        autoAppendArray(tableData, "main_order_id", "acc_log_order_list");
        autoAppendArray(tableData, "order_id", "acc_log_order_list");
        //添加所有扩展字段中的paymentId
        autoAppendArray(tableData, "ext_info.paymentId", "acc_log_order_list");

        /**
         * 设置fc取数关联单号
         */
        autoAppendArray(tableData, "main_order_id", "fc_combine_order_list");
        autoAppendArray(tableData, "order_id", "fc_combine_order_list");
        autoAppendArray(tableData, "biz_no", "fc_combine_order_list");
        autoAppendArray(tableData, "ext_info.assetOrderId", "fc_combine_order_list");
        autoAppendArray(tableData, "ext_info.fincoreId", "fc_combine_order_list");

        /**
         * 设置支付单关联关系
         */
        autoAppendArray(tableData, "ext_info.paymentId", "payment_id");

        /**
         * 设置creadit关联单号
         */
        autoAppendArray(tableData, "ext_info.creditAccountOrderId", "credit_order_id");

        /**
         * 设置账务关联单号
         */
        context.copyValue("fc_combine_order_list", "mftrans_combine_order_list");
        context.copyValue("fc_combine_order_list", "aftrans_order_list");
        List<List<String>> dataRowList = tableData.getColumnData();
        for (List<String> row : dataRowList) {
            if (index == 1) {
                String payerNo = row.get(tableData.getColumnIndex("payer_principal_id"));
                String payeeNo = row.get(tableData.getColumnIndex("payee_principal_id"));
                context.appendAsArrayUnique("common_mf_account_no_list",payerNo);
                context.appendAsArrayUnique("common_mf_account_no_list",payeeNo);
                /*
                if (payerNo != null && payerNo.startsWith("60")) {
                    context.putGlobal("mf_account_no", payerNo);
                }
                if (payeeNo != null && payeeNo.startsWith("60")) {
                    context.putGlobal("mf_account_no", payeeNo);
                }
                 */
            }
        }
        /**
         * 处理产品账逻辑
         * 判断双边单强字段asset_type = CHARGE_LIMIT_PRODTRANS，代表户账
         * --根据扩展字段中subProductCode、relationProduct拼接产品码
         *
         * 判断双边单强字段asset_type = FREE_QUOTA，代表免费提现额度
         * --产品码固定为ITC_CHARGE_CUST
         *
         * 免费提现额度是空的优先principal_id，没有就用user_id
         */
            //将微账号和uid全部传入principals，以兼容principal为空的场景
        autoAppendArray(tableData, "principal_id", "prodtrans_principal_id_list");
        autoAppendArray(tableData, "user_id", "prodtrans_principal_id_list");
        autoAppendArray(tableData, "main_order_id", "prodtrans_out_biz_no_list");
        autoAppendArray(tableData, "order_id", "prodtrans_out_biz_no_list");
    }
}
