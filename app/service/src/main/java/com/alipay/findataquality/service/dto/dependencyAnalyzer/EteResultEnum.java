package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import com.alibaba.common.lang.StringUtil;

/**
 * 用例分母用例实例端到端执行结果。0-待执行，1-成功，2-失败
 * <AUTHOR>
 * @version $Id: Z90DcCaseInstanceEteResultEnum.java, v 0.1 Oct 12, 2022 12:49:00 PM lidengke.ldk Exp $
 */
public enum EteResultEnum {
    EXECUTING("0", "待执行"), SUCCESS("1", "成功"), FAIL("2", "失败");

    private String code;

    private String desc;

    private EteResultEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static EteResultEnum getByCode(String code) {
        for (EteResultEnum item : EteResultEnum.values()) {
            if (StringUtil.equals(item.getCode(), code)) {
                return item;
            }
        }
        return null;
    }
}
