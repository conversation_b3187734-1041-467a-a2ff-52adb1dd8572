package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.charge;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinFundTaskMgrTask;

/**
 * @ClassName FundChargePersonalQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/19 20:14
 * @Version V1.0
 **/
public class FundChargePersonalQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FundChargePersonalQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                FinFundTaskMgrTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FUND_CHARGE_C_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {

    }
}
