package com.alipay.findataquality.service.rpc.fetchFullTraceDB.model;

import com.alibaba.common.lang.StringUtil;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.JdbcTypeEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;

/**
 * @ClassName DBConnectConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 15:54
 * @Version V1.0
 **/
public class DBConnectConfig {
    //数据库连接信息
    private String dbUrl;
    //数据库用户名密码
    private String dbSchema;
    //zdal加密串
    private String encryption;
    private String dbUser;
    private String dbPassword;
    private JdbcTypeEnum jdbcTypeEnum;

    public String getDbUrl() {
        return dbUrl;
    }

    public void setDbUrl(String dbUrl) {
        this.dbUrl = dbUrl;
    }

    public String getDbUser() {
        return dbUser;
    }

    public void setDbUser(String dbUser) {
        this.dbUser = dbUser;
    }

    public String getDbPassword() {
        return dbPassword;
    }

    public void setDbPassword(String dbPassword) {
        this.dbPassword = dbPassword;
    }

    public void setZdalEncryption(String zdalEncryption){
        setEncryption(zdalEncryption);
        String pwd = DbUtils.decodeZdal(zdalEncryption);
        if(!StringUtil.isBlank(pwd)){
            this.setDbPassword(pwd);
        }
    }

    public void setDbName(String dbName){
        setDbSchema(dbName);
        switch(this.getJdbcTypeEnum()){
            case MYSQL:
                this.setDbUrl(fillMysqlUrl(dbName));
                break;
            case OB:
                this.setDbUrl(fillObUrl(dbName));
                break;
            default:break;
        }
    }

    public String getDbSchema() {
        return dbSchema;
    }

    public void setDbSchema(String dbSchema) {
        this.dbSchema = dbSchema;
    }

    public String getEncryption() {
        return encryption;
    }

    public void setEncryption(String encryption) {
        this.encryption = encryption;
    }

    private String fillMysqlUrl(String dbName){
        return "*****************************************/"+dbName+"?useUnicode=true&amp;characterEncoding=utf8";
    }

    private String fillObUrl(String dbName){
        return "*********************************************/"+dbName+"?usePartition=true";
    }

    public JdbcTypeEnum getJdbcTypeEnum() {
		return jdbcTypeEnum;
	}

	public void setJdbcTypeEnum(JdbcTypeEnum jdbcTypeEnum) {
		this.jdbcTypeEnum = jdbcTypeEnum;
	}
}
