package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreSubcardTradePreTask extends AbstractQueryTask {

    public YebcoreSubcardTradePreTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //转出的关联关系，转出表biz_order_no=子卡交易表biz_order_no，此处先查转出表，根据转出表查找子卡交易表
                //转入关联关系，转入表order_no=子卡交易表biz_order_no，因而此处可先不查转入表
                "select * from fmp_yeb_trans_out_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from yebs_trans_out_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))" ,
                "select * from fmp_yeb_ext_${db_flag} where order_no in (${fc_combine_order_list})",
                //子卡交易表
                //"select * from yebs_transfer_order_${db_flag} where order_no = '${order_no}'",
                "select * from yebs_transfer_order_${db_flag} where order_no like '${date}%' and (order_no in (${fc_combine_order_list}) or biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list}))",
                "select * from fmp_yeb_trans_notrade_${db_flag} where id in (${fc_combine_order_list}) or (id like '${date}%' and (biz_order_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list})))"
        };
    }

    @Override
    public int[] filterResultIndex() {
        return new int[]{0,1,2};
    }

    @Override
    public String[] outcomeValue() {
        String fcOrderCombine = "order_no->fc_combine_order_list,biz_order_no->fc_combine_order_list,cnl_no->fc_combine_order_list";
        return new String[]{
                fcOrderCombine,
                fcOrderCombine,
                null,
                "mini_account_no->common_mf_account_no_list,from_mini_account_no->common_mf_account_no_list,biz_order_no->fc_combine_order_list,order_no->fc_combine_order_list,cnl_no->fc_combine_order_list",
                "subcard_no->common_mf_account_no_list,from_subcard_no->common_mf_account_no_list,biz_order_no->fc_combine_order_list,order_no->fc_combine_order_list,cnl_no->fc_combine_order_list"
        };
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[]{
                OutTypeEnum.ARRAY_APPEND,
                OutTypeEnum.ARRAY_APPEND,
                null,
                OutTypeEnum.ARRAY_APPEND,
                OutTypeEnum.ARRAY_APPEND};
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValueAppend("fc_combine_order_list","mftrans_combine_order_list");
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAppend("order_no","fc_combine_order_list");
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        if(index!=2){
            return;
        }

        /**
         * 在K12子卡交易中，存在群成员像群主转入的场景，该场景存在跨分表位，故此处提取对手方信息
         */
        String[] extInfoList = tableData.getArrayValueList("ext_info");
        String opUserId = null;
        if(extInfoList!=null){
            for(String extInfo:extInfoList){
                String[]list = extInfo.split("\n");
                for (String line:list) {
                    String value = line.trim();
                    //提取对手方信息，payerUserId=2088402672816460
                    if (value.startsWith("payerUserId=")){
                        String[] splitLine = value.split("=");
                        opUserId = splitLine[1].trim();
                    }
                }
                if(StringUtil.isNotBlank(opUserId)){
                    String opDbFlag = StrUtils.substrFromEnd(opUserId,3,2);
                    if(!opDbFlag.equals(context.getValue("db_flag"))){
                        context.putGlobal("op_db_flag",opDbFlag);
                        context.putGlobal("op_db_schema","0"+StrUtils.substrFromEnd(opUserId,3,1));
                        String dbr = DbUtils.getDbrFromDbFlag(context.getValue("op_db_flag"));
                        context.putGlobal("op_db_r",dbr);

                        //设置产品账分表位
                        String dbFlag = context.getValue("op_db_flag");
                        String date = context.getValue("date");
                        String prodtransDbFlag = DbUtils.getProdtransSchemaFromDbFlag(dbFlag,date);
                        if(StringUtil.isNotBlank(prodtransDbFlag)){
                            context.putGlobal("op_prodtrans_db_flag",prodtransDbFlag);
                        }
                        String isntpay = DbUtils.getInstpaySchemaFromDbFlag(dbFlag);
                        if(StringUtil.isNotBlank(isntpay)){
                            context.putGlobal("op_instpay_db_schema",isntpay);
                        }
                        break;
                    }
                }
            }
        }
    }
}
