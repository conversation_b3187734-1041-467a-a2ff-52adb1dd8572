package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.InstIdEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.CheckUtil;
import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName FinancingcoreData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:23
 * @Version V1.0
 **/
public class FinancingcoreData extends AssetRecordData {

    public static final String INCREASE = "yebapp.yeb_asset_increase_order";
    public static final String DECREASE = "yebapp.yeb_asset_decrease_order";
    public static final String FREEZE = "yebapp.yeb_asset_freeze_order";
    public static final String UNFREEZE = "yebapp.yeb_asset_unfreeze_order";

    @Override
    public String[] dataTable() {
        return new String[]{
                INCREASE,
                DECREASE,
                FREEZE,
                UNFREEZE
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        //userId,String amount,String gmtCreate
        AssetOperation operation = AssetOperation.createBaseOperation(tableData, dataIndex, "user_id", "asset_account_no","real_amount");
        operation.setTraceId(operation.getColumnValue("ext_info#fcTraceId"));

        //资产类型解析
        String assetAccountType = tableData.getColumnIndexValue("asset_account_type", dataIndex);
        operation.setPayToolType(AssetPayToolEnum.getBySubCardType(assetAccountType));
        //账务时间
        String transDt = operation.getColumnValue("trans_dt");
        if(StringUtil.isNotBlank(transDt)) {
            operation.setTransDt(CheckUtil.parseDateTime(transDt));
        }

        //设置余额宝主体
        String masterInstId = operation.getColumnValue("ext_info#currentMasterInstId");
        operation.setCurrentMasterInstId(InstIdEnum.getByMasterInstCode(masterInstId));
        //在展示资金流图时，在节点上展示余额宝主体，如余额宝(天弘)
        operation.setShowCurrentInstIdLabel(true);

        //设置对手方账号
        String opUserId = operation.getColumnValue("ext_info#fcOpUserId");
        if(StringUtil.isNotBlank(opUserId)){
            operation.setOpUserId(opUserId);
        }

        //操作销售渠道
        String instIdCode = tableData.getColumnIndexValue("inst_id", dataIndex);
        operation.setInstId(InstIdEnum.getByFcInstCode(instIdCode));
        //在展示资金流图时，在资金边上展示余额宝流入流出渠道，如：30元,SHUMIJJ流出,3008流入
        operation.setShowInstIdLabel(true);
        switch (tableIndex) {
            case 0:
                operation.setActionType(AssetActionTypeEnum.INCREASE);
                break;
            case 1:
                operation.setActionType(AssetActionTypeEnum.DECREASE);
                break;
            case 2:
                operation.setActionType(AssetActionTypeEnum.FREEZE);
                break;
            case 3:
                operation.setActionType(AssetActionTypeEnum.UNFREEZE);
                break;
            default:
                return null;
        }

        //当操作类型为fundpay_share时，根据扩展字段判断是否属于红包B类户
        if(operation.getPayToolType() == AssetPayToolEnum.YEB){
            String extInfo = operation.getTableData().getColumnIndexValue("ext_info", dataIndex);
            String epMark = CheckUtil.getJsonValue(extInfo, "VOUCHEREPOP",false);
            if(epMark !=null && epMark.equals("Y")){
                operation.setPayToolType(AssetPayToolEnum.YEB_COUPON_B);
            }
        }

        /**
         * 基金业务，需要补FundDummy节点，当fc计增时，可能为基金赎回或撤单到余额宝，补fund计减
         * 当fc计减时，可能为余额宝申购基金，补fund计增
         */
        String yebBizType = operation.getColumnValue("ext_info#yebBizType");
        //DISTRIBUTOR链路，基金赎回或撤单到余额宝
        //DISTRIBUTOR_BRIDGE基金赎回到存款卡，从主卡过桥
        if((tableIndex==0||tableIndex==1) && ("DISTRIBUTOR".equals(yebBizType)||"DISTRIBUTOR_BRIDGE".equals(yebBizType))
                //银行卡大额代扣链路排除
                && !"LARGE_PAY".equals(operation.getColumnValue("ext_info#assetOpertype"))){
            AssetOperation operationOp = operation.clone();
            //基金类的dummy节点，不展示主体
            operationOp.setShowCurrentInstIdLabel(false);
            operationOp.setPayToolType(AssetPayToolEnum.FUND_DUMMY);
            if(tableIndex==0){
                operationOp.setActionType(AssetActionTypeEnum.DECREASE);
            }else{
                operationOp.setActionType(AssetActionTypeEnum.INCREASE);
            }
            this.getAssetOperationData().getOperationList().add(operationOp);
        }
        return operation;
    }
}
