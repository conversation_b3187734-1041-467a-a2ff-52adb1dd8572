package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName AccLogTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/28 12:41
 * @Version V1.0
 **/
public class AccLogEpTask extends AbstractQueryTask {
    public AccLogEpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.ACCTRANS_LOG_EP;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //红包泛金融出资账户，当前固定配置20881021613091590156
                "select * from iw_account_log_15 where payment_id like '${date}%' and (payment_id in(${acc_log_order_list}) or cnl_no in(${acc_log_order_list}))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[1];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[1];
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        String[] extList = tableData.getArrayValueList("ext_params");
        if(extList!=null && extList.length>0){
            for (String ext : extList) {
                if(ext.contains("traceId=")){
                    String traceId = ext.split("traceId=")[1].split("\\^")[0];
                    context.appendAsArrayUnique("trace_id",traceId);
                    break;
                }
            }
        }
    }
}
