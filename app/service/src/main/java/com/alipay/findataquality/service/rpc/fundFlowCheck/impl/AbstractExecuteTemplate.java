package com.alipay.findataquality.service.rpc.fundFlowCheck.impl;

/**
 * @ClassName AbstractExecuteTemplate
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 15:25
 * @Version V1.0
 **/

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.BusinessException;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.BaseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName AbstractExecuteTemplate
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/15 15:59
 * @Version V1.0
 **/
public class AbstractExecuteTemplate <R extends BaseResult,M>{
    private final static Logger LOGGER = LoggerFactory.getLogger("AbstractExecuteTemplate");

    public R doExecute(String sceneDesc,ExecuteInvoke<R,M> invoke){
        R result = invoke.getResultInstance();
        try {
            //1、参数校验
            invoke.checkParam();
            //2、执行业务逻辑
            M model = invoke.execute();
            //3、组装结果
            if(model==null){
                result.setSuccess(false);
                LOGGER.warn("{}业务处理异常，返回模型为空",sceneDesc);
                return result;
            }
            invoke.assembleResult(result,model);
            return result;
        }catch (Exception e){
            if(e instanceof BusinessException){
                BusinessException be = (BusinessException)e;
                result.setResultCode(be.getErrorCode());
                result.setResultDesc(be.getMessage());
                result.setSuccess(false);
                LOGGER.error("{}业务处理异常，异常内容{}",sceneDesc,be);
                return result;
            }
            LOGGER.error("{}AbstractExecuteTemplate通用模板执行异常，异常内容{}",sceneDesc,e);
            result.setSuccess(false);
            return result;
        }
    }

    public static abstract class ExecuteInvoke<R,M>{
        /**
         * 参数校验
         */
        public abstract void checkParam();

        /**
         * 构造结果
         * @return
         */
        public abstract void assembleResult(R result,M model);

        /**
         * 获取结果实例
         * @return
         */
        public abstract R getResultInstance();

        /**
         * 执行业务流程
         * @return
         */
        public abstract M execute();
    }
}
