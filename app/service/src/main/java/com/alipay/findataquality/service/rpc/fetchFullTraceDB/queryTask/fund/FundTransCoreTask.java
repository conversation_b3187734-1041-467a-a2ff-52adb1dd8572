package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import static com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinFundTradeTask.fundCommonSet;

/**
 * @ClassName FinmemberTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 15:26
 * @Version V1.0
 **/
public class FundTransCoreTask extends AbstractQueryTask {
    public FundTransCoreTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FUNDTRANSCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                genSql("fin_trans_order_${db_flag}","order_id",fundCommonSet,new String[]{"out_biz_no"},new String[]{"tc_id","repay_id","reservation_order_id","group_order_id"}),
                genSql("fin_trans_idem_order_${db_flag}","idem_order_id",fundCommonSet,new String[]{"out_biz_no"},new String[]{"trans_order_id"}),
                genSql("fin_trans_sub_order_${db_flag}","sub_order_id",fundCommonSet,null,new String[]{"main_order_id","biz_unique_id","cnl_no"}),
                genSql("fin_trans_repay_order_${db_flag}","repay_id",fundCommonSet,new String[]{"out_biz_no","trans_order_id"},new String[]{"fas_order_no","cnl_no"})

        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "user_id->user_id_list,order_id|out_biz_no|tc_id|repay_id|reservation_order_id|group_order_id->"+fundCommonSet,
                "user_id->user_id_list,idem_order_id|out_biz_no|trans_order_id->"+fundCommonSet,
                "user_id->user_id_list,sub_order_id|main_order_id|biz_unique_id|cnl_no->"+fundCommonSet,
                "user_id->user_id_list,repay_id|out_biz_no|trans_order_id|fas_order_no|cnl_no->"+fundCommonSet
        };
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        //后续fa取数依赖使用
        if(index == 0){
            autoPutFist(tableData, "tc_id", "main_order_id");
        }
        if(index == 3) {
            autoPutFist(tableData, "fas_order_no", "main_order_id");
        }
    }

    @Override
    public void handleContextBefore (QueryContext context) {
        context.copyValueAppend("order_id",fundCommonSet);
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        super.handleContextAfter(context);
        context.copyValueAppend(fundCommonSet,"fa_trans_and_order_list");
    }
}
