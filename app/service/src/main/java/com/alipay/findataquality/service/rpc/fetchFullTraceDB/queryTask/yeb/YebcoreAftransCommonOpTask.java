package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreAftransCommonOpTask extends YebcoreAftransCommonTask {

    public YebcoreAftransCommonOpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String opTask() {
        return "op";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
