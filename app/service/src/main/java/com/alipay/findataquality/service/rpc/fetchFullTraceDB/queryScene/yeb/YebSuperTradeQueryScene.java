package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.AftransLogCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.AftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName YebSuperTradeQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/2/10 11:06
 * @Version V1.0
 **/
public class YebSuperTradeQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public YebSuperTradeQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                YebcoreSuperTradePreTask.class,

                FinancingcoreCommonTask.class,
                YebcoreSuperTradeTask.class,

                FinancingcoreCommonOpTask.class,
                YebcoreSuperTradeOpTask.class,

                MftransLogCommonTask.class,
                MftransLogCommonOpTask.class,
                AftransLogCommonTask.class,
                AftransLogCommonOpTask.class,
                ProdtransTask.class,
                ProdtransOpTask.class,
                ObcloudpayTask.class,
                ObcloudpayOpTask.class,
                AccLogCommonTask.class,
                //AccLogCommonOpTask.class,
                //AccLogEpTask.class,
                //AccLogFundEpTask.class,
                InstpayCommonTask.class,
                InstpayCommonOpTask.class,

                AccLogInnerAccountTask.class,
                AccLogInnerAccount2Task.class,
                AccLogInnerAccount3Task.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{
                0,
                1,1,
                2,2,
                3,3,3,3,3,3,3,3,3,3,3,//3,3,3,
                4,4,4
        };
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.YEB_SUPER_TRADE_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
    }
}
