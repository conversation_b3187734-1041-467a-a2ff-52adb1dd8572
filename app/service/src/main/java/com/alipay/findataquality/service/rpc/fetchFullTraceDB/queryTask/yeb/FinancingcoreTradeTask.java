package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName FinancingcoreTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 11:06
 * @Version V1.0
 **/
public class FinancingcoreTradeTask extends AbstractQueryTask {
    public FinancingcoreTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCINGCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from yeb_asset_increase_order_${db_flag} where asset_order_id like '${date}%' and (biz_no = '${order_no}' or out_biz_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yeb_asset_decrease_order_${db_flag} where asset_order_id like '${date}%' and (biz_no = '${order_no}' or out_biz_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yeb_asset_freeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no = '${order_no}' or out_biz_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yeb_asset_unfreeze_order_${db_flag} where asset_order_id like '${date}%' and (biz_no = '${order_no}' or out_biz_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yeb_asset_instruction_${db_flag} where instruction_id like '${date}%' and asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_batch_mftrans_order_${db_flag} where asset_order_id in (${asset_order_id})"
        };
    }

    @Override
    public String[] outcomeValue() {
        //String keys = "asset_order_id,payment_id,biz_no,ext_info.fcTraceId->trace_id";
        return new String[]{
                "asset_order_id,payment_id,biz_no","asset_order_id,payment_id,biz_no","asset_order_id,payment_id,biz_no","asset_order_id,payment_id,biz_no",null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null};
    }
}
