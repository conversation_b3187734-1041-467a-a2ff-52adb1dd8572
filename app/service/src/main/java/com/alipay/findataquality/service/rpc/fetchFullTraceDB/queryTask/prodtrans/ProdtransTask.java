package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName Prodtrans
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/3 15:49
 * @Version V1.0
 **/
public class ProdtransTask extends AbstractQueryTask {
    public ProdtransTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String[] queryDependency() {
        //"prodtrans_out_biz_no_list"
        return new String[]{"prodtrans_principal_id_list"};
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.PRODTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from prod_log_${prodtrans_db_flag} where  principal_id in (${prodtrans_principal_id_list}) and (out_biz_no in (${prodtrans_out_biz_no_list}) or cnl_no in (${prodtrans_out_biz_no_list}))",
                "select * from prod_flow_record_${prodtrans_db_flag} where  principal_id in (${prodtrans_principal_id_list}) and (out_biz_no in (${prodtrans_out_biz_no_list}) or cnl_no in (${prodtrans_out_biz_no_list}))",
                "select * from prod_lifetime${db_flag} where principal_id in (${prodtrans_principal_id_list_life_time})"
        };
    }

    @Override
    public String[] keyData() {
        return new String[]{
                "principal_id, amount, out_biz_no, sub_product_code, Memo, var2, var4",
                "principal_id, amount, out_biz_no, sub_product_code, data_type, data_sub_type",
                null
        };
    }

    @Override
    public String[] outcomeValue() {
        //在交易场景中，只在产品账存在流水记录时捞取lifetime表数据
        return new String[]{
                "principal_id->prodtrans_principal_id_list_life_time","principal_id->prodtrans_principal_id_list_life_time",null
        };
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{
                OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null
        };
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        String dbFlag = context.getValue("db_flag");
        String date = context.getValue("date");
        String prodtransDbFlag = DbUtils.getProdtransSchemaFromDbFlag(dbFlag,date);
        if(StringUtil.isNotBlank(prodtransDbFlag)){
            context.putGlobal("prodtrans_db_flag",prodtransDbFlag);
        }
    }
}
