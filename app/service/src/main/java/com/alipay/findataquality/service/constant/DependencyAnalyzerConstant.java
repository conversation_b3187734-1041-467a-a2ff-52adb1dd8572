package com.alipay.findataquality.service.constant;

public class DependencyAnalyzerConstant {

    public static final String BAKERY_SCENE_ADD_URL_PROD = "https://bakery.alipay.com/openapi/mockScenes/add";

    public static final String BAKERY_SCENE_QUERY_DETAIL_URL_PROD = "https://bakery.alipay.com/openapi/mockScenes/";

    public static final String BAKERY_TOKEN_PROD = " c25ed809-2b11-4c54-ab9d-6ebfd1c82238";

    public static final String BAKERY_TRANSOUTPAGE_SPRINT_ID = "uiauto";

    public static final String BAKERY_TRANSOUTPAGE_PROJECT_ID = "1c0f21ba-d014-4e67-ad6c-5131dc6ac3fc";

    public static final String BAKERY_TRANSOUTPAGE_PREVIEW_SPRINT_ID= "S090011007433";

    public static final String BAKERY_TRANSOUTPAGE_STARTPAGE= "transfer_out.html";

    public static final String BKLIGHT_EXEC_CASE_TYPE_ETE= "ETE";

    //依赖强弱判断类型：强依赖
    public static final String DEPENDENCY_JUDGE_TYPE_STRONG= "STRONG";
    //依赖强弱判断类型：弱依赖
    public static final String DEPENDENCY_JUDGE_TYPE_weak= "WEAK";

}
