package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * @ClassName PackageUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/29 19:56
 * @Version V1.0
 **/
public class PackageUtils {

    /**
     * 获取指定包路径下的所有类
     *
     * @param packageName 包路径
     * @return 类名列表
     */
    public static List<String> getClasses(String packageName) {
        List<String> classes = new ArrayList<>();
        try {
            ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
            String path = packageName.replace('.', '/');
            Enumeration<URL> resources = classLoader.getResources(path);
            while (resources.hasMoreElements()) {
                URL resource = resources.nextElement();
                File file = new File(resource.getFile());
                if (file.exists()) {
                    classes.addAll(findClasses(file, packageName));
                }
            }
        } catch (IOException e) {
            //e.printStackTrace();
            return null;
        }
        return classes;
    }

    /**
     * 递归查找指定目录下的所有类
     *
     * @param directory 目录
     * @param packageName 包路径
     * @return 类名列表
     */
    private static List<String> findClasses(File directory, String packageName) {
        List<String> classes = new ArrayList<>();
        if (!directory.exists()) {
            return classes;
        }
        File[] files = directory.listFiles();
        if (files == null) {
            return classes;
        }
        for (File file : files) {
            if (file.isDirectory()) {
                classes.addAll(findClasses(file, packageName + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                classes.add(packageName + '.' + file.getName().substring(0, file.getName().length() - 6));
            }
        }
        return classes;
    }

    public static List<String> getAllQuerySceneClassName(){
        List<String> result = new ArrayList<>();
        List<String>classNameList = PackageUtils.getClasses("com.alipay.finqatrans.biz.fetchFullTraceDB.queryScene");
        for (String classPath: classNameList) {
            if(classPath.endsWith("AbstractQueryScene")){
                continue;
            }
            result.add(classPath);
        }
        return result;
    }

}
