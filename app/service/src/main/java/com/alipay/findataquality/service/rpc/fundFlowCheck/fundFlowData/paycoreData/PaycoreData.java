package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.paycoreData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;
import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName PaycoreData
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/12 16:58
 * @Version V1.0
 **/
public class PaycoreData extends AssetRecordData {

    public static final String PMT_COMM_FD_DTL = "obcloudpay.pmt_comm_fd_dtl";

    @Override
    public String[] dataTable() {
        return new String[]{
                PMT_COMM_FD_DTL
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        AssetOperation operation = null;
        switch (tableIndex) {
            case 0:
                /**
                 * 差错户识别
                 */
                String extra = tableData.getColumnIndexValue("fd_dtl_extra",dataIndex);
                String assetType = tableData.getColumnIndexValue("asset_type",dataIndex);
                String assetTypeCode = tableData.getColumnIndexValue("asset_type_code",dataIndex);
                String fundBelong = tableData.getColumnIndexValue("fund_belong",dataIndex);
                String userId = tableData.getColumnIndexValue("user_id",dataIndex);
                String payToolType = tableData.getColumnIndexValue("pay_tool_type",dataIndex);
                //基金专用差错户
                if(StringUtil.isNotBlank(extra)
                        && extra.contains("assignedSlipInnerAccount=true")
                        && "ALIPAYACCOUNT".equals(assetType)
                        && "INNER_BALANCE".equals(assetTypeCode)
                        && "PYEE".equals(fundBelong)){
                    /**
                     * 基金专用差错户
                     * https://finqc-pre.alipay.com/dataValidation?shareCode=20250509074726382369792
                     */
                    operation = AssetOperation.createBaseOperation(tableData, dataIndex, "user_id", "account_no", "amount");
                    operation.setActionType(AssetActionTypeEnum.INCREASE);
                    //基金专用差错户专用两码********************，********，其他为支付宝通用差错户
                    if(extra.contains("detailPdCode=********************")&&extra.contains("detailEvCode=********")){
                        operation.setPayToolType(AssetPayToolEnum.YUE_FUND_SLIP_INNER_ACCOUNT);
                    }else{
                        operation.setPayToolType(AssetPayToolEnum.YUE_COMMON_SLIP_INNER_ACCOUNT);
                    }
                }
                //外部银联机构，资金从支付宝体系->外部银联机构，场景为合花子卡消费，合花子卡->泛金融余额户->外部银联机构
                //https://finqc-pre.alipay.com/dataValidation?shareCode=20250712078880708515840
                if(StringUtil.isNotBlank(extra)
                        && extra.contains("assetInstId=CUP")
                        && "CUP".equals(userId)
                        && "ALIPAYACCOUNT".equals(assetType)
                        && "INST_CLEARING_ACCOUNT".equals(assetTypeCode)
                        && "PYEE".equals(fundBelong)){
                    operation = AssetOperation.createBaseOperation(tableData, dataIndex, "user_id", "account_no", "amount");
                    operation.setActionType(AssetActionTypeEnum.INCREASE);
                    operation.setPayToolType(AssetPayToolEnum.OUTSIDE_INST_CUP);
                }
                //外部银联机构，资金从外部银联机构->支付宝体系，场景为合花子卡消费退款，外部银联机构->泛金融余额户->合花子卡
                //https://finqc-pre.alipay.com/dataValidation?shareCode=20250712078880708515840
                if(StringUtil.isNotBlank(extra)
                        && extra.contains("accountInstId=CUP")
                        && "ALIPAYACCOUNT".equals(payToolType)
                        && "PYER".equals(fundBelong)){
                    operation = AssetOperation.createBaseOperation(tableData, dataIndex, "user_id", "account_no", "amount");
                    operation.setActionType(AssetActionTypeEnum.DECREASE);
                    //https://finqc-pre.alipay.com/dataValidation?shareCode=20250715003535649134592
                    //该情况下，userId数据为空，此处设置为CUP以防止后续绘图userId空指针
                    operation.setUserId("CUP");
                    operation.setPayToolType(AssetPayToolEnum.OUTSIDE_INST_CUP);
                }
                break;
            default:
                return null;
        }
        return operation;
    }
}
