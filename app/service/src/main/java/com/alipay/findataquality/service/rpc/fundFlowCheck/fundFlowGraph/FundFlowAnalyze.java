package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.sofa.common.utils.StringUtil;

import java.util.*;

/**
 * @ClassName FundFlowAnalyze
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 17:33
 * @Version V1.0
 **/
public class FundFlowAnalyze {

    /**
     * 分析资金流节点，形成资金操作左右对，并对资金操作序列根据traceId进行分组
     * @param dataContent
     * @return
     */
    public List<TransactionGroup> analyzeFundFlowGroup(String dataContent){
        List<AssetTransaction> list = analyzeFundFlow(dataContent);
        if(list==null){
            return null;
        }
        return partitionTransactionGroup(list);
    }

    /**
     * 对资金交易节点进行资金流程分组，如余额宝买基金、买基金撤单，会分为两个资金过程
     * @param transactionList
     * @return
     */
    public List<TransactionGroup> partitionTransactionGroup(List<AssetTransaction> transactionList){
        List<TransactionGroup> transactionGroups = new ArrayList<>();
        if(transactionList.isEmpty()){
            return transactionGroups;
        }
        //对transaction进行分组
        String preTraceId = transactionList.get(0).getTraceId();
        for (AssetTransaction transaction : transactionList) {
            if(StringUtil.isNotBlank(preTraceId) && StringUtil.isNotBlank(transaction.getTraceId())
                    && !preTraceId.equals(transaction.getTraceId())){
                //traceId和pre不同时，新建一个分组
                TransactionGroup group = new TransactionGroup();
                group.addTransaction(transaction);
                transactionGroups.add(group);
            }else{
                if(transactionGroups.isEmpty()){
                    TransactionGroup group = new TransactionGroup();
                    group.addTransaction(transaction);
                    transactionGroups.add(group);
                }else{
                    //traceId和pre没有不同时，直接追加至最新一个分组
                    transactionGroups.get(transactionGroups.size()-1).addTransaction(transaction);
                }
            }
            preTraceId = transaction.getTraceId();
        }
        //更新每个资金group对应的画图展示
        for (TransactionGroup group:transactionGroups) {
            group.covertTransactionToGraph();
        }
        return transactionGroups;
    }

    /**
     * 分析资金流
     * @param dataContent
     * @return
     */
    public List<AssetTransaction> analyzeFundFlow(String dataContent){
        FundFlowTransGraph graph = new FundFlowTransGraph();
        TableDataContext dataContext = new TableDataContext();
        dataContext.initFromContent(dataContent,graph.getFundFlowClasses(),null);//修改：此处不需要transactionGroups，因此值为空
        List<AssetOperation> operationListAll = new ArrayList<>();
        Set<Class> keys = dataContext.getTableDataMapByClass().keySet();
        for (Class key: keys) {
            AssetRecordData recordData = dataContext.getTableDataMapByClass().get(key);
            operationListAll.addAll(recordData.getAssetOperationData().getOperationList());
        }
        operationListAll.sort(Comparator.comparing(AssetOperation::getGmtCreate));

        List<AssetTransaction> transactionList = graph.calculateTransPath(operationListAll);
        return transactionList;
    }
}
