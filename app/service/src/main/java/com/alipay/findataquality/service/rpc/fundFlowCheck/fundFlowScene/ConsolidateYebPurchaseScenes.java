package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import com.iwallet.biz.common.util.money.Money;

import java.util.LinkedList;
import java.util.List;

/**
 * 组合余额宝买入的交易阶段场景，最终得到组合场景
 */
public class ConsolidateYebPurchaseScenes implements ConsolidateScenes{
    @Override
    public TransactionComposedScene consolidateStageScenes(List<TransactionStageScene> transactionStageScenes) {
        TransactionComposedScene transactionComposedScene = new TransactionComposedScene();
        StringBuffer composedSceneDesc = new StringBuffer();
        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
        Money sumAmount = new Money(0);
        int sceneLength = 0;
        for (TransactionStageScene transactionStageScene:transactionStageScenes){
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.BANK_TO_YEB){
                composedSceneDesc.append("+银行卡");
                transactionStageSceneList.add(transactionStageScene);
                sumAmount = sumAmount.add(transactionStageScene.getAmount());
                sceneLength++;
            }
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.COUPON_TO_YEB){
                composedSceneDesc.append("+红包");
                transactionStageSceneList.add(transactionStageScene);
                sumAmount = sumAmount.add(transactionStageScene.getAmount());
                sceneLength++;
            }
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.YEB_CKK_TO_YEB){
                composedSceneDesc.append("+余额宝攒钱卡");
                transactionStageSceneList.add(transactionStageScene);
                sumAmount = sumAmount.add(transactionStageScene.getAmount());
                sceneLength++;
            }
        }
        if (sceneLength == 0){
            return null;
        } else {
            composedSceneDesc.deleteCharAt(0);//删除第一个"+"
            composedSceneDesc.append("申购余额宝");//拼接该类实现的业务目标
        }
        //组装结果
        StringBuffer composedSceneDescFinalBuffer = new StringBuffer();
        composedSceneDescFinalBuffer.append("[金额").append(sumAmount).append("元]").append(composedSceneDesc).append("\n");
        transactionComposedScene.setComposedSceneDesc(composedSceneDescFinalBuffer.toString());
        transactionComposedScene.setTransactionStageSceneList(transactionStageSceneList);
        transactionComposedScene.setSumAmount(sumAmount);
        return transactionComposedScene;

    }
}
