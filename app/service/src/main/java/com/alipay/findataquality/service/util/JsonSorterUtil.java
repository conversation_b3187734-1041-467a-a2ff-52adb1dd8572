package com.alipay.findataquality.service.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.TreeMap;

public class JsonSorterUtil{

//    public static void main(String[] args) {
//        String originalJson = "{\n" +
//                "        \"gmt_create\": \"2024-11-26 17:04:06.0\",\n" +
//                "        \"biz_dt\": \"2024-11-26 17:02:34.0\",\n" +
//                "        \"memo\": \"红包奖励发放\",\n" +
//                "        \"biz_context\": \"{}\",\n" +
//                "        \"gmt_modified\": \"2024-11-26 17:04:08.0\",\n" +
//                "        \"cnl_pd_code\": \"UR220100100000000001\",\n" +
//                "        \"biz_pd_code\": \"UR220100100000000001\",\n" +
//                "        \"fund_code\": \"050003\",\n" +
//                "        \"sub_biz_type\": \"060001\",\n" +
//                "        \"biz_request_id\": \"3f82afa33485a61b469a20d961426d29\",\n" +
//                "        \"cnl_ev_code\": \"22010002\",\n" +
//                "        \"biz_no\": \"20241126009350001010670000441986\",\n" +
//                "        \"payment_id\": \"2024112630000000670640277716\",\n" +
//                "        \"business_type\": \"T1_CHANGE\",\n" +
//                "        \"clear_dt\": \"2024-11-26 17:02:34.0\",\n" +
//                "        \"inst_id\": \"SHUMIJJ\",\n" +
//                "        \"trans_dt\": \"2024-11-26 17:04:06.538\",\n" +
//                "        \"pd_code\": \"********************\",\n" +
//                "        \"ev_code\": \"********\",\n" +
//                "        \"asset_account_type\": \"fundpay_share\",\n" +
//                "        \"asset_account_no\": \"60102277515666710156\",\n" +
//                "        \"biz_type\": \"100\",\n" +
//                "        \"bill_detail_id\": \"2024112630000109670614035783\",\n" +
//                "        \"fund_inst\": \"BSFDE2CN\",\n" +
//                "        \"out_biz_no\": \"20241126009350001010670000443234\",\n" +
//                "        \"ext_info\": \"{\\\"assetOpertype\\\":\\\"T1_CHANGE\\\",\\\"payerAssetTypeCode\\\":\\\"ALIPAY_FINANCING_TRANS_VOUCHER\\\",\\\"fcOpUserId\\\":\\\"***********87094\\\",\\\"fcOpSaleInst\\\":\\\"SHUMIJJ\\\",\\\"orgPmtDt\\\":\\\"*************\\\",\\\"yebBizType\\\":\\\"FCTRANSFER\\\",\\\"BIZ_MODE\\\":\\\"FP_SENIOR\\\",\\\"BIZ_PROD\\\":\\\"fncpay20003\\\",\\\"mfProdkey\\\":\\\"N\\\",\\\"payeeAssetTypeCode\\\":\\\"MONEY_FUND\\\",\\\"fundSource\\\":\\\"FUND\\\",\\\"fcOpCode\\\":\\\"050003\\\",\\\"strategyConsultMark\\\":\\\"Y\\\",\\\"fcTraceId\\\":\\\"0b7c8f1a1732611843506343933389\\\",\\\"payeeAssetType\\\":\\\"MONEYFUND\\\",\\\"currentMasterInstId\\\":\\\"SHUMIJJ\\\",\\\"VOUCHERCUSTOP\\\":\\\"Y\\\",\\\"transType\\\":\\\"voucherTrans\\\",\\\"transMode\\\":\\\"Y\\\",\\\"payerAssetType\\\":\\\"VOUCHER\\\",\\\"BIZ_ACTION_TYPE\\\":\\\"PAY\\\",\\\"mybankCheckNo\\\":\\\"20241126009350001010670000441986\\\",\\\"fcAcType\\\":\\\"fundpay_share\\\",\\\"fcHighAvailable\\\":\\\"T\\\"}\",\n" +
//                "        \"pmt_dt\": \"2024-11-26 17:02:34.0\",\n" +
//                "        \"cnl_no\": \"20241126009350003010670000346052\",\n" +
//                "        \"gmt_commit\": \"2024-11-26 17:04:08.492241\",\n" +
//                "        \"asset_order_id\": \"20241126000940091000670012226941\",\n" +
//                "        \"real_amount\": \"100\",\n" +
//                "        \"user_id\": \"****************\",\n" +
//                "        \"biz_ev_code\": \"********\",\n" +
//                "        \"status\": \"S\"\n" +
//                "      }";
//
//        // 转换为JSONObject并排序
//        JSONObject sortedJsonObject = sortJsonObject(JSON.parseObject(originalJson));
//
//        // 直接使用排序后的JSONObject
//        System.out.println("最终得到的JSONObject类型：" + sortedJsonObject.getClass().getName());
//        System.out.println("对象结构验证：\n" + sortedJsonObject.toJSONString());
//
//        // 获取排序后的属性（按字母顺序）
//        System.out.println("\n第一个属性键：" + sortedJsonObject.keySet().iterator().next());
//
//
//    }


    /**
     * 递归排序JSONObject的核心方法
     * @param jsonObject 原始JSON对象
     * @return 排序后的新JSONObject对象
     */
    public static JSONObject sortJsonObject(JSONObject jsonObject) {
        // 使用TreeMap实现键排序
        TreeMap<String, Object> sortedMap = new TreeMap<>();

        jsonObject.forEach((key, value) -> {
            // 递归处理不同类型的值
            sortedMap.put(key, processValue(value));
        });

        // 创建新的JSONObject并保持排序
        return new JSONObject(true) {{
            putAll(sortedMap);
        }};
    }

    private static Object processValue(Object value) {
        if (value instanceof JSONObject) {
            return sortJsonObject((JSONObject) value);
        } else if (value instanceof JSONArray) {
            return processArray((JSONArray) value);
        }
        return value;
    }

    private static JSONArray processArray(JSONArray array) {
        JSONArray newArray = new JSONArray(array.size());
        array.forEach(item -> {
            if (item instanceof JSONObject) {
                newArray.add(sortJsonObject((JSONObject) item));
            } else if (item instanceof JSONArray) {
                newArray.add(processArray((JSONArray) item));
            } else {
                newArray.add(item);
            }
        });
        return newArray;
    }
}
