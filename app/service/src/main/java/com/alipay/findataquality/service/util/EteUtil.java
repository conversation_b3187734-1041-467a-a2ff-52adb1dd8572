package com.alipay.findataquality.service.util;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.EteExecResultDTO;

public class EteUtil {

    /**
     * 对接执行平台执行用例模板。
     * @return 通用结果描述对象，相关详情在其message信息中
     * @throws Exception
     */
    public static EteExecResultDTO executeEteTest(String url, String req) {
        String response = null;
        try {
            response = HttpUtil.sendHttpPostCommon(url,req);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        EteExecResultDTO eteExecResultDTO = new EteExecResultDTO();

        //结果解析与校验
        JSONObject responseObj = JSONObject.parseObject(response);
        String traceId = responseObj.getString("traceId");
        String dataStr = responseObj.getString("data");
        JSONObject dataObj = JSONObject.parseObject(dataStr);
        String taskUrl = dataObj.getString("taskUrl");
        if (!taskUrl.startsWith("https") && taskUrl.startsWith("http")) {
            //将http替换成https
            taskUrl = taskUrl.replace("http", "https");
        }
        String taskId = dataObj.getString("taskId");
        String[] paramlist = taskUrl.split("&");
        String flowRecordCode = "";
        for (String param:paramlist){
            if (param.contains("flowRecordCode")){
                flowRecordCode = param.split("=")[1];
            }
        }
        eteExecResultDTO.setFlowRecordCode(flowRecordCode);
        eteExecResultDTO.setMessage(String.format("执行中，稍后重试，执行链接:%s", taskUrl));
        eteExecResultDTO.setTaskUrl(taskUrl);
        eteExecResultDTO.setTaskId(taskId);
        eteExecResultDTO.setTraceId(traceId);
        eteExecResultDTO.setResponse(response);
        eteExecResultDTO.setSuccess(!StringUtil.isEmpty(flowRecordCode));
        return eteExecResultDTO;
    }

}
