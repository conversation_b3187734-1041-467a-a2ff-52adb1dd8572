package com.alipay.findataquality.service.repository;

import com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceSameOriginNotCoreDOExtendMapper;
import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginNotCoreDO;
import com.alipay.findataquality.service.dto.FullTraceSameOriginNotCoreDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class FullTraceSameOriginNoCoreConfigRepositoryImpl implements FullTraceSameOriginNoCoreConfigRepository{

    private static final Logger logger = LoggerFactory.getLogger(FullTraceSameOriginNoCoreConfigRepository.class);

    @Autowired(required = false)
    private FullTraceSameOriginNotCoreDOExtendMapper fullTraceSameOriginNotCoreDOExtendMapper;

    @Override
    public FullTraceSameOriginNotCoreDTO selectByBusinessRegionAndDbNameAndTableName(String businessResigion, String dbName, String tableName) {
        FullTraceSameOriginNotCoreDO fullTraceSameOriginNotCoreDO = fullTraceSameOriginNotCoreDOExtendMapper.selectByBusinessRegionAndDbNameAndTableName(businessResigion,dbName,tableName);
        if (fullTraceSameOriginNotCoreDO == null){
            logger.warn("fullTraceSameOriginNotCoreDOExtendMapper.selectByBusinessRegionAndDbNameAndTableName() 查询结果为空，businessRegion:{0},dbName:{1},tableName:{2}", businessResigion, dbName, tableName);
            return null;
        }
        return convertDO2DTO(fullTraceSameOriginNotCoreDO);
    }

    private FullTraceSameOriginNotCoreDTO convertDO2DTO(FullTraceSameOriginNotCoreDO doObj) {
        FullTraceSameOriginNotCoreDTO dto = new FullTraceSameOriginNotCoreDTO();
        BeanUtils.copyProperties(doObj, dto);
        return dto;
    }
}
