package com.alipay.findataquality.service.repository;

import com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceSameOriginValidateBaselineDOExtendMapper;
import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginValidateBaselineDO;
import com.alipay.findataquality.service.dto.FullTraceSameOriginValidateBaselineDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class FullTraceSameOriginValidateBaselineRepositoryImpl implements FullTraceSameOriginValidateBaselineRepository {

    private static final Logger logger = LoggerFactory.getLogger(FullTraceValidateDataFetchRecordRepository.class);

    @Autowired(required = false)
    private FullTraceSameOriginValidateBaselineDOExtendMapper fullTraceSameOriginValidateBaselineDOExtendMapper;

    @Override
    public List<FullTraceSameOriginValidateBaselineDTO> queryList(String businessRegion, String tableName, String businessCode) {
        List<FullTraceSameOriginValidateBaselineDO> fullTraceSameOriginValidateBaselineDOS = fullTraceSameOriginValidateBaselineDOExtendMapper.selectByBusinessRegionAndTableNameAndBusinessCode(businessRegion,tableName,businessCode);
        if(fullTraceSameOriginValidateBaselineDOS == null) {
            logger.warn("fullTraceSameOriginValidateBaselineDOMapper.selectByBusinessRegionAndTableNameAndBusinessCode() 查询结果为空，businessRegion:{0},tableName:{1},businessCode:{2}", businessRegion, tableName, businessCode);
            return null;
        }
        return fullTraceSameOriginValidateBaselineDOS.stream().map(this::convertDO2DTO).collect(Collectors.toList());
    }

    private FullTraceSameOriginValidateBaselineDTO convertDO2DTO(FullTraceSameOriginValidateBaselineDO doObj) {
        FullTraceSameOriginValidateBaselineDTO dto = new FullTraceSameOriginValidateBaselineDTO();
        BeanUtils.copyProperties(doObj, dto);
        return dto;
    }

}
