package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;

/**
 * @ClassName MftransData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:20
 * @Version V1.0
 **/
public class MftransData extends AssetRecordData {
    public static final String LOG = "mftrans.mini_account_log_mf";
    public static final String FREEZE_LOG = "mftrans.mini_account_freeze_log_mf";

    @Override
    public String[] dataTable() {
        return new String[]{
                LOG,
                FREEZE_LOG
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        return null;
    }
}
