package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay.InstpayCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransLogCommonTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore.ObcloudpayTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransOpTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName YebTransInQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/24 10:32
 * @Version V1.0
 **/
public class YebSubcardTradeQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public YebSubcardTradeQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                YebcoreSubcardTradePreTask.class,

                FinancingcoreCommonTask.class,
                FinancingcoreCommonOpTask.class,

                YebcoreSubcardTradeTask.class,
                YebcoreSubcardTradeOpTask.class,
                MftransLogCommonTask.class,
                MftransLogCommonOpTask.class,
                ProdtransTask.class,
                ProdtransOpTask.class,
                ObcloudpayTask.class,
                ObcloudpayOpTask.class,
                AccLogCommonTask.class,
                //AccLogCommonOpTask.class,
                InstpayCommonTask.class,
                InstpayCommonOpTask.class,

                AccLogInnerAccountTask.class,
                AccLogInnerAccount2Task.class,
                AccLogInnerAccount3Task.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{
                0,
                1,1,
                2,2,2,2,2,2,2,2,2,2,2,//2,
                3,3,3
        };
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.YEB_SUBCARD_TRADE_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
    }
}
