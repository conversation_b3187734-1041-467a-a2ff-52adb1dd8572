/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2016 All Rights Reserved.
 */
package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import com.alibaba.common.lang.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static java.util.concurrent.TimeUnit.*;

/**
 * 
 * <AUTHOR>
 * @version $Id: DateUtil.java, v 0.1 2016-12-21 ����9:51:53 fuyu.fy Exp $
 */
public class DateUtil {
    private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

    public static final int SECONDS_PER_MINUTE = 60;
    public static final int SECONDS_PER_HOUR = 3600;
    public static final int SECONDS_PER_DAY = 86400;

    private static ThreadLocal<SimpleDateFormat>                    SDF_DATE              = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "yyyy-MM-dd");
        }
    };

    private static ThreadLocal<SimpleDateFormat>                    SDF_DATETIME          = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "yyyy-MM-dd HH:mm:ss");
        }
    };

    private static ThreadLocal<SimpleDateFormat>                    SDF_SHORTDATETIME     = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "yyyy-MM-dd HH:mm");
        }
    };
    private static ThreadLocal<SimpleDateFormat>                    SDF_TIME              = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "HH:mm:ss");
        }
    };
    private static ThreadLocal<SimpleDateFormat>                    SDF_SIMPLEDATETIME    = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "yyyyMMddHHmm");
        }
    };
    private static ThreadLocal<SimpleDateFormat>                    SDF_HOURMIN           = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat(
                    "HH:mm");
        }
    };

    private static final Map<String, ThreadLocal<SimpleDateFormat>> SIMPLE_DATE_FORMATERS = new HashMap<String, ThreadLocal<SimpleDateFormat>>();
    static {
        SIMPLE_DATE_FORMATERS.put("yyyy-MM-dd", SDF_DATE);
        SIMPLE_DATE_FORMATERS.put("yyyy-MM-dd HH:mm:ss", SDF_DATETIME);
        SIMPLE_DATE_FORMATERS.put("yyyy-MM-dd HH:mm", SDF_SHORTDATETIME);
        SIMPLE_DATE_FORMATERS.put("HH:mm:ss", SDF_TIME);
        SIMPLE_DATE_FORMATERS.put("yyyyMMddHHmm", SDF_SIMPLEDATETIME);
        SIMPLE_DATE_FORMATERS.put("HH:mm", SDF_HOURMIN);
    }

    public static final String                                      CRON_BLANK            = " ";
    public static final String                                      CRON_STAR             = "*";
    public static final String                                      CRON_QUESTION         = "?";

    private static SimpleDateFormat getCachedDateFormat(String aMask) {
        return SIMPLE_DATE_FORMATERS.get(aMask).get();
    }

    /**
     * 返回自字符串格式为：yyyy-MM-dd
     */
    public static String formatDate(Date aDate) {
        return format("yyyy-MM-dd", aDate);
    }

    /**
     * 返回自字符串格式为：yyyy-MM-dd HH:mm:ss
     */
    public static String formatDateTime(Date date) {
        return format("yyyy-MM-dd HH:mm:ss", date);
    }

    /**
     * 返回自字符串格式为：HH:mm:ss
     */
    public static String formatTime(Date date) {
        return format("HH:mm:ss", date);
    }

    /**
     * 返回自定义格式字符串
     */
    public static String format(String aMask, Date aDate) {
        if (aDate == null) {
            return null;
        } else {
            SimpleDateFormat sd = getCachedDateFormat(aMask);
            if (sd == null) {
                sd = new SimpleDateFormat(aMask);
                return sd.format(aDate);
            }
            synchronized (sd) {
                return sd.format(aDate);
            }
        }
    }

    /**
     * 只支持格式yyyy-MM-dd
     */
    public static Date parseDate(String strDate) {
        try {
            return parse("yyyy-MM-dd", strDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 只支持格式yyyy-MM-dd HH:mm:ss
     */
    public static Date parseDateTime(String strDate) {
        try {
            return parse("yyyy-MM-dd HH:mm:ss", strDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 只支持格式HH:mm:ss
     */
    public static Date parseTime(String timeOnly) {
        timeOnly = formatDate(new Date()) + " " + timeOnly;
        try {
            return parse("yyyy-MM-dd HH:mm:ss", timeOnly);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 只支持格式yyyyMMddHHmmss
     */
    public static Date parseMinDate(String strDate) {
        try {
            return parse("yyyyMMddHHmmss", strDate);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 支持自定义格式转换
     */
    public static Date parse(String aMask, String strDate) throws ParseException {
        if (strDate == null) {
            return null;
        } else {
            SimpleDateFormat sd = getCachedDateFormat(aMask);
            if (sd == null) {
                sd = new SimpleDateFormat(aMask);
                return sd.parse(strDate);
            }
            synchronized (sd) {
                return sd.parse(strDate);
            }
        }
    }

    /**
     * 年4位，月、日、时、分、秒各2位，毫秒3位，支持从右边删除任意部分，如：只有年月日，或者只有年月等
     */
    public static Date parse(String strDate) throws ParseException {
        if (strDate == null) {
            return null;
        } else {
            strDate = strDate.replace("-", "").replace(".", "").replace("/", "").replace("\\", "")
                    .replace(" ", "").replace(":", "");
            String formate = "yyyyMMddHHmmssSSS";
            formate = formate.substring(0, strDate.length());
            return new SimpleDateFormat(formate).parse(strDate);
        }
    }

    public static String getTimeInteval(Date date) {
        if (null == date) {
            return "";
        }
        int hours, minutes, seconds;
        long timeMillSeconds = System.currentTimeMillis() - date.getTime();
        hours = (int) (timeMillSeconds / (60 * 60 * 1000));
        timeMillSeconds = timeMillSeconds - (hours * 60 * 60 * 1000);
        minutes = (int) (timeMillSeconds / (60 * 1000));
        timeMillSeconds = timeMillSeconds - (minutes * 60 * 1000);
        seconds = (int) (timeMillSeconds / 1000);
        String inteval = "";
        if (hours > 0) {
            inteval = hours + "小时" + minutes + "分" + seconds + "秒";
        } else if (minutes > 0) {
            inteval = minutes + "分" + seconds + "秒";
        } else {
            inteval = seconds + "秒";
        }
        return inteval;
    }

    public static Date addYears(Date date, int amount) {
        return add(date, Calendar.YEAR, amount);
    }

    public static Date addMonths(Date date, int amount) {
        return add(date, Calendar.MONTH, amount);
    }

    public static Date addWeeks(Date date, int amount) {
        return add(date, Calendar.WEEK_OF_YEAR, amount);
    }

    public static Date addDays(Date date, int amount) {
        return add(date, Calendar.DAY_OF_YEAR, amount);
    }

    public static Date addHours(Date date, int amount) {
        return add(date, Calendar.HOUR_OF_DAY, amount);
    }

    public static Date addMinutes(Date date, int amount) {
        return add(date, Calendar.MINUTE, amount);
    }

    public static Date addSeconds(Date date, int amount) {
        return add(date, Calendar.SECOND, amount);
    }

    public static String getTodayString() {
        return format("yyyyMMdd", new Date());
    }

    private static Date add(Date date, int calendarField, int amount) {
        if (date == null) {
            throw new IllegalArgumentException("The date must not be null");
        } else {
            Calendar c = Calendar.getInstance();
            c.setTime(date);
            c.add(calendarField, amount);
            return c.getTime();
        }
    }

    public static String nextDate(String strdate) throws ParseException {
        Date temp = parse("yyyy-MM-dd", strdate);
        Date next = new Date(temp.getTime() + 1 * 24 * 3600 * 1000);
        return formatDate(next);
    }

    public static int getYear(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        return cal.get(Calendar.YEAR);
    }

    public static final String appendTime(String date) {
        if (date != null && date instanceof String) {
            //2014-07-01
            if (date.length() >= 8 && date.length() <= 10
                    && date.indexOf("-") != date.lastIndexOf("-")) {
                date = date + " 23:59:59.999";
            }
        }
        return date;
    }

    /**
     * 获取凌晨00:00:00
     *
     * @param date
     * @return
     * <AUTHOR>
     */
    public static Date getMorning(Date date) {
        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);
        dateCalendar.set(Calendar.HOUR_OF_DAY, 0);
        dateCalendar.set(Calendar.MINUTE, 0);
        dateCalendar.set(Calendar.SECOND, 0);
        dateCalendar.set(Calendar.MILLISECOND, 0);

        return dateCalendar.getTime();
    }

    /**
     * 获得 某天的23:59:59
     * @param date
     * @return
     * <AUTHOR>
     */
    public static Date getNight(Date date) {
        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);
        dateCalendar.set(Calendar.HOUR_OF_DAY, 23);
        dateCalendar.set(Calendar.MINUTE, 59);
        dateCalendar.set(Calendar.SECOND, 59);
        dateCalendar.set(Calendar.MILLISECOND, 0);

        return dateCalendar.getTime();
    }

    public static String formatCurrentMs(String format) {
        String ret = null;
        long currentMs = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date date = new Date(currentMs);
        try {
            ret = sdf.format(date);
        } catch (Exception e) {
            logger.error("e={}",e);
        }
        return ret;
    }

    /**
     * 功能：根据指定的输入字符串日期，解析为通用的Date格式
     *
     * 如：输入为：2015-06-1
     *    输出为： Date表达
     * 注意：解析格式一旦异常或者输入为空，那么就返回当前时间
     * @param day
     * @return
     */
    public static Date getDayStartTime(String day) {
        try {
            if (StringUtil.isBlank(day)) {
                Date date = new Date();
                return SDF_DATE.get().parse(SDF_DATE.get().format(date));
            }
            return SDF_DATE.get().parse(day);
        } catch (ParseException e) {
            return new Date();
        }
    }

    //    public static Date getDayStartTime() {
    //        return getDayStartTime(new Date());
    //    }
    //
    //    public static Date getDayStartTime(Date day){
    //        return new Date(day.getTime() - 1000 * 60 * 60 * 24);
    //    }

    /**
     * 功能：根据输入的当前时间，返回一天后的时间
     *
     * 如：输入的为2015-06-1
     *    输出为：2015-06-02
     *
     * @param startTime
     * @return
     */
    public static Date getDayEndTime(Date startTime) {
        return new Date(startTime.getTime() + (3600 * 1000 * 24));
    }

    public static List<Date> getOffsetDates(Date curDate, int offsetDays, String type) {
        List<Date> dates = new ArrayList<Date>();
        if (StringUtil.equals("pre", type)) {
            for (int i = offsetDays; i >= 1; i--) {
                Date preDate = DateUtil.addDays(curDate, -i);
                dates.add(preDate);
            }
        } else if (StringUtil.equals("post", type)) {
            for (int i = 1; i <= offsetDays; i++) {
                Date postDate = DateUtil.addDays(curDate, i);
                dates.add(postDate);
            }
        }
        return dates;
    }

    public static int getMinIndexInDay(Date date) {
        int index = -1;
        try {
            Date morning = DateUtil.getMorning(date);
            index = (int) ((date.getTime() - morning.getTime()) / 60000L);
        } catch (Exception e) {
            index = -1;
        }
        return index;
    }

    public static long getTimeEraseSecs(Date date) {
        return date.getTime() / 60000 * 60000;
    }

    public static long getTimeEraseSecs(long date) {
        return date / 60000 * 60000;
    }

    public static Date parseUtcToLocal(String utcTime) {
        Date d = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            d = sdf.parse(utcTime);
        } catch (Exception e) {
            logger.error("e={}",e);
        }
        return d;
    }

    public static String formatLocalToUtc(Date date) {
        String utcTime = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            utcTime = sdf.format(date);
        } catch (Exception e) {
            logger.error("e={}",e);
        }
        return utcTime;
    }

    public static Date before(int times, Date date, TimeUnit timeUnit) {
        long d = date.getTime() / 1000;

        if (timeUnit == SECONDS) {
            return new Date((d - times) * 1000);
        } else if (timeUnit == MINUTES) {
            return new Date((d - times * 60) * 1000);
        } else if (timeUnit == HOURS) {
            return new Date((d - times * 60 * 60) * 1000);
        } else if (timeUnit == DAYS) {
            return new Date((d - times * 60 * 60 * 24) * 1000);
        }

        throw new RuntimeException("only support TimeUnit.SECONDS, TimeUnit.MINUTES, TimeUnit.HOURS!");
    }

    public static int toUnixTime(long millseconds) {
        return (int) TimeUnit.MILLISECONDS.toSeconds(millseconds);
    }

    public static int toUnixTimeOfCeil(long millseconds) {
        return (int) Math.ceil(millseconds / 1000.0D);
    }

    public static String getFormatDate(long millis, String pattern) {
        if (pattern == null || pattern.isEmpty()) {
            pattern = "yyyyMMdd";
        }
        SimpleDateFormat df = new SimpleDateFormat(pattern);
        return df.format(new Date(millis));
    }
}
