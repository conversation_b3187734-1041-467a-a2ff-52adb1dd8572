package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName MftransTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 13:51
 * @Version V1.0
 **/
public class AftransLogCommonTask extends AbstractQueryTask {
    public AftransLogCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.AFTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from mini_account_log_af_${db_flag} where mini_trans_log_id like '${date}%' and (order_no in (${mftrans_combine_order_list}) or out_biz_no in (${mftrans_combine_order_list}))",
                "select * from mini_account_freeze_log_af_${db_flag} where id like '${date}%' and order_no in (${mftrans_combine_order_list})",

                "select * from mini_account_af_${db_flag} where mini_account_no in (${af_account_no_trd_list})",
                "select * from mini_account_freeze_af_${db_flag} where mini_account_no in (${af_account_no_trd_list})",
                "select * from mini_main_account_af_${db_flag} where MINI_MAIN_ACCOUNT_NO in (${af_main_account_no_list})"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{"account_no->af_account_no_trd_list","mini_account_no->af_account_no_trd_list","mini_main_account_no->af_main_account_no_list",null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{
                OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND
        };
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
