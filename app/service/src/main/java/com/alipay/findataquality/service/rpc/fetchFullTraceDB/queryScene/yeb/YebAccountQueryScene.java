package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.CifCustAssetTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.FinmemberTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif.ObcifTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinfundprotocolTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.AftransAccountTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans.MftransAccountTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans.ProdtransTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb.YebcoreAccountTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;


/**
 * @ClassName YebAccountQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 10:06
 * @Version V1.0
 **/
public class YebAccountQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public YebAccountQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                YebcoreAccountTask.class,
                CifCustAssetTask.class,
                FinmemberTask.class,
                ObcifTask.class,
                FinfundprotocolTask.class,
                MftransAccountTask.class,
                ProdtransTask.class,
                AftransAccountTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,0,0,0,0,1,1,1};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.YEB_ACCOUNT_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,3,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,3,1));
    }
}
