package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import com.iwallet.biz.common.util.money.Money;

import java.util.LinkedList;
import java.util.List;

/**
 * 组合基金撤单后资金回余额宝的交易阶段场景，最终得到组合场景
 * -- 此处默认基金撤单回余额宝在一笔完整场景中不存在发生多次
 */
public class ConsolidateFundCancelToYebScenes implements ConsolidateScenes{
    @Override
    public TransactionComposedScene consolidateStageScenes(List<TransactionStageScene> transactionStageScenes) {
        TransactionComposedScene transactionComposedScene = new TransactionComposedScene();
        StringBuffer composedSceneDesc = new StringBuffer();
        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
        Money sumAmount = new Money(0);
        int transactionTimes = 0;

        for (TransactionStageScene transactionStageScene:transactionStageScenes){
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL){
                composedSceneDesc.append("基金撤单后资金退回余额宝");
                sumAmount = transactionStageScene.getAmount();
                transactionStageSceneList.add(transactionStageScene);
                transactionTimes++;
                break;
            }
        }
        if (transactionTimes==0){
            return null;
        }else{
            //组装结果
            StringBuffer composedSceneDescFinalBuffer = new StringBuffer();
            composedSceneDescFinalBuffer.append("[金额").append(sumAmount).append("元]").append(composedSceneDesc).append("\n");
            transactionComposedScene.setComposedSceneDesc(composedSceneDescFinalBuffer.toString());
            transactionComposedScene.setSumAmount(sumAmount);
            transactionComposedScene.setTransactionStageSceneList(transactionStageSceneList);
            return transactionComposedScene;
        }
    }
}
