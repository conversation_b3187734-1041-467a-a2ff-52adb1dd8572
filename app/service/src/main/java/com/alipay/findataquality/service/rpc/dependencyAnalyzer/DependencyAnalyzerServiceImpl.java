package com.alipay.findataquality.service.rpc.dependencyAnalyzer;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.DependencyAnalyzerService;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.BklightMockData;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.DependencyAnalyzerModel;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result.DependencyAnalyzerResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;
import com.alipay.findataquality.service.dto.dependencyAnalyzer.*;
import com.alipay.sofa.rpc.api.annotation.RpcProvider;
import com.alipay.tfapi.CreateJobRequest;
import org.apache.commons.lang.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

import static com.alipay.findataquality.service.constant.DependencyAnalyzerConstant.*;
import static com.alipay.findataquality.service.constant.DependencyAnalyzerConstant.BKLIGHT_EXEC_CASE_TYPE_ETE;
import static com.alipay.findataquality.service.util.HttpUtil.sendHttpGetForUIMock;
import static com.alipay.findataquality.service.util.HttpUtil.sendHttpPostForUIMock;


@RpcProvider
public class DependencyAnalyzerServiceImpl implements DependencyAnalyzerService {

    private static final Logger logger = LoggerFactory.getLogger(DependencyAnalyzerService.class);

//    @Autowired
//    private JobFacade jobFacade;

    /**
     * 依赖分析服务入口：转出页改版示例-https://bakery.alipay.com/bakery/yuebao/yeb-trade/sprints/S090011007433/uiauto_scene
     * @param bklightMockDataList
     * @return
     */
    @Override
    public DependencyAnalyzerResult analysisDependency(List<BklightMockData> bklightMockDataList) {
        DependencyAnalyzerResult result = new DependencyAnalyzerResult();
        //处理返回值
        //todo:从配置db里面获取scene信息
        String sceneId = StrUtil.calculateMD5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+"S090011007433");
        String sceneName = "余额宝主动转出页";
        //组装对照组
        String comparisonData_response="";//todo：从db获取
        UIMockQueryDetailResult uiMockQueryDetailResult_comparision =  addBakeryCase(new BklightMockData(),comparisonData_response,false);
        String bakeryUrl_comparison= uiMockQueryDetailResult_comparision.getUrl();
        String statisticPicUrl_comparison= uiMockQueryDetailResult_comparision.getThumbnail();//截图+获取静态图片
        logger.info("对照组：bakeryId：{},bakery演示地址bakeryUrl：{},静态页面图片地址statisticPicUrl:{}",uiMockQueryDetailResult_comparision.getId(),bakeryUrl_comparison,statisticPicUrl_comparison);
        DependencyAnalyzerModel dependencyAnalyzerModel_comparison = new DependencyAnalyzerModel();
        dependencyAnalyzerModel_comparison.setCaseId(uiMockQueryDetailResult_comparision.getId());//todo：待替换，当前为查询bakery的id
        dependencyAnalyzerModel_comparison.setCaseName(uiMockQueryDetailResult_comparision.getName());
        //sceneId=md5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+雨燕迭代地址)
        //雨燕迭代地址：“https://yuyan.antfin-inc.com/yuebao/yeb-trade/sprints/S090011007433/overview”取“S090011007433”
        dependencyAnalyzerModel_comparison.setSceneId(sceneId);
        dependencyAnalyzerModel_comparison.setSceneName(sceneName);
        dependencyAnalyzerModel_comparison.setAbnormalDetail("");//下游应用名称#下游节点名称#下游异常注入类型
        dependencyAnalyzerModel_comparison.setBakeryUrl(bakeryUrl_comparison);
        dependencyAnalyzerModel_comparison.setBakeryStaticPicUrl(statisticPicUrl_comparison);
        dependencyAnalyzerModel_comparison.setControlGroup(true);//是否是对照组，默认为实验组

        //组装实验组
        List<DependencyAnalyzerModel> treatmentDependencyAnalyzerModels = new LinkedList<>();
        for (BklightMockData bklightMockData : bklightMockDataList){
            UIMockQueryDetailResult uiMockQueryDetailResult =  addBakeryCase(bklightMockData,null,true);
            String bakeryUrl= uiMockQueryDetailResult.getUrl();
            String statisticPicUrl= uiMockQueryDetailResult.getThumbnail();//截图+获取静态图片
            logger.info("对照组：bakeryId：{},bakery演示地址bakeryUrl：{},静态页面图片地址statisticPicUrl:{}",uiMockQueryDetailResult.getId(),bakeryUrl,statisticPicUrl);
            DependencyAnalyzerModel dependencyAnalyzerModel = new DependencyAnalyzerModel();
            dependencyAnalyzerModel.setCaseId(uiMockQueryDetailResult.getId());//todo：待替换，当前为查询bakery的id
            dependencyAnalyzerModel.setCaseName(uiMockQueryDetailResult.getName());
            //sceneId=md5("YEB"+"com.alipay.yebtradebff.transferOut.prepare"+"operation=TRANSFER_OUT"+雨燕迭代地址)
            //雨燕迭代地址：“https://yuyan.antfin-inc.com/yuebao/yeb-trade/sprints/S090011007433/overview”取“S090011007433”
            dependencyAnalyzerModel.setSceneId(sceneId);
            dependencyAnalyzerModel.setSceneName(sceneName);
            dependencyAnalyzerModel.setAbnormalDetail(bklightMockData.getDown_node_app_name()+"#"+bklightMockData.getDown_node_service_name()+"#"+bklightMockData.getDown_node_inject_type());//下游应用名称#下游节点名称#下游异常注入类型
            dependencyAnalyzerModel.setBakeryUrl(bakeryUrl);
            dependencyAnalyzerModel.setBakeryStaticPicUrl(statisticPicUrl);
            dependencyAnalyzerModel.setControlGroup(false);//是否是对照组，默认为实验组

            //todo：自动判断强弱依赖--能力待搭建
            dependencyAnalyzerModel.setDependencyType(DEPENDENCY_JUDGE_TYPE_STRONG);
            dependencyAnalyzerModel.setJudgmentInfo("");
            dependencyAnalyzerModel.setValid(true);//todo:强弱依赖判断有效性，默认有效
            //
            treatmentDependencyAnalyzerModels.add(dependencyAnalyzerModel);
        }
        logger.info("dependencyAnalyzerModels: {}", com.alibaba.fastjson.JSON.toJSONString(treatmentDependencyAnalyzerModels));// 打印dependencyAnalyzerModels
        result.setSuccess(true);
        result.setSceneId(sceneId);
        result.setSceneName(sceneName);
        result.setIterateAddress("S090011007433");
        result.setComparisonGroup(dependencyAnalyzerModel_comparison);
        result.setTreatmentGroupList(treatmentDependencyAnalyzerModels);

        logger.info("解析并添加用例后返回结果: {}", com.alibaba.fastjson.JSON.toJSONString(result));

        return result;
    }

    /**
     * 新增bakery用例
     * @param bklightMockData
     * @return
     */
    UIMockQueryDetailResult addBakeryCase(BklightMockData bklightMockData,String comparisionData_response,boolean treatmentGroup){
        UIAISceneModel uiaiSceneModel = new UIAISceneModel();
        if (treatmentGroup) {
            //处理Hit_instance_node_response
            String hit_instance_node_response = "";
            try {
                hit_instance_node_response = parseExecuteResult(bklightMockData.getCase_type(), bklightMockData.getHit_instance_node_response());
            } catch (Exception e) {
                logger.error("bklight执行结果hit_instance_node_response解析失败!执行工具{}，执行返回结果:{}", bklightMockData.getCase_type(), bklightMockData.getHit_instance_node_response());
                return null;
            }
            if (StringUtil.isEmpty(bklightMockData.getHit_instance_node_response())) {
                logger.error("bklight执行结果hit_instance_node_response解析为空!执行工具{}，执行返回结果:{}", bklightMockData.getCase_type(), bklightMockData.getHit_instance_node_response());
                return null;
            }
            uiaiSceneModel.setSceneName("异常测试页面-转出页-新架构-提现页面渲染-"+bklightMockData.getDown_node_app_name()+"-"+bklightMockData.getDown_node_service_name()+"-"+bklightMockData.getDown_node_inject_type());
            uiaiSceneModel.setResponse(hit_instance_node_response);
        }else{
            uiaiSceneModel.setSceneName("标准对照页面-转出页-新架构-提现页面渲染");
            uiaiSceneModel.setResponse(comparisionData_response);
        }
        uiaiSceneModel.setServiceName("com.alipay.yebtradebff.transferOut.prepare");//operationType与服务有所差异，需要映射
        uiaiSceneModel.setRequest("operation=TRANSFER_OUT");
        //转出页：uiauto ； 账单：uiauto
        uiaiSceneModel.setSprintId(BAKERY_TRANSOUTPAGE_SPRINT_ID);
        //转出页：1c0f21ba-d014-4e67-ad6c-5131dc6ac3fc ； 账单：7604094e-9e65-4665-945d-8441d3bc190c
        uiaiSceneModel.setProjectId(BAKERY_TRANSOUTPAGE_PROJECT_ID);
        //转出页：S090011007433 ； 账单：S09001614827
        uiaiSceneModel.setPreviewSprintId(BAKERY_TRANSOUTPAGE_PREVIEW_SPRINT_ID);
        uiaiSceneModel.setStartPage(BAKERY_TRANSOUTPAGE_STARTPAGE);
        String idActual = addSceneUIAICase(uiaiSceneModel,"RPC","ONLINE");
        UIMockQueryDetailResult uiMockQueryDetailResult = getStatisticPicUrl(idActual,uiaiSceneModel);

        return uiMockQueryDetailResult;
    }

    /**
     * 添加bakery用例
     * @param uiaiSceneModel
     * @return
     */
    public String addSceneUIAICase(UIAISceneModel uiaiSceneModel,String mockType,String status) {
        //批量写
        UIMockAddRequest uiMockAddRequest = new UIMockAddRequest();
        List<MockConfig> mockConfigs = new LinkedList<>();
        MockConfig mockConfig = new MockConfig();
        mockConfig.setApi(uiaiSceneModel.getServiceName());
        mockConfig.setData(uiaiSceneModel.getResponse());
        mockConfig.setLatency(100);
        mockConfig.setType(mockType);
        mockConfigs.add(mockConfig);

        uiMockAddRequest.setMockConfig(mockConfigs);
        uiMockAddRequest.setQs(uiaiSceneModel.getRequest());
        uiMockAddRequest.setSprintId(uiaiSceneModel.getSprintId());// //S09001614827
        uiMockAddRequest.setName(uiaiSceneModel.getSceneName());//场景名称
        uiMockAddRequest.setDescription(uiMockAddRequest.getName());
        uiMockAddRequest.setProjectId(uiaiSceneModel.getProjectId());

        uiMockAddRequest.setStartPage(uiaiSceneModel.getStartPage());
        uiMockAddRequest.setStatus(status);
        List<String> typeList = new LinkedList<>();
        typeList.add("STATUS");
        uiMockAddRequest.setType(typeList);
//        uiMockAddRequest.setEnvConfig();
//        uiMockAddRequest.setSchemaConfig();

        String res = sendHttpPostForUIMock(BAKERY_SCENE_ADD_URL_PROD, com.alibaba.fastjson.JSON.toJSONString(uiMockAddRequest));
        logger.info("bakery-case add result:{}", res);
        return res;
    }

    /**
     * 获取bakery用例对应渲染页面的静态图片地址
     * @param id
     * @param uiaiSceneModel
     * @return
     */
    public UIMockQueryDetailResult getStatisticPicUrl(String id,UIAISceneModel uiaiSceneModel){
        UIMockQueryDetailResult uiMockQueryDetailResult = getSceneModelDetail(id,uiaiSceneModel);//入参sprint固定值？
        try {
            //截图
            shotScreen(uiMockQueryDetailResult.getUrl(),"page");
        }catch (Exception e){
            logger.error("截图异常");
        }
        //获取静态图
        uiMockQueryDetailResult = getSceneModelDetail(id,uiaiSceneModel);
//        statisticPic = uiMockQueryDetailResult.getThumbnail()==null?uiMockQueryDetailResult.getUrl():uiMockQueryDetailResult.getThumbnail();
        return uiMockQueryDetailResult;
    }

    /**
     * bakery触发页面截图并装载在bakery用例中的thumbnail字段
     * @param url
     * @param type
     */
    public void shotScreen(String url,String type){
        CreateJobRequest createJobRequest = new CreateJobRequest();
        createJobRequest.setTaskYuyanId("180020010000834003");
        Map argsMap = new HashMap();
        argsMap.put("url",url);
        argsMap.put("type",type);
//        argsMap.put("deviceName","iPhone X");
        createJobRequest.setArgs(argsMap);
//        JobFacade jobFacade = new JobFacade() {
//            @Override
//            public CreateJobResult createJob(CreateJobRequest createJobRequest) {
//                return null;
//            }
//        };
//        CreateJobResult result = jobFacade.createJob(createJobRequest);
//        logger.info("截图结果:{}", JSONObject.toJSONString(result));
    }


    /**
     * 获取bakery用例的详细信息
     * @param id
     * @param uiaiSceneModel
     * @return
     */
    public UIMockQueryDetailResult getSceneModelDetail(String id, UIAISceneModel uiaiSceneModel){
        String bakeryUrlQueryDetail = BAKERY_SCENE_QUERY_DETAIL_URL_PROD;//生产环境
        UIMockQueryDetailRequest uiMockQueryDetailRequest = new UIMockQueryDetailRequest();
        JSONObject idJsonObject = JSONObject.parseObject(id);
        uiMockQueryDetailRequest.setId(idJsonObject.getString("id"));
        uiMockQueryDetailRequest.setInclude("url");
        uiMockQueryDetailRequest.setPreviewSprintId(uiaiSceneModel.getPreviewSprintId());
        uiMockQueryDetailRequest.setSprintId(uiaiSceneModel.getSprintId());//uiauto
        bakeryUrlQueryDetail =bakeryUrlQueryDetail+uiMockQueryDetailRequest.getId()+"?previewSprintId="+uiMockQueryDetailRequest.getPreviewSprintId()+"&sprintId="+uiMockQueryDetailRequest.getSprintId()+"&include="+uiMockQueryDetailRequest.getInclude();//生产环境
        UIMockQueryDetailResult uiMockQueryDetailResult = null;
        String queryDetailRes = "";
        try{
            queryDetailRes = sendHttpGetForUIMock(bakeryUrlQueryDetail);
            uiMockQueryDetailResult = com.alibaba.fastjson.JSON.parseObject(queryDetailRes, UIMockQueryDetailResult.class);
        }catch(Exception e ){
            logger.error("uiMockQueryDetailResult Run Exception!");
        }
        logger.info(" bakery-case query result:{}", uiMockQueryDetailResult.toString());
        return uiMockQueryDetailResult;
    }

    /**
     * 解析mock执行的返回结果
     * @param case_Type
     * @param executeResult
     * @return
     */
    String parseExecuteResult(String case_Type,String executeResult){
        if (executeResult==null || StringUtil.isEmpty(executeResult)){
            return null;
        }
        if (BKLIGHT_EXEC_CASE_TYPE_ETE.equals(case_Type)) {
            HashMap<String, Object> execResultMap = com.alibaba.fastjson.JSON.parseObject(executeResult, HashMap.class);
            if (!execResultMap.isEmpty()) {
                // 获取第一个 entry
                Map.Entry<String, Object> firstEntry = execResultMap.entrySet().iterator().next();
                // 将 value 转换为 JSONObject
                Object value = firstEntry.getValue();
                JSONObject jsonObject;

                if (value instanceof JSONObject) {
                    // 如果已经是 JSONObject 则直接转换
                    jsonObject = (JSONObject) value;
                } else {
                    // 如果不是，通过 JSON 序列化/反序列化转换
                    jsonObject = JSONObject.parseObject(JSONObject.toJSONString(value));
                }
                String executeState = jsonObject.getString("state");
                String eteResponse = null;
                if ("SUCCESS".equals(executeState)) {
                    String eteSuccessResult = jsonObject.getJSONObject("runningCtx").getString("resultCtx");
                    JSONObject eteResultJson = JSONObject.parseObject(StringEscapeUtils.unescapeJava(eteSuccessResult));
                    eteResponse = eteResultJson.getString("result");
                } else if ("FAIL".equals(executeState)) {
                    String eteExceptionResult = jsonObject.getString("exceptionDetail");
                    JSONObject eteResultJson = JSONObject.parseObject(StringEscapeUtils.unescapeJava(eteExceptionResult));
                    eteResponse = eteResultJson.getString("result");
                }
                if (!StringUtil.isEmpty(eteResponse)) {
                    eteResponse = StringEscapeUtils.unescapeJava(eteResponse);
                    logger.info("eteResponse:{}", eteResponse);
                    return eteResponse;
                }
            }
        }

        return null;
    }

//    /**
//     * 生成随机码
//     * @return
//     */
//    public static String generateRandomCode() {
//        //十八位长度
//        SimpleDateFormat sdf = new SimpleDateFormat("MMddHHmm");  // 定义日期格式
//        String dateStr = sdf.format(new Date());  // 获取当前日期并格式化为字符串
//        Random random = new Random();
//        int randomNum = random.nextInt(9999);  // 生成0~9999之间的随机整数
//        String code = dateStr + String.format("%04d", randomNum);  // 将日期和随机整数组合成18位字符串
//        return code;
//    }

}
