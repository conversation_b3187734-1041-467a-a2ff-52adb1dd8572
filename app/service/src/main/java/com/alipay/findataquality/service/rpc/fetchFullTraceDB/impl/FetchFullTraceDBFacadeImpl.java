package com.alipay.findataquality.service.rpc.fetchFullTraceDB.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.exception.BusinessErrorEnum;
import com.alipay.findataquality.facade.exception.BusinessException;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.FetchFullTraceDBFacade;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.DBDataFetchRecordStatusEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.*;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.request.FetchDataRequest;
import com.alipay.findataquality.service.dto.FullTraceValidateDataFetchRecordDTO;
import com.alipay.findataquality.service.repository.FullTraceValidateDataFetchRecordRepository;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.DBConnectManager;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans.FaassetAccountQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans.FaassetAccountingQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans.FaassetFundTradeQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans.FaassetInsurTradeQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.charge.FundChargePersonalQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.financeasset.FaFinStrategyQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.financeasset.FinanceAssetQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.fund.FinFundTradeQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.yeb.*;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.IdGenerator;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import com.alipay.findataquality.service.template.AbstractExecuteTemplate;
import com.alipay.sofa.common.thread.SofaThreadPoolExecutor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.*;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName FetchFullTraceDBFacade
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 13:39
 * @Version V1.0
 **/
public class FetchFullTraceDBFacadeImpl implements FetchFullTraceDBFacade {
    /** 日志 */
    private static final Logger logger = LoggerFactory.getLogger(FetchFullTraceDBFacade.class);

    //DB连接管理
    private DBConnectManager dbConnectManager = new DBConnectManager();

    @Autowired
    private FullTraceValidateDataFetchRecordRepository fullTraceValidateDataFetchRecordRepository;

    @Override
    public QueryTaskResult fetchDataScene(FetchDataRequest request) {
        QueryTaskResult result = new AbstractExecuteTemplate<QueryTaskResult, QueryTaskResult>().doExecute("FetchFullTraceDBFacadeImpl-queryScene",
                new AbstractExecuteTemplate.ExecuteInvoke<QueryTaskResult, QueryTaskResult>() {
                    @Override
                    public void checkParam() {
                        QuerySceneEnum sceneEnum = QuerySceneEnum.getByCode(request.getSceneCode());
                        if(sceneEnum==null){
                            throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"sceneCode值无效");
                        }
                        if(StringUtil.isBlank(request.getIntputValue())){
                            throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"inputValue不能为空");
                        }
                        if(StringUtil.isBlank(request.getEnv())){
                            throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"env不能为空");
                        }
                        if(StringUtil.isBlank(request.getOperator())){
                            throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"operator不能为空");
                        }
                    }

                    @Override
                    public void assembleResult(QueryTaskResult result, QueryTaskResult model) {
                        if(model!=null&&!model.getTableData().isEmpty()){
                            List<TableData> tableData = new ArrayList<>();
                            //有数据的放在前面，无数据的放在后面
                            tableData.addAll(model.getTableData().stream().filter(data->CollectionUtils.isNotEmpty(data.getColumnData())).collect(Collectors.toList()));
                            tableData.addAll(model.getTableData().stream().filter(data->CollectionUtils.isEmpty(data.getColumnData())).collect(Collectors.toList()));
                            //关键数据
                            List<TableData> keyData = model.getKeyData();
                            result.setSuccess(true);
                            result.setTableData(tableData);
                            result.setKeyData(keyData);
                            result.setTraceIds(model.getTraceIds());
                            String keyDataSummary = StrUtils.getKeyDataSummary(keyData);
                            result.setKeyDataSummary(keyDataSummary);

                            //汇总获取结果，记录扩展信息
                            String extInfo = JSONObject.toJSONString(getExtInfoMap(tableData,model.getTraceIds()));
                            result.setExtInfo(extInfo);
                            String shareCode = IdGenerator.nextSn();
                            result.setShareCode(shareCode);

                            //测试类型直接跳过，不记录数据
                            //获取数据结果为0时不记录
                            if("system_auto_test".equals(request.getOperator())||result.getTotalRowCnt()==0){
                                return;
                            }

                            try {
                                FullTraceValidateDataFetchRecordDTO recordDTO = new FullTraceValidateDataFetchRecordDTO();
                                recordDTO.setSceneCode(request.getSceneCode());
                                recordDTO.setEntranceCode(request.getIntputValue());
                                recordDTO.setStatus(DBDataFetchRecordStatusEnum.UNMARKED.getCode());
                                recordDTO.setFetchTime(new Date());
                                recordDTO.setContent(JSONObject.toJSONString(tableData));
                                recordDTO.setOperator(request.getOperator());
                                recordDTO.setOperatorId(request.getOperatorId());
                                recordDTO.setEnv(request.getEnv());
                                recordDTO.setShareCode(shareCode);
                                recordDTO.setExtInfo(extInfo);
                                if (keyData != null && !keyData.isEmpty()) {
                                    recordDTO.setKeyData(JSONObject.toJSONString(keyData));
                                }
                                recordDTO.setKeyDataSummary(keyDataSummary);
                                fullTraceValidateDataFetchRecordRepository.insert(recordDTO);
                                result.setId(String.valueOf(recordDTO.getId()));

                            } catch (Exception e) {
                                logger.error("保存取数请求记录失败！sceneCode={}, inputValue={}, env={}, operator={}", request.getSceneCode(), request.getIntputValue(), request.getEnv(), request.getOperator());
                            }
                        }
                    }

                    @Override
                    public QueryTaskResult getResultInstance() {
                        return new QueryTaskResult();
                    }

                    @Override
                    public QueryTaskResult execute() {
                        //初始化上下文
                        QueryContext context = new QueryContext();
                        context.setDbConnectConfigMap(dbConnectManager.getDbConnectionInfoMap());
                        //创建线程池
                        long start = System.currentTimeMillis();
                        SofaThreadPoolExecutor service = new SofaThreadPoolExecutor(10, 20, 60,
                                TimeUnit.SECONDS, new LinkedBlockingQueue<>(100));
//                        ExecutorService service = Executors.newFixedThreadPool(10); // 创建固定大小的线程池
                        long end = System.currentTimeMillis();
                        logger.info("初始化线程池耗时：{}",(end-start)/1000.0);

                        context.setExecutorService(service);
                        context.setInputValue(request.getIntputValue());
                        //获取场景并调用取数
                        AbstractQueryScene scene = getScene(request.getSceneCode(), context);
                        QueryTaskResult result = null;
                        if(scene!=null){
                            result = scene.doQuery();
                        }
                        service.shutdown();
                        return result;
                    }

                    private Map<String,String> getExtInfoMap(List<TableData> tableData,String traceIds){
                        long dbCount = tableData.stream().filter(data -> CollectionUtils.isNotEmpty(data.getColumnData())).map(TableData::getDbName).distinct().count();
                        long tableCount = tableData.stream().filter(data -> CollectionUtils.isNotEmpty(data.getColumnData()))
                                .map(data -> data.getDbName() + "." + data.getTableName()).distinct().count();
                        long columnCount = 0;
                        for (TableData data : tableData) {
                            Set<String> columnSet = new HashSet<>();
                            for (String columnName : data.getColumnName()) {
                                columnSet.add(data.getDbName() + "." + data.getTableName() + "." + columnName);
                            }
                            if (CollectionUtils.isNotEmpty(data.getColumnData())) {
                                columnCount += columnSet.size() * data.getColumnData().size();
                            }
                        }

                        String dbNames = tableData.stream().filter(data -> CollectionUtils.isNotEmpty(data.getColumnData()))
                                .map(data -> data.getDbName()).distinct().collect(Collectors.joining(","));

                        long dataCount = 0L;
                        for (TableData data : tableData) {
                            dataCount = dataCount + data.getColumnData().size();
                        }

                        Map<String,String> extInfoMap = new HashMap<>();
                        extInfoMap.put("dbCount", dbCount+"");
                        extInfoMap.put("tableCount", tableCount+"");
                        extInfoMap.put("columnCount", columnCount+"");
                        extInfoMap.put("dataCount", dataCount+"");
                        extInfoMap.put("dbNames", dbNames);
                        extInfoMap.put("traceIds", traceIds);
                        return extInfoMap;
                    }
                    private Class[]sceneList(){
                        return new Class[]{
                                /**
                                 * 资金平台
                                */
                                FinanceAssetQueryScene.class,
                                FaFinStrategyQueryScene.class,
                                /**
                                 * 余额宝
                                */
                                YebSuperTradeQueryScene.class,
                                YebAccountQueryScene.class,
                                YebFinancingcoreQueryScene.class,
                                YebSubcardTradeQueryScene.class,
                                YebTransFzAndUnfzQueryScene.class,
                                YebTransInQueryScene.class,
                                YebTransOutQueryScene.class,
                                YebTransInAndSwitchQueryScene.class,
                                /**
                                 * 计收费
                                */
                                FundChargePersonalQueryScene.class,

                                /**
                                 * 资产账
                                */
                                FaassetAccountQueryScene.class,
                                FaassetAccountingQueryScene.class,
                                FaassetFundTradeQueryScene.class,
                                FaassetInsurTradeQueryScene.class,
                                /**
                                 * 基金
                                */
                                FinFundTradeQueryScene.class
                        };
                    }

                    private AbstractQueryScene getScene(String sceneCode, QueryContext context){
                        QuerySceneEnum sceneEnum = QuerySceneEnum.getByCode(sceneCode);
                        AbstractQueryScene scene = null;
                        //自动扫描包下新增的查询场景，并添加到功能中来
                        //List<String> classNameList = PackageUtils.getAllQuerySceneClassName();
                        for(Class classType: sceneList()){
                            try {
                                AbstractQueryScene sceneInstance = (AbstractQueryScene)classType.getConstructor(QueryContext.class).newInstance(context);
                                if(sceneInstance.queryScene().equals(sceneEnum)){
                                    scene=sceneInstance;
                                }
                            } catch (Exception e) {
                                logger.warn("获取查询场景失败！sceneCode={}, classType={}, e={}", sceneCode,classType,e);
                            }
                        }
                        return scene;
                    }
                });
        return result;
    }

    @Override
    public QuerySceneResult getAllScenes() {
        QuerySceneResult result = new AbstractExecuteTemplate<QuerySceneResult, QuerySceneResult>().doExecute("FetchFullTraceDBFacadeImpl-getAllScenes",
                new AbstractExecuteTemplate.ExecuteInvoke<QuerySceneResult, QuerySceneResult>() {

                    @Override
                    public void checkParam() {

                    }

                    @Override
                    public void assembleResult(QuerySceneResult result, QuerySceneResult model) {
                        if(model!=null&&!model.getScenes().isEmpty()){
                            result.setSuccess(true);
                            result.setScenes(model.getScenes());
                        }
                    }

                    @Override
                    public QuerySceneResult getResultInstance() {
                        return new QuerySceneResult();
                    }

                    @Override
                    public QuerySceneResult execute() {
                        QuerySceneResult result = new QuerySceneResult();
                        for (QuerySceneEnum sceneEnum: QuerySceneEnum.values()) {
                            QueryScene scene = new QueryScene();
                            scene.setSceneName(sceneEnum.getDesc());
                            scene.setSceneCode(sceneEnum.getCode());
                            scene.setDomainCode(sceneEnum.getDomainEnum().getCode());
                            scene.setDomainName(sceneEnum.getDomainEnum().getDesc());
                            scene.setInputVar(sceneEnum.getInputName());
                            scene.setInputTips(sceneEnum.getInputDesc()+sceneEnum.getInputName());
                            scene.setSupportFundFlow(sceneEnum.isSupportFundFlow()?"T":"F");
                            result.getScenes().add(scene);
                        }
                        return result;
                    }
                });
        return result;
    }
}
