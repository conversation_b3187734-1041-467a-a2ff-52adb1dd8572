package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.cif;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName ObcifTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 20:06
 * @Version V1.0
 **/
public class ObcifTask extends AbstractQueryTask {
    public ObcifTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.OBCIF;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from cs_sub_card where card_no = '${user_id}'",
                "select * from cs_contract where principal_id = '${user_id}'",
                "select * from cs_outside_asset_bind_relation where user_id = '${user_id}'",
                "select * from cs_user_account_ar where user_id = '${user_id}'",
                "select * from cs_principal_relation where business_to_id = '${user_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[5];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[5];
    }
}
