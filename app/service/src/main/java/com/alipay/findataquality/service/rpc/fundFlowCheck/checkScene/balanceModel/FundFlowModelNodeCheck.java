package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractModelCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetModelNode;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.ModelCheckResult;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @ClassName FundFlowModelNodeCheck
 * @Description 资金模型检查-单节点检查
 * <AUTHOR>
 * @Date 2025/5/6 14:57
 * @Version V1.0
 **/
public class FundFlowModelNodeCheck extends AbstractModelCheckScene {
    public FundFlowModelNodeCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "资金模型单节点检查";
    }

    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户同时存在流入、流出，则流入金额<=流出金额")
    public ModelCheckResult 多渠道流入金额小于等于流出金额(AssetModelNode assetModelNode) {
        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
        actionTypeMap.put(AssetActionTypeEnum.INCREASE,-1);
        actionTypeMap.put(AssetActionTypeEnum.DECREASE,-1);
        String comparator = "<=";

        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
    }

    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户同时存在流入、流出，且流入、流出均只包含一种渠道，则流入金额=流出金额")
    public ModelCheckResult 单渠道流入金额等于流出金额(AssetModelNode assetModelNode) {
        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
        actionTypeMap.put(AssetActionTypeEnum.INCREASE,1);
        actionTypeMap.put(AssetActionTypeEnum.DECREASE,1);
        String comparator = "==";

        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
    }

//    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户存在流入并冻结，则流入金额<=冻结金额")
//    public ModelCheckResult 多渠道流入金额小于等于冻结金额(AssetModelNode assetModelNode) {
//        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
//        actionTypeMap.put(AssetActionTypeEnum.INCREASE,-1);
//        actionTypeMap.put(AssetActionTypeEnum.FREEZE,1);
//        String comparator = "<=";
//
//        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
//    }
//
//    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户存在解冻并流出，则流出总额<=解冻金额")
//    public ModelCheckResult 多渠道流出金额小于等于解冻金额(AssetModelNode assetModelNode) {
//        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
//        actionTypeMap.put(AssetActionTypeEnum.DECREASE,-1);
//        actionTypeMap.put(AssetActionTypeEnum.UNFREEZE,1);
//        String comparator = "<=";
//
//        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
//    }

    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户存在流入并冻结，且流入只包含一种渠道，则转入总额=冻结金额")
    public ModelCheckResult 单渠道流入金额等于冻结金额(AssetModelNode assetModelNode) {
        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
        actionTypeMap.put(AssetActionTypeEnum.INCREASE,1);
        actionTypeMap.put(AssetActionTypeEnum.FREEZE,-1);
        String comparator = "==";

        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
    }


    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户存在流入并冻结，且流出只包含一种渠道，则转出总额=解冻金额")
    public ModelCheckResult 单渠道流出金额等于解冻金额(AssetModelNode assetModelNode) {
        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
        actionTypeMap.put(AssetActionTypeEnum.DECREASE,1);
        actionTypeMap.put(AssetActionTypeEnum.UNFREEZE,-1);
        String comparator = "==";

        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
    }

//    //todo：处理冻结码一致
//    @AutoCheck(ruleDesc = "单账户资金平衡检查：账户存在冻结与解冻且冻结码一致，则冻结金额=解冻金额")
//    public ModelCheckResult 多渠道冻结金额等于解冻金额(AssetModelNode assetModelNode) {
//        Map<AssetActionTypeEnum,Integer> actionTypeMap = new LinkedHashMap<>();
//        actionTypeMap.put(AssetActionTypeEnum.FREEZE,1);
//        actionTypeMap.put(AssetActionTypeEnum.UNFREEZE,1);
//        String comparator = "==";
//
//        return autoCheckNodeBalance(assetModelNode,actionTypeMap,comparator);
//    }
}
