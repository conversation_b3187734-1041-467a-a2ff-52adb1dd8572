package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName FaassetTradeTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 11:19
 * @Version V1.0
 **/
public class FaassetTradeTask extends AbstractQueryTask {
    public FaassetTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FAASSET;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from astc_trans_order_fa_${db_flag} where id in (${fa_trans_and_order_list}) or (id like '${date}%' and order_no in (${fa_trans_and_order_list})) or (id like '${date}%' and unique_id in (${fa_trans_and_order_list}))",
                "select * from astc_trans_instruction_fa_${db_flag} where order_id in (${fa_trans_and_order_list})",
                "select * from astc_asset_log_fa_${db_flag} where id like '${date}%' and trans_log_id in (${trans_log_id_list})",
                "select * from astc_asset_freeze_log_fa_${db_flag} where id like '${date}%' and trans_log_id in (${trans_log_id_list})",
                "select * from astc_asset_balance_fa_${db_flag} where accounting_id in (${asset_id_list})",
                "select * from astc_asset_last_price_fa_${db_flag} where accounting_id in (${asset_id_list})",
                "select * from astc_trade_control_fa_${db_flag} where asset_id in (${asset_id_list})",
                "select * from astc_net_asset_fa_${db_flag} where asset_id in (${asset_id_list})"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "id->fa_trans_and_order_list,asset_id->asset_id_list",
                "trans_log_id->trans_log_id_list",
                null, null, null, null, null,null
        };
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAppend("id","fa_trans_and_order_list");
        context.copyValueAppend("order_id","fa_trans_and_order_list");
        context.copyValueAppend("unique_id","fa_trans_and_order_list");
    }
}
