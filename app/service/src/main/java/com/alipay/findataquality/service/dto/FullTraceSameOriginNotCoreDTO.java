package com.alipay.findataquality.service.dto;

import java.util.Date;

public class FullTraceSameOriginNotCoreDTO {
    private Long id;

    private Date gmtCreate;

    private Date gmtModified;

    private String businessRegion;

    private String dbName;

    private String tableName;

    private String notCoreColumns;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getBusinessRegion() {
        return businessRegion;
    }

    public void setBusinessRegion(String businessRegion) {
        this.businessRegion = businessRegion;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getNotCoreColumns() {
        return notCoreColumns;
    }

    public void setNotCoreColumns(String notCoreColumns) {
        this.notCoreColumns = notCoreColumns;
    }
}