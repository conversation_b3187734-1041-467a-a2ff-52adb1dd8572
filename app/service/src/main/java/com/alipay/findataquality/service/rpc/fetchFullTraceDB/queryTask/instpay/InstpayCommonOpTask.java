package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.instpay;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
/**
 * @ClassName InstpayCommonOpTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/25 15:27
 * @Version V1.0
 **/
public class InstpayCommonOpTask extends InstpayCommonTask{
    public InstpayCommonOpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String opTask() {
        return "op";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
