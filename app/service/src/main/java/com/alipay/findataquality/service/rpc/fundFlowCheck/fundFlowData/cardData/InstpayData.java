package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.cardData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;

/**
 * @ClassName InstpayData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 15:06
 * @Version V1.0
 **/
public class InstpayData extends AssetRecordData {

    public static final String IP_DECREASE_COMMAND = "instpay.ip_decrease_command";
    public static final String IP_WITHDRAW_COMMAND = "instpay.ip_withdraw_command";
    public static final String IP_REFUND_COMMAND = "instpay.ip_refund_command";

    @Override
    public String[] dataTable() {
        return new String[]{
                IP_DECREASE_COMMAND,
                IP_WITHDRAW_COMMAND,
                IP_REFUND_COMMAND
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
            AssetOperation operation = null;
            switch (tableIndex) {
                case 0:
                    operation = AssetOperation.createBaseOperation(tableData,dataIndex,"payer_id","asset_principal","settle_amount");
                    operation.setTraceId(operation.getColumnValue("ant_ev_ctx#traceID"));
                    operation.setActionType(AssetActionTypeEnum.DECREASE);
                    String status1 = operation.getColumnValue("status");
                    //非SU状态，不记录操作
                    if (!"SU".equals(status1)){
                        operation = null;
                    }
                    break;
                case 1:
                    operation = AssetOperation.createBaseOperation(tableData,dataIndex,"payer_id","card_no","apply_amount");
                    operation.setActionType(AssetActionTypeEnum.INCREASE);
                    String status2 = operation.getColumnValue("status");
                    if("FA".equals(status2)){
                        //如果是FA失败状态，需要补一条卡资产减操作
                        AssetOperation newOp = operation.clone();
                        newOp.setPayToolType(AssetPayToolEnum.CARD);
                        newOp.setActionType(AssetActionTypeEnum.DECREASE);
                        this.getAssetOperationData().getOperationList().add(newOp);
                    }
                    break;
                case 2:
                    //基金退款操作，也作为银行卡入
                    operation = AssetOperation.createBaseOperation(tableData,dataIndex,"payer_id","deposit_inst_account_no","refund_amount");
                    operation.setActionType(AssetActionTypeEnum.INCREASE);
                    operation.setTraceId(operation.getColumnValue("ant_ev_ctx#traceID"));
                    String status3 = operation.getColumnValue("status");
                    if("FA".equals(status3)){
                        //如果是FA失败状态，需要补一条卡资产减操作
                        AssetOperation newOp = operation.clone();
                        newOp.setPayToolType(AssetPayToolEnum.CARD);
                        newOp.setActionType(AssetActionTypeEnum.DECREASE);
                        this.getAssetOperationData().getOperationList().add(newOp);
                    }
                    break;
                default:
                    return null;
            }
            if(operation!=null){
                operation.setPayToolType(AssetPayToolEnum.CARD);
            }
        return operation;
    }
}
