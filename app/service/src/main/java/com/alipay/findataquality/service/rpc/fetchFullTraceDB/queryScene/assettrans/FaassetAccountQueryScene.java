package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetFainsTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetFincomeTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetTradeTask;

/**
 * @ClassName FaassetTransQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 11:14
 * @Version V1.0
 **/
public class FaassetAccountQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FaassetAccountQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                FaassetTradeTask.class,
                FaassetFincomeTradeTask.class,
                FaassetFainsTradeTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,0,0};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FAASSET_ACCOUNT_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        context.putGlobal("asset_id_list",context.getInputValue());
        context.putGlobal("db_flag", StrUtils.substrFromEnd(inputValue,12,2));
        context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(inputValue,12,1));
        context.putGlobal("date",StrUtils.substrFromStart(inputValue,0,8));
        String dbr = DbUtils.getDbrFromDbFlag(context.getValue("db_flag"));
        context.putGlobal("db_r",dbr);
    }
}
