package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import static com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund.FinFundTradeTask.fundCommonSet;

/**
 * @ClassName FinmemberTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 15:26
 * @Version V1.0
 **/
public class FinTradeRecordTask extends AbstractQueryTask {

    public FinTradeRecordTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINTRADERECORD;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                genSql("fin_trade_record_${db_f_record_flag}","order_id",fundCommonSet,null,new String[]{"out_biz_no","out_order_no","trade_out_biz_no","relation_order_id"}),
                "select * from fin_trade_confirm_record_${db_f_record_flag} where user_id in (${user_id_list}) and (idempotent_id in (${"+fundCommonSet+"}) or trade_order_id in (${"+fundCommonSet+"}))",
                genSql("fin_trade_record_flow_${db_flag}","order_id",fundCommonSet,new String[]{"out_biz_no"},new String[]{"idempotent_id"})
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "order_id|out_biz_no|out_order_no|trade_out_biz_no|relation_order_id->"+fundCommonSet,
                "idempotent_id|trade_order_id->"+fundCommonSet,
                "order_id|out_biz_no|idempotent_id->"+fundCommonSet
        };
    }

    @Override
    public void handleContextBefore (QueryContext context) {
        String uid = StrUtils.getFirstItem(context.getValue("user_id_list"));
        String fRecordFlag = DbUtils.getFinTradeRecordSchemaFromUid(uid);
        context.putGlobal("db_f_record_flag",fRecordFlag);
        context.copyValueAppend("order_id",fundCommonSet);
    }
}
