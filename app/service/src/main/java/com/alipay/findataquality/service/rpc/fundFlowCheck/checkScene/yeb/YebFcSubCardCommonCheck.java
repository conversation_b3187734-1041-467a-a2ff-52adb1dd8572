package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;

/**
 * @ClassName YebCommonCheck
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/8 14:57
 * @Version V1.0
 **/
public class YebFcSubCardCommonCheck extends AbstractCheckScene {
    public YebFcSubCardCommonCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "financingcore子卡数据检查";
    }

//    @AutoCheck(ruleDesc="[主子卡过户]financingcore主子卡核心要素（uid、inst_id、amount、clear_dt）一致性检查")
//    public RuleCheckResult fc主子卡核心要素一致性检查(FinancingcoreData fcMainCardData, FinancingcoreData fcSubcardData) {
//        AssetRecordData left = fcMainCardData.findTable(FinancingcoreData.DECREASE).findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'").findVars("user_id", "inst_id", "clear_dt");
//        AssetRecordData right = fcSubcardData.findTable(FinancingcoreData.INCREASE).findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'").findVars("user_id", "inst_id", "clear_dt");
//        return autoCheckCombineEqual(left, right);
//    }

    @AutoCheck(ruleDesc = "fc子卡数据同分账数据汇总金额相等")
    public RuleCheckResult fc子卡数据同分账数据汇总金额相等(FinancingcoreData fc, AftransTaskData aftransTaskData) {
        AssetRecordData a1 = fc.findData("asset_account_type != 'fundpay_share' && asset_account_type != 'distribution'").findVars("real_amount");
        AssetRecordData a2 = aftransTaskData.findTable("yebcore.yebs_subcard_aftrans_task").findVars("amount");
        return autoCheckSumEqual(a1,a2,1) ;
    }

    @AutoCheck(ruleDesc = "fc数据组合比对")
    public RuleCheckResult fc数据组合比对(FinancingcoreData fc, AftransTaskData aftransTaskData) {
        AssetRecordData a1 = fc.findData("asset_account_type != 'fundpay_share' && asset_account_type != 'distribution'").findVars("real_amount","inst_id");
        AssetRecordData a2 = aftransTaskData.findTable("yebcore.yebs_subcard_aftrans_task").findVars("amount","inst_id");
        return autoCheckCombineEqual(a1,a2);
    }



}
