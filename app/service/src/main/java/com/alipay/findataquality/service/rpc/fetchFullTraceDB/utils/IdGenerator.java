package com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.SecureRandom;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;

/**
 * id生成工具类
 */
public class IdGenerator {
    private final static Logger logger = LoggerFactory.getLogger("IdGenerator");

    /**
     * 计算精度：毫秒值（时间位41位，最多69年）
     * 秒值（时间位31位，最多68年）
     */
    private TimeUnit timeUnit = TimeUnit.SECONDS;
    /**
     * 最大时钟回退时间
     */
    private final long MAX_BACKWARD = 5L;
    /**
     * 开始时间截 (2015-01-01)
     */
    private final long twepoch = 1420041600000L;
    /**
     * 一天秒|毫秒值所占的位数：86400_000
     */
    private final long dayMillisBits = 27L;
    private final long daySecondsBits = 17L;
    /**
     * 机器id所占的位数
     */
    private long workerIdBits = 10L;
    /**
     * 数据中心id所占的位数
     */
    private long datacenterIdBits = 0L;
    /**
     * 序列在id中占的位数
     */
    private long sequenceBits = 12L;
    /**
     * 支持的最大机器id，结果是31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数)
     */
    private long maxWorkerId = -1L ^ (-1L << workerIdBits);
    /**
     * 支持的最大数据中心id，结果是31
     */
    private long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
    /**
     * 机器ID向左移12位
     */
    private long workerIdShift = sequenceBits;
    /**
     * 数据中心id向左移17位(12+5)
     */
    private long datacenterIdShift = sequenceBits + workerIdBits;
    /**
     * 时间截向左移22位(5+5+12)
     */
    private long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
    /**
     * 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)
     */
    private long sequenceMask = -1L ^ (-1L << sequenceBits);
    /**
     * 按天序列ID最大值：毫秒值位数+时间偏移位
     */
    private long maxSnId = (1L << (timestampLeftShift + dayMillisBits)) - 1;

    /**
     * 工作机器ID(0~1023)
     */
    private long workerId;
    /**
     * 数据中心ID(0~0)
     */
    private long datacenterId;
    /**
     * 毫秒内序列(0~4095)
     */
    private long sequence = 0L;
    /**
     * 上次生成ID的时间截
     */
    private long lastTimestamp = -1L;

    /**
     * 单例idGenerator
     */
    private static IdGenerator idGenerator = null;

    static {
        idGenerator = new IdGenerator();
        idGenerator.doSetBits(10, 0, 10, TimeUnit.MILLISECONDS);

        String hostName = null;
        try {
            InetAddress netAddress = InetAddress.getLocalHost();
            hostName = netAddress.getHostName();
        } catch (UnknownHostException e) {
            logger.error("获取当前服务器hostname失败！e={}",e);
        }
        if (null != hostName && !"".equals(hostName)) {
            idGenerator.workerId = Math.abs(hostName.hashCode()) % idGenerator.maxWorkerId;
        } else {
            idGenerator.workerId = new SecureRandom().nextInt(1000) + 1;
        }
    }

    /*public IdGenerator(long workerId, long datacenterId) {
        checkMachineId(workerId, datacenterId);
        this.workerId = workerId;
        this.datacenterId = datacenterId;
    }*/

    public IdGenerator() {}

    private void checkMachineId(long workerId, long datacenterId) {
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(String.format("datacenter Id can't be greater than %d or less than 0", maxDatacenterId));
        }
    }

    private synchronized IdGenerator doSetBits(long workerIdBits, long datacenterIdBits, long sequenceBits) {
        return doSetBits(workerIdBits, datacenterIdBits, sequenceBits, TimeUnit.MILLISECONDS);
    }

    /**
     * 设置工作机器位数 & 序列位数
     * @param workerIdBits
     * @param datacenterIdBits
     * @param sequenceBits
     * @param timeUnit
     * @return
     */
    private synchronized IdGenerator doSetBits(long workerIdBits, long datacenterIdBits, long sequenceBits, TimeUnit timeUnit) {
        // 已使用，不能重新设置
        if (lastTimestamp > 0) {
            throw new UnsupportedOperationException("Id generator is used, can't doSetBits");
        }
        if (timeUnit != TimeUnit.MILLISECONDS && timeUnit != TimeUnit.SECONDS) {
            throw new IllegalArgumentException("timeUnit is unsupported: " + timeUnit);
        }
        // 序列默认位数：毫秒12位|秒22位
        if (sequenceBits == 0) {
            sequenceBits = timeUnit == TimeUnit.MILLISECONDS ? 12L : 22L;
        }
        // >0判断：datacenter可为0
        if (workerIdBits <= 0 || datacenterIdBits < 0 || sequenceBits <= 0) {
            throw new IllegalArgumentException("worker Id Bits | datacenter Id Bits | sequence Bits can't be less than 0");
        }
        // 最大位数：毫秒22位（+时间41位）| 秒32位（+时间31位）
        long maxBits = timeUnit == TimeUnit.MILLISECONDS ? 22 : 32;
        if (workerIdBits + datacenterIdBits + sequenceBits > maxBits) {
            throw new IllegalArgumentException(String.format("worker Id Bits + datacenter Id Bits + sequence Bits can't be greater than %d", maxBits));
        }

        this.workerIdBits = workerIdBits;
        this.datacenterIdBits = datacenterIdBits;
        this.sequenceBits = sequenceBits;
        this.timeUnit = timeUnit;

        this.maxWorkerId = -1L ^ (-1L << workerIdBits);
        this.maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
        this.workerIdShift = sequenceBits;
        this.datacenterIdShift = sequenceBits + workerIdBits;
        this.timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
        this.sequenceMask = -1L ^ (-1L << sequenceBits);
        // 最大序列ID值：毫秒值位数+时间偏移位
        long timeBits = timeUnit == TimeUnit.MILLISECONDS ? dayMillisBits : daySecondsBits;
        this.maxSnId = (1L << (timestampLeftShift + timeBits)) - 1;

        checkMachineId(workerId, datacenterId);
        return this;
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     * @return SnowflakeId
     */
    private synchronized long doGetNextId() {
        long timestamp = timeGen();
        // 系统时钟回退时间
        long backOffset = lastTimestamp - timestamp;
        // 单位转换：秒
        if (timeUnit == TimeUnit.SECONDS) {
            backOffset = DateUtil.toUnixTimeOfCeil(backOffset);
        }
        if (backOffset > 0) {
            // 允许范围内休眠
            if (backOffset <= MAX_BACKWARD) {
                LockSupport.parkNanos(timeUnit.toNanos(backOffset));
            }
            // 超出抛异常
            else {
                throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d %s", backOffset, timeUnit.toString()));
            }
        }
        // 如果是同一时间生成的，则进行序列递增
        if (backOffset == 0) {
            sequence = (sequence + 1) & sequenceMask;
            // 序列溢出
            if (sequence == 0) {
                // 阻塞到下一个毫秒|秒，获得新的时间戳
                timestamp = tilNextTime(lastTimestamp);
            }
        }
        // 时间戳改变，毫秒内序列重置
        else {
            sequence = 0L;
        }
        // 上次生成ID的时间截
        lastTimestamp = timestamp;
        // 移位并通过或运算拼到一起组成64位的ID
        long timeOffset = timestamp - twepoch;
        // 单位转换：秒（往下取整：避免首次重复）
        if (timeUnit == TimeUnit.SECONDS) {
            timeOffset = DateUtil.toUnixTime(timeOffset);
        }
        return (timeOffset << timestampLeftShift) //
                | (datacenterId << datacenterIdShift) //
                | (workerId << workerIdShift) //
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒|秒，直到获得新的时间戳
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    protected long tilNextTime(long lastTimestamp) {
        long nextTimestamp = lastTimestamp + 1;
        // 单位转换：秒
        if (timeUnit == TimeUnit.SECONDS) {
            nextTimestamp = DateUtil.toUnixTimeOfCeil(nextTimestamp) * 1000L;
        }
        long timestamp = timeGen();
        while (timestamp < nextTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 获取id对应的时间（ms）
     * @param id
     * @return
     */
    private long getIdTimeMillis(long id) {
        long elapsedTimes = id >> timestampLeftShift;
        return twepoch + (timeUnit == TimeUnit.MILLISECONDS ? elapsedTimes : elapsedTimes * 1000L);
    }

    /**
     * 获取序号：[日期][bizCode][ID]
     * @param bizCode
     * @param bizCodeLength
     * @return
     */
    private synchronized String doGetNextSn(String bizCode, Integer bizCodeLength) {
        if (StringUtils.isNumeric(bizCode)) {
            return doGetNextSn(0, StringUtils.toFixedLength(String.valueOf(Integer.valueOf(bizCode) % 100), bizCodeLength == null ? 2 : bizCodeLength, '0'));
        } else {
            return doGetNextSn(0, StringUtils.toFixedLength(String.valueOf(bizCode.hashCode() % 100), bizCodeLength == null ? 2 : bizCodeLength, '0'));
        }
    }

    /**
     * 获取序号：[日期][ID]
     * @return
     */
    private synchronized String doGetNextSn() {
        return doGetNextSn(0, null);
    }

    /**
     * 获取序号：[日期][bizCode][ID]
     * @param length 长度，不足补0
     * @param bizCode
     * @return
     */
    private synchronized String doGetNextSn(int length, String bizCode) {
        long id = doGetNextId();
        // 当天所在毫秒|秒值：相对于00:00:00时刻的偏移值
        long dayTimes = timeUnit == TimeUnit.MILLISECONDS ? DateUtil.SECONDS_PER_DAY * 1000L : DateUtil.SECONDS_PER_DAY;
        long newTimeOffset = (id >> timestampLeftShift) % dayTimes;
        long newId = (newTimeOffset << timestampLeftShift) //
                | (datacenterId << datacenterIdShift) //
                | (workerId << workerIdShift) //
                | sequence;

        // 最大长度，不足位补0
        int maxLength = String.valueOf(maxSnId).length();
        if (length > 0) {
            if (length < maxLength) {
                throw new RuntimeException(String.format("Id length is less than %d: %d", maxLength, length));
            }
            maxLength = length;
        }
        String dayFormat = DateUtil.getFormatDate(lastTimestamp, "yyyyMMdd");
        return dayFormat + (bizCode == null ? "" : bizCode) + StringUtils.toFixedLength(String.valueOf(newId), maxLength, '0');
    }

    /**
     * 获取id
     * @return
     */
    public static long nextId() {
        return idGenerator.doGetNextId();
    }

    /**
     * 获取序号：[日期][ID]
     * @return
     */
    public static String nextSn() {
        return idGenerator.doGetNextSn();
    }

    /**
     * 获取序号：[prefix][日期][ID]
     * @return
     */
    public static String nextSn(String prefix) {
        return prefix + idGenerator.doGetNextSn();
    }

    /**
     * 获取序号：[prefix][日期][bizCode][ID]
     * @return
     */
    public static String nextSn(String prefix, String bizCode) {
        return prefix + idGenerator.nextSn(bizCode, 2);
    }

    /**
     * 获取序号：[日期][bizCode][ID]
     * bizCode
     * @param bizCode
     * @param bizCodeLength
     * @return
     */
    public static String nextSn(String bizCode, Integer bizCodeLength) {
        return idGenerator.doGetNextSn(bizCode, bizCodeLength);
    }

    @Override
    public String toString() {
        return "IdGenerator{" +
                "workerId=" + workerId +
                ", datacenterId=" + datacenterId +
                ", workerIdBits=" + workerIdBits +
                ", datacenterIdBits=" + datacenterIdBits +
                ", sequenceBits=" + sequenceBits +
                ", timeUnit=" + timeUnit +
                '}';
    }

    /*public static void main(String[] args) throws InterruptedException {
        long time = System.currentTimeMillis();
        CountDownLatch latch = new CountDownLatch(10);
        for (int i = 0; i < latch.getCount(); i++) {
            int workId = i;
            new Thread(new Runnable() {
                @Override
                public void run() {
                    long curTime = System.currentTimeMillis();
                    printAll();
                    System.out.println("finish: " + (System.currentTimeMillis() - curTime));
                    latch.countDown();
                }
            }).start();
        }
        latch.await();
        System.out.println("finish: " + (System.currentTimeMillis() - time));
    }

    public static void printAll() {
        for (int i = 0; i < 1000; i++) {
            String sn = IdGenerator.nextSn("test", 2);
            System.out.println(sn);
        }
    }*/

    /** 生成UUID */
    public static String uuid() {
        return UUID.randomUUID().toString();
    }

}
