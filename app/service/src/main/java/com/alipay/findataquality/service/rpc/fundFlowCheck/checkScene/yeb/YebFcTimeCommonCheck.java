package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;

/**
 * @ClassName YebCommonCheck
 * @Description FC清算时间检查
 * <AUTHOR>
 * @Date 2025/4/8 14:57
 * @Version V1.0
 **/
public class YebFcTimeCommonCheck extends AbstractCheckScene {
    public YebFcTimeCommonCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "financingcore清算时间检查";
    }

    /****************************************FC减**********************************************************/
    /**
     *  余额宝主子卡fc减：T0大额业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡大额T0清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡减大额T0业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findJsonData("ext_info","t0Large=='Y'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /**
     * 余额宝主子卡fc减：红包业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡红包清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡减红包业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findJsonData("ext_info","VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：过户业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡T0_CHANGE、T1_CHANGE清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡减过户业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findData("business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：调账业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡AGENCY_ADJUST清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡减调账业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findData("business_type == 'AGENCY_ADJUST'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /**
     * 余额宝主子卡fc减：天弘第二直销
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡天弘第二直销清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡减天弘第二直销清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == 'THCFUND'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /**
     * 余额宝主子卡fc减：3008和MYBANK清算时间
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡3008和MYBANK清算时间clear_dt=gmt_commit")
    public RuleCheckResult fc主子卡减3008和MYBANK清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == '3008' || inst_id == 'MYBANK'")
                .findVars("clear_dt","gmt_commit");
        return autoCheckExpression(data,"clear_dt == gmt_commit");
    }

    /**
     * 余额宝主子卡fc减：SHUMIJJ且过户业务(数米小钱袋、悄悄攒、攒着)
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡SHUMIJJ且OUTER_REDEEM清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡减SHUMIJJ且OUTER_REDEEM业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findJsonData("ext_info","!(t0Large=='Y' || VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：数米基金业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡数米基金业务清算时间clear_dt=gmt_commit")
    public RuleCheckResult fc主子卡减数米基金业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findJsonData("ext_info","!(t0Large=='Y' || VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE' || business_type == 'AGENCY_ADJUST' || inst_id == 'THCFUND' || inst_id == '3008' || inst_id == 'MYBANK')")
                .findData("!(inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM')")
                .findData("inst_id == 'SHUMIJJ'")
                .findJsonData("ext_info","yebSaleByShumi == 'T'")
                .findVars("clear_dt","gmt_commit");
        return autoCheckExpression(data,"clear_dt == gmt_commit");
    }

    /**
     * 余额宝主子卡fc减：排除以上条件的其他业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore减主子卡通用业务清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡减通用业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.DECREASE)
                .findJsonData("ext_info","!(t0Large=='Y' || VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE' || business_type == 'AGENCY_ADJUST' || inst_id == 'THCFUND' || inst_id == '3008' || inst_id == 'MYBANK')")
                .findData("!(inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM')")
                .findJsonData("ext_info","yebSaleByShumi != 'T'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /****************************************FC增**********************************************************/
    /**
     * 余额宝主子卡fc增：红包业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡红包清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡增红包业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：过户业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡T0_CHANGE、T1_CHANGE清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡增过户业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：调账业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡AGENCY_ADJUST清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡增调账业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("business_type == 'AGENCY_ADJUST'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /**
     * 余额宝主子卡fc增：3008和MYBANK清算时间
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡3008和MYBANK清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡增3008和MYBANK清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE' || business_type == 'AGENCY_ADJUST')")
                .findData("inst_id == '3008' || inst_id == 'MYBANK'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc增：数米并且为基金支付或者企业申购业务清算时间
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡SHUMIJJ且FINANCE_PURCHASE业务清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡增SHUMIJJ且FINANCE_PURCHASE业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'FINANCE_PURCHASE'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc增：SHUMIJJ且过户业务（数米小钱袋、悄悄攒份额过户、攒着）
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡SHUMIJJ且OUTER_PURCHASE清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡增SHUMIJJ且OUTER_PURCHASE业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc减：排除以上条件的其他业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore增主子卡通用业务清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡增通用业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.INCREASE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(business_type == 'T0_CHANGE' || business_type == 'T1_CHANGE' || business_type == 'AGENCY_ADJUST' || inst_id == '3008' || inst_id == 'MYBANK')")
                .findData("!(inst_id == 'SHUMIJJ' && business_type == 'FINANCE_PURCHASE')")
                .findData("!(inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE')")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /****************************************FC冻结**********************************************************/
    /**
     * 余额宝主子卡fc冻结：红包业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore冻结主子卡红包清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡冻结红包业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.FREEZE)
                .findJsonData("ext_info","VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc冻结：3008和MYBANK清算时间
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore冻结主子卡3008和MYBANK清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡冻结3008和MYBANK清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.FREEZE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("inst_id == '3008' || inst_id == 'MYBANK'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc冻结：数米基金业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore冻结主子卡数米基金业务清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡冻结数米基金业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.FREEZE)
                .findData("inst_id == 'SHUMIJJ'")
                .findJsonData("ext_info","yebSaleByShumi == 'T'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc冻结：排除以上条件的其他业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore冻结主子卡通用业务清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡冻结通用业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.FREEZE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(inst_id == '3008' || inst_id == 'MYBANK')")
                .findJsonData("ext_info","yebSaleByShumi != 'T'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

    /****************************************FC解冻**********************************************************/
    /**
     * 余额宝主子卡fc解冻：红包业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore解冻主子卡红包清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡解冻红包业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.UNFREEZE)
                .findJsonData("ext_info","VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc解冻：3008和MYBANK清算时间
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore解冻主子卡3008和MYBANK清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡解冻3008和MYBANK清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.UNFREEZE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("inst_id == '3008' || inst_id == 'MYBANK'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc解冻：数米基金业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore解冻主子卡数米基金业务清算时间clear_dt=pmt_dt")
    public RuleCheckResult fc主子卡解冻数米基金业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.UNFREEZE)
                .findData("inst_id == 'SHUMIJJ'")
                .findJsonData("ext_info","yebSaleByShumi == 'T'")
                .findVars("clear_dt","pmt_dt");
        return autoCheckExpression(data,"clear_dt == pmt_dt");
    }

    /**
     * 余额宝主子卡fc解冻：排除以上条件的其他业务
     * @param fc
     * @return
     */
    @AutoCheck(ruleDesc = "financingcore解冻主子卡通用业务清算时间clear_dt=biz_dt")
    public RuleCheckResult fc主子卡解冻通用业务清算时间检查(FinancingcoreData fc) {
        AssetRecordData data = fc.findTable(FinancingcoreData.UNFREEZE)
                .findJsonData("ext_info","!(VOUCHERCUSTOP == 'Y' || VOUCHEREPOP == 'Y')")
                .findData("!(inst_id == '3008' || inst_id == 'MYBANK')")
                .findJsonData("ext_info","yebSaleByShumi != 'T'")
                .findVars("clear_dt","biz_dt");
        return autoCheckExpression(data,"clear_dt == biz_dt");
    }

}
