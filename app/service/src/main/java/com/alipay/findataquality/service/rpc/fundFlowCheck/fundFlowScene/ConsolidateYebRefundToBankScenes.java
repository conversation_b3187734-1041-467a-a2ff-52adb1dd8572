package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import com.iwallet.biz.common.util.money.Money;

import java.util.LinkedList;
import java.util.List;

/**
 * 组合余额宝退回银行卡的交易阶段场景，最终得到组合场景
 * -- 理论不存在多次
 */
public class ConsolidateYebRefundToBankScenes implements ConsolidateScenes{
    @Override
    public TransactionComposedScene consolidateStageScenes(List<TransactionStageScene> transactionStageScenes) {
        TransactionComposedScene transactionComposedScene = new TransactionComposedScene();
        StringBuffer composedSceneDesc = new StringBuffer();
        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
        Money sumAmount = new Money(0);
        int transactionTimes = 0;

        for (TransactionStageScene transactionStageScene:transactionStageScenes){
            if (transactionStageScene.getFundFlowStageEnum() == FundFlowStageEnum.YEB_TO_BANK){
                sumAmount=transactionStageScene.getAmount();
                transactionStageSceneList.add(transactionStageScene);
                transactionTimes++;
            }
        }
        if (transactionTimes==0){
            return null;
        }else{
            //组装结果
            composedSceneDesc.append("余额宝退回到银行卡").append(transactionTimes).append("次");//理论不存在多次..
            StringBuffer composedSceneDescFinalBuffer = new StringBuffer();
            composedSceneDescFinalBuffer.append("[金额").append(sumAmount).append("元]").append(composedSceneDesc).append("\n");
            transactionComposedScene.setComposedSceneDesc(composedSceneDescFinalBuffer.toString());
            transactionComposedScene.setSumAmount(sumAmount);
            transactionComposedScene.setTransactionStageSceneList(transactionStageSceneList);
            return transactionComposedScene;
        }
    }
}
