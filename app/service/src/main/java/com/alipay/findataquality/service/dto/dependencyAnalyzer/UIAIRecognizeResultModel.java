package com.alipay.findataquality.service.dto.dependencyAnalyzer;

import java.util.List;
import java.util.Map;

public class UIAIRecognizeResultModel {
    /**
     * 存放静态字段：即该字段与页面元素无关系，不是页面渲染字段
     */
    List<String> noSenseData;

    /**
     * 存放不影响动态数据，只影响页面样式的字段
     */
    Map<String,List<String>> statisticData;

    /**
     * 存放动态字段与页面元素的映射关系：一个字段可能影响多个页面元素
     */
    Map<String, List<UIAIRecognizeKvModel.Area>> relationMap;

    public List<String> getNoSenseData() {
        return noSenseData;
    }

    public void setNoSenseData(List<String> noSenseData) {
        this.noSenseData = noSenseData;
    }

    public Map<String, List<String>> getStatisticData() {
        return statisticData;
    }

    public void setStatisticData(Map<String, List<String>> statisticData) {
        this.statisticData = statisticData;
    }

    public Map<String, List<UIAIRecognizeKvModel.Area>> getRelationMap() {
        return relationMap;
    }

    public void setRelationMap(Map<String, List<UIAIRecognizeKvModel.Area>> relationMap) {
        this.relationMap = relationMap;
    }
}
