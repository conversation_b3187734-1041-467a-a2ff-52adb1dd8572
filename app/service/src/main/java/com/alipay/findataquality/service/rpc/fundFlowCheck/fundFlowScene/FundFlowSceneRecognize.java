package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowScene;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alipay.common.tracer.util.TracerContextUtil;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.service.util.ChatUtil;
import com.alipay.findataquality.utils.common.AmountUtil;
import com.iwallet.biz.common.util.money.Money;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum.*;

/**
 * 根据资金流图识别获取场景列表
 * 1.识别场景列表
 * 2.计算每个资金对象的出入金
 * 3.匹配需要导航到的规则链
 */

//@Component
public class FundFlowSceneRecognize {

    private static final Logger logger = LoggerFactory.getLogger(FundFlowSceneRecognize.class);

    List<FundFlowStageEnum> fundFlowStageEnumList = new LinkedList<>();

    public FundFlowSceneRecognize() {
        initTransactionStageList();
    }

    private void initTransactionStageList() {
        fundFlowStageEnumList = Arrays.asList(BANK_TO_YEB, COUPON_TO_YEB, YEB_CKK_TO_YEB
                , YEB_TO_FUND_DUMMY, YEB_TO_FUND_DUMMY_HQ, YEB_YUE_TO_FUND_DUMMY_HQ
                , YEB_TO_COUPON, YEB_TO_BANK, FUND_DUMMY_TO_YEB_CANCEL, FUND_DUMMY_TO_YEB_TOTALREFUND_03T1,FUND_DUMMY_TO_YEB_TOTALREFUND_03T2
                , FUND_DUMMY_TO_YEB_PARTREFUND_03T1,FUND_DUMMY_TO_YEB_PARTREFUND_03T2);
    }


    /**
     * 识别场景列表
     *
     * @param transactionGroups
     * @param dataJson
     * @return
     */
    public List<TransactionStageScene> recognizeScene(List<TransactionGroup> transactionGroups, String dataJson) {
        //STEP1-初步识别场景列表
        //合并transactionGroups下的所有transactionList
        List<AssetTransaction> allTransactions = transactionGroups.stream()
                .flatMap(group -> group.getTransactionList().stream())
                .filter(assetTransaction -> assetTransaction.getFrom()!=null && assetTransaction.getTo()!=null)
                .collect(Collectors.toList());
        List<TransactionStageScene> transactionStageSceneList = matchFundFlowStage(allTransactions);

        //STEP2-优化场景列表列表：此处针对特殊fundFlow需要再次加工识别
        if (transactionStageSceneList != null && !transactionStageSceneList.isEmpty()) {
            transactionStageSceneList = optimizeFundFlowRecognize(transactionStageSceneList, dataJson);
        }
        return transactionStageSceneList;
    }

    /**
     * 初步识别场景列表
     *
     * @param assetTransactions
     * @return
     */
    public List<TransactionStageScene> matchFundFlowStage(List<AssetTransaction> assetTransactions) {
        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
        int ignoreTimes = 0;
        for (int i = 0; i < assetTransactions.size(); i++) {
            if (ignoreTimes == 0){
                AssetTransaction assetTransaction = assetTransactions.get(i);
                String flow = assetTransaction.getFrom().getPayToolType().getCode();//初始化flow
                // 特殊标识
                boolean isContinueAddFlow = false;//是否需要拼接fundflow
                TransactionStageScene transactionStageScene = matchFundFlowStageRecursive(assetTransactions, i, assetTransaction.getAmount(), flow, isContinueAddFlow);
                if (transactionStageScene != null) {
                    transactionStageSceneList.add(transactionStageScene);
                    //设置跳过次数
                    ignoreTimes = transactionStageScene.getNextStartIndex()-i;
                }
            }
            ignoreTimes--;
        }
        return transactionStageSceneList;
    }

    /**
     * 递归处理场景匹配
     *
     * @param assetTransactions
     * @param index
     * @param lastTotalAmount
     * @param flow
     * @param isContinueAddFlow
     * @return
     */
    private TransactionStageScene matchFundFlowStageRecursive(List<AssetTransaction> assetTransactions, int index, Money lastTotalAmount, String flow, boolean isContinueAddFlow) {
        if (index >= assetTransactions.size()) {
            return null;
        }
        String[] nodeList = flow.split("-");
        String lastTo = flow;
        if (nodeList.length > 1) {
            lastTo = nodeList[nodeList.length - 1];//取flow以"-"分隔后的最后一个节点作为from节点过滤
        }
        String nextFrom = assetTransactions.get(index).getFrom().getPayToolType().getCode();
        if (isContinueAddFlow &&
                ((!lastTo.equals(nextFrom)) || (!lastTotalAmount.equals(assetTransactions.get(index).getAmount())))) {
            //若isContinueAddFlow && lastTo!=nextFrom,则没匹配继续递归
            //若isContinueAddFlow && lastTotalAmount!=transactionViews.get(index),则没匹配继续递归
            return matchFundFlowStageRecursive(assetTransactions, index + 1, lastTotalAmount, flow, true);
        }

        StringBuffer targetFlowBuffer = new StringBuffer(flow);
        String targetFlow = targetFlowBuffer.append("-").append(assetTransactions.get(index).getTo().getPayToolType().getCode()).toString();//拼接flow
        Optional<FundFlowStageEnum> matchingFundFlow = fundFlowStageEnumList.stream()
                .filter(stage -> stage.getFlow().equals(targetFlow))
                .findFirst();
        if (matchingFundFlow.isPresent()) {
            TransactionStageScene transactionStageScene = new TransactionStageScene();
            transactionStageScene.setFundFlowStageEnum(matchingFundFlow.get());
            transactionStageScene.setAmount(lastTotalAmount);//此处也可以设置当前index的amount，因为即使isContinueAddFlow，也满足lastTotalAmount!=transactionViews.get(index).amount
            transactionStageScene.setNextStartIndex(index+1);
            return transactionStageScene;
        } else {
            return matchFundFlowStageRecursive(assetTransactions, index + 1, lastTotalAmount, targetFlow, true);
        }
    }

    /**
     * 基于规则细化交易阶段场景
     * todo：测试
     * @param transactionStageSceneList
     * @param dataJson
     * @return
     */
    public List<TransactionStageScene> optimizeFundFlowRecognize(List<TransactionStageScene> transactionStageSceneList, String dataJson) {
        List<TableData> tableDataList = JSON.parseArray(dataJson, TableData.class);
        for (TransactionStageScene transactionStageScene:transactionStageSceneList){
            // 处理：区分"余额宝普通申购基金"or"余额宝走高保非基金支付申购基金"
           if (transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.YEB_TO_FUND_DUMMY.getFlow())){
               distinctYebToFundPurchase(transactionStageScene,tableDataList);//测试此处是否需要remove和add
           }
           // 处理：区分"基金撤单回余额宝"or"基金全部退款回余额宝"，以及""基金部分退款回余额宝"
           if (transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.YEB_TO_FUND_DUMMY.getFlow())||
                   transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T1.getFlow())||
                   transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.FUND_DUMMY_TO_YEB_TOTALREFUND_03T2.getFlow())||
                   transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1.getFlow())||
                   transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2.getFlow())){
               distinctFundRefundOrCancelToYeb(transactionStageSceneList,transactionStageScene,tableDataList);//测试此处是否需要remove和add
           }
        }

        return transactionStageSceneList;

    }

    /**
     * 处理：区分"余额宝普通申购基金"or"余额宝走高保非基金支付申购基金"
     * @param transactionStageScene
     * @param tableDataList
     * @return
     */
    private void distinctYebToFundPurchase(TransactionStageScene transactionStageScene,List<TableData> tableDataList){
        boolean isHqNonFundPayForFundPurchase = false;
        // 过滤出符合条件的TableData
        TableData yebAssetDecreaseOrders = tableDataList.stream().filter(tb -> tb.getTableName().contains("yeb_asset_decrease_order")).findFirst().get();
        int assetAccountTypeIndex = yebAssetDecreaseOrders.getColumnName().indexOf("asset_account_type");//asset_account_type=fundpay_share
        int businessTypeIndex = yebAssetDecreaseOrders.getColumnName().indexOf("business_type");//business_type=T0_LOAN
        int extInfoIndex = yebAssetDecreaseOrders.getColumnName().indexOf("ext_info");

        for (List<String> column : yebAssetDecreaseOrders.getColumnData()) {
            if ("fundpay_share".equals(column.get(assetAccountTypeIndex)) && "T0_LOAN".equals(column.get(businessTypeIndex))) {
                String extInfo = column.get(extInfoIndex);
                if (extInfo.contains("\"fcHighAvailable\":\"T\"") && extInfo.contains("\"hasYebPay\":\"Y\"")) {
                    isHqNonFundPayForFundPurchase = true;
                }
            }
        }
        if (isHqNonFundPayForFundPurchase) {
            transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY_HQ);//余额宝申购基金走非基金支付链路
        } else {
            transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.YEB_TO_FUND_DUMMY);//余额宝申购基金
        }
    }

    /**
     * 处理：区分"基金撤单回余额宝"or"基金全部退款回余额宝"，以及""基金部分退款回余额宝"
     * @param transactionStageScene
     * @param tableDataList
     * @return
     */
    private void distinctFundRefundOrCancelToYeb(List<TransactionStageScene> transactionStageSceneList,TransactionStageScene transactionStageScene,List<TableData> tableDataList){
        TableData yebAssetIncreaseOrders = tableDataList.stream().filter(tb -> tb.getTableName().contains("yeb_asset_increase_order")).findFirst().get();
        int assetAccountTypeIndex = yebAssetIncreaseOrders.getColumnName().indexOf("asset_account_type");//asset_account_type=fundpay_share
        int businessTypeIndex = yebAssetIncreaseOrders.getColumnName().indexOf("business_type");//business_type=""
        int memoIndex = yebAssetIncreaseOrders.getColumnName().indexOf("memo");//memo="基金退款"
        int extInfoIndex = yebAssetIncreaseOrders.getColumnName().indexOf("ext_info");
        for (List<String> column : yebAssetIncreaseOrders.getColumnData()) {
            if ("fundpay_share".equals(column.get(assetAccountTypeIndex)) && "".equals(column.get(businessTypeIndex))
            && "基金退款".equals(column.get(memoIndex))) {
                String extInfo = column.get(extInfoIndex);
                if (extInfo.contains("\"orderType\":\"fund_purchase_cancel\"")) {
                    transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL);//此处交易为基金撤单后资金退回余额宝，撤单都走03T2
                } else if (extInfo.contains("\"orderType\":\"fund_purchase_confirm\"")) {
                    //基金退款金额小于申购基金金额则为部分退款
                    Optional<TransactionStageScene> matchingStageForYebToFundDummy = transactionStageSceneList.stream()
                            .filter(stageScene -> (stageScene.getFundFlowStageEnum().getFlow().equals(YEB_TO_FUND_DUMMY.getFlow())
                                                    ||stageScene.getFundFlowStageEnum().getFlow().equals(YEB_TO_FUND_DUMMY_HQ.getFlow())
                                                    ||stageScene.getFundFlowStageEnum().getFlow().equals(YEB_YUE_TO_FUND_DUMMY_HQ.getFlow())))
                            .findFirst();
                    if (matchingStageForYebToFundDummy.isPresent()){
                        TransactionStageScene transactionForYebToFundDummy = matchingStageForYebToFundDummy.get();//余额宝申购基金交易阶段
                        int realAmountIndex = yebAssetIncreaseOrders.getColumnName().indexOf("real_amount");
                        Money realAmountMoney= AmountUtil.transferAmountFromCentT0Yuan(column.get(realAmountIndex));//String类型的分转换成Money类型
                        if (transactionForYebToFundDummy.getAmount().greaterThan(realAmountMoney)){//部分退款
                           if (extInfo.contains("\"BIZ_ACTION_TYPE\":\"BATCHPAY\"")){
                               transactionStageScene.setFundFlowStageEnum(FUND_DUMMY_TO_YEB_TOTALREFUND_03T1);//基金全部退款后资金退回余额宝03t1
                           }else if(extInfo.contains("\"BIZ_ACTION_TYPE\":\"SINGLEPAY\"")){
                               transactionStageScene.setFundFlowStageEnum(FUND_DUMMY_TO_YEB_TOTALREFUND_03T2);//基金全部退款后资金退回余额宝03t2
                           }
                        }else{//全部退款
                            if (extInfo.contains("\"BIZ_ACTION_TYPE\":\"BATCHPAY\"")){//全部退款
                                transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T1);//基金部分退款后资金退回余额宝03t1
                            }else if(extInfo.contains("\"BIZ_ACTION_TYPE\":\"SINGLEPAY\"")){
                                transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_PARTREFUND_03T2);//基金部分退款后资金退回余额宝03t2
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 基于规则组合获取交易场景的结论集合
     * 阶段1，硬编码时可使用过滤器模式优化代码
     * todo：阶段2，后续可以将每个交易阶段场景编排出可以场景组合链，并分配组合链对应的处理器组件对真实数据进行场景阶段组合，最终获取得到组合场景集合。
     * @param transactionStageScenes
     * @return
     */
    public List<TransactionComposedScene> consolidateStageScenes(List<TransactionStageScene> transactionStageScenes) {
        List<TransactionComposedScene> transactionComposedScenes = new LinkedList<>();
        //生成一组可编排的组合场景对象
        //1.组合余额宝买入的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateYebPurchaseScene = new ConsolidateYebPurchaseScenes();
        //2.组合余额宝申购买基金的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateYebSellForFundBuyScene = new ConsolidateYebSellForFundBuyScenes();
        //3.组合基金撤单后资金回余额宝的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateFundCancelToYebScene = new ConsolidateFundCancelToYebScenes();
        //4.组合基金全部退款回余额宝的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateFundRefundToYebScene = new ConsolidateFundRefundToYebScenes();
        //5.组合基金部分退款回余额宝的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateFundPartRefundToYebScene = new ConsolidateFundPartRefundToYebScenes();
        //6.组合余额宝退回红包奖励发放的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateYebRefundToCouponBScene = new ConsolidateYebRefundToCouponBScenes();
        //7.组合余额宝退回银行卡的交易阶段场景，最终得到组合场景
        ConsolidateScenes consolidateYebRefundToBankScene = new ConsolidateYebRefundToBankScenes();

        //执行场景组合过滤器
        TransactionComposedScene composedSceneForYebPurchase = consolidateYebPurchaseScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForYebSellFundBuy = consolidateYebSellForFundBuyScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForFundCancelToYeb = consolidateFundCancelToYebScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForFundRefundToYeb = consolidateFundRefundToYebScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForPartFundRefundToYeb = consolidateFundPartRefundToYebScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForYebRefundToCouponB = consolidateYebRefundToCouponBScene.consolidateStageScenes(transactionStageScenes);
        TransactionComposedScene composedSceneForYebRefundToBank = consolidateYebRefundToBankScene.consolidateStageScenes(transactionStageScenes);

        //组装执行结果
        Stream.of(composedSceneForYebPurchase, composedSceneForYebSellFundBuy,composedSceneForFundCancelToYeb
                        ,composedSceneForFundRefundToYeb,composedSceneForPartFundRefundToYeb,composedSceneForYebRefundToCouponB,composedSceneForYebRefundToBank)
                .filter(scene -> scene != null)
                .forEach(transactionComposedScenes::add);
        return transactionComposedScenes;
    }

    /**
     * 一句话推断场景描述
     * @return
     */
    public String generateTransactionSceneSummary(List<TransactionComposedScene> transactionComposedScenes){
        StringBuffer transactionSceneSummary = new StringBuffer();
        if (CollectionUtils.isEmpty(transactionComposedScenes)) {
            return transactionSceneSummary.toString();
        }
        //编写脚本，todo：通过配置化脚本orDB读取来优化
        String traceId= TracerContextUtil.getTraceId();
        StringBuilder systemInfoBuffer = new StringBuilder();
        systemInfoBuffer.append("你是一个资金运营专家，你能根据一组有按时间正序排列的资金流转阶段推测出这是一笔什么交易场景。\n" +
                "此处有几个预测需要遵守的规则：“\n" +
                "规则1：资金流转前后顺序以时间前后顺序为准。\n" +
                "规则2：资金流转过程中若资金流存在从“余额宝攒钱卡申购余额宝”，也存在“余额宝申购基金”，并且这两段资金流涉及的资金不相等并且“余额宝攒钱卡申购余额宝”涉及的金额小于“余额宝申购基金”则表示余额宝自身也发生了出金，则可以推断此交易场景包含阶段为：余额宝攒钱卡+余额宝申购申购基金。\n" +
                "\n" +
                "为更准确的推测交易场景，此处举几个例子，\n" +
                "例子1：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额1元]余额宝攒钱卡申购余额宝；\n" +
                "[金额3元]余额宝申购基金（高保基金支付链路）。”\n" +
                "对应的交易场景是：余额宝攒钱卡+余额宝申购基金（高保基金支付链路）\n" +
                "例子2：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额112.25元]红包+余额宝攒钱卡申购余额宝；\n" +
                "[金额113元]余额宝申购基金（高保基金支付链路）；\n" +
                "[金额113元]基金撤单到余额宝；\n" +
                "[金额0.5元]余额宝红包奖励发放退回。”\n" +
                "对应的交易场景是：红包+余额宝攒钱卡+余额宝申购基金（高保基金支付链路），并且基金撤单到余额宝以及红包退回。\n" +
                "例子3：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额112.25元]银行卡+红包+余额宝攒钱卡申购余额宝；\n" +
                "[金额113元]余额宝申购基金（高保非基金支付链路）；\n" +
                "[金额1.5元]基金部分退回到余额宝；\n" +
                "[金额0.5元]余额宝红包奖励发放退回。”\n" +
                "对应的交易场景是：银行卡+红包+余额宝攒钱卡+余额宝申购基金（高保非基金支付链路），并且基金部分退款到余额宝以及红包退回。\n");
        //补充本次需要推断的交易场景
        StringBuilder callInfoBuffer = new StringBuilder();
        callInfoBuffer.append("现在有一段一组有按时间正序排列的资金运转文本，请推测对应的交易场景是什么。资金流转文本为：\n\"");
        for (TransactionComposedScene transactionComposedScene : transactionComposedScenes) {
            //示例："[金额112.25元]银行卡+余额宝攒钱卡申购余额宝\n"
            callInfoBuffer.append(transactionComposedScene.getComposedSceneDesc());
        }
        callInfoBuffer.append("注意：一句话给出具体交易场景，然后换行并简单给出推理即可，不需要额外的回答");

        //Qwen服务
//      transactionSceneSummary = ChatUtil.onewayChatByQwen(callInfo.toString());
        //百灵服务
        String llmResponse = ChatUtil.onewayChatByBaiLing(systemInfoBuffer.toString(),callInfoBuffer.toString(),traceId);
        if (!StringUtil.isEmpty(llmResponse)) {
//            transactionSceneSummary.append("根据资金流图以及已知数据，可推断得出：").append("\n");
            transactionSceneSummary.append(llmResponse);
            //拼接交易场景推断
            transactionSceneSummary.append("依据：");
        }else{
            transactionSceneSummary.append("根据资金流图以及已知数据，可分析得出交易场景包括以下阶段：");
        }
        transactionSceneSummary.append("\n");
        // 将每个 composedSceneDesc 拼接到 transactionSceneSummary 中
        transactionComposedScenes.stream()
                .map(TransactionComposedScene::getComposedSceneDesc) // 获取每个 composedSceneDesc
                .forEach(desc -> transactionSceneSummary.append(desc)); // 拼接字符串
        logger.info("交易场景推断：{}", transactionSceneSummary.toString());
        return transactionSceneSummary.toString();
    }

}