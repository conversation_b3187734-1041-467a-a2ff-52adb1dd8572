package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.mftrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName MftransTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 13:51
 * @Version V1.0
 **/
public class MftransAccountTask extends AbstractQueryTask {
    public MftransAccountTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.MFTRANS;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from mini_account_mf_${db_flag} where ${mf_account_like_conditions}",
                "select * from mini_account_freeze_mf_${db_flag} where ${mf_account_like_conditions}",
                "select * from mini_main_account_mf_${db_flag} where ${mf_main_account_like_conditions}"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{null,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{null,null,null};
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.copyValueAsLikeType("mf_account_no_list","mf_account_like_conditions","mini_account_no");
        context.copyValueAsLikeType("mf_account_no_list","mf_main_account_like_conditions","mini_main_account_no");
    }
}
