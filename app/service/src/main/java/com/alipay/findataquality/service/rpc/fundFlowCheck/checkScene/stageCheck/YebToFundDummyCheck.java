package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.stageCheck;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.accTransData.AcctransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;

/**
 * 应用场景：FundFlowStageEnum.YEB_TO_FUND_DUMMY（余额宝申购基金）
 *
 */
public class YebToFundDummyCheck extends AbstractCheckScene {

    public YebToFundDummyCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return null;
    }

    /**
     * 测试样例
     * @param fc
     * @param aftrans
     * @param acc
     * @return
     */
    @AutoCheck(ruleDesc = "余额宝Financingcore及分账数据一致性检查")
    public RuleCheckResult checkFcAndAftrans_test(FinancingcoreData fc, AftransTaskData aftrans, AcctransData acc) {
        putVar("testa","fc.real_amount");
        LOGGER.info("fc={},af={},acc={}",fc,aftrans,acc);
        return null;
    }

}
