package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;

/**
 * @ClassName YebCommonCheck
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/8 14:57
 * @Version V1.0
 **/
public class YebFcCommonCheck extends AbstractCheckScene {
    public YebFcCommonCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "financingcore同分账一致性检查";
    }

    @AutoCheck(ruleDesc = "financingcore中全部数据状态为终态S")
    public RuleCheckResult fc所有单据状态为终态S(FinancingcoreData fc) {
        return autoCheckEqual(fc.findVars("status"),"S");
    }

    @AutoCheck(ruleDesc="[主卡增]financingcore同分账落地一致性检查")
    public RuleCheckResult fc主卡增同分账落地一致性检查(FinancingcoreData fcData, AftransTaskData aftransTaskData){
        AssetRecordData left = fcData.findTable(FinancingcoreData.INCREASE).findData("asset_account_type == 'fundpay_share' && asset_account_type != 'distribution'").findVars("user_id","inst_id","real_amount");
        AssetRecordData right = aftransTaskData.findTable(AftransTaskData.AFTRANS_3008,AftransTaskData.AFTRANS_PLUS).findData("order_type == 'INCREASE'").findVars("user_id","inst_id","amount");
        return autoCheckCombineEqual(left,right);
    }

    @AutoCheck(ruleDesc="[主卡减]financingcore同分账落地一致性检查")
    public RuleCheckResult fc主卡减同分账落地一致性检查(FinancingcoreData fcData, AftransTaskData aftransTaskData){
        AssetRecordData left = fcData.findTable(FinancingcoreData.DECREASE).findData("asset_account_type == 'fundpay_share' && asset_account_type != 'distribution'").findVars("user_id","inst_id","real_amount");
        AssetRecordData right = aftransTaskData.findTable(AftransTaskData.AFTRANS_3008,AftransTaskData.AFTRANS_PLUS).findData("order_type == 'DECREASE'").findVars("user_id","inst_id","amount");
        return autoCheckCombineEqual(left,right);
    }

    @AutoCheck(ruleDesc="[主卡冻结]financingcore同分账落地一致性检查")
    public RuleCheckResult fc主卡冻结同分账落地一致性检查(FinancingcoreData fcData, AftransTaskData aftransTaskData){
        AssetRecordData left = fcData.findTable(FinancingcoreData.FREEZE).findData("asset_account_type == 'fundpay_share' && asset_account_type != 'distribution'").findVars("user_id","inst_id","real_amount");
        AssetRecordData right = aftransTaskData.findTable(AftransTaskData.AFTRANS_3008,AftransTaskData.AFTRANS_PLUS).findData("order_type == 'FREEZE' || order_type == 'FZ'").findVars("user_id","inst_id","amount");
        return autoCheckCombineEqual(left,right);
    }

    @AutoCheck(ruleDesc="[主卡解冻]financingcore同分账落地一致性检查")
    public RuleCheckResult fc主卡解冻同分账落地一致性检查(FinancingcoreData fcData, AftransTaskData aftransTaskData){
        AssetRecordData left = fcData.findTable(FinancingcoreData.UNFREEZE).findData("asset_account_type == 'fundpay_share' && asset_account_type != 'distribution'").findVars("user_id","inst_id","real_amount");
        AssetRecordData right = aftransTaskData.findTable(AftransTaskData.AFTRANS_3008,AftransTaskData.AFTRANS_PLUS).findData("order_type == 'UNFREEZE' || order_type == 'UNFZ'").findVars("user_id","inst_id","amount");
        return autoCheckCombineEqual(left,right);
    }
}
