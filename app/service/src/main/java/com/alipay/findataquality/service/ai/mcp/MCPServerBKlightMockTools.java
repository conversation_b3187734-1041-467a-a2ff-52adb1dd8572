package com.alipay.findataquality.service.ai.mcp;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.LocalDateTime;

public class MCPServerBKlightMockTools {

    @Tool(name="getCurrentDateTime",description = "Get the current date and time in the user's timezone")
    String getCurrentDateTime() {
        return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
    }

    @Tool(name="addNewPlan",description = "Get the current date and time in the user's timezone")
    String addNewPlan() {
        return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
    }


}
