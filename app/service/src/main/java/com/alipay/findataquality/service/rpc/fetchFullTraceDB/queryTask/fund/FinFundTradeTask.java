package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
/**
 * @ClassName FinmemberTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 15:26
 * @Version V1.0
 **/
public class FinFundTradeTask extends AbstractQueryTask {
    public static final String fundCommonSet = "fund_combine_id_list";

    public FinFundTradeTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINFUNDTRADE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //首笔预取数
                genSql("fund_trade_refund_${db_flag}","refund_id",fundCommonSet,new String[]{"out_biz_no","original_order_id"},new String[]{"biz_fund_trans_id","biz_fund_order_id","biz_unique_id","cnl_no"}),
                //正式取数开始
                genSql("fund_trade_order_${db_flag}","order_id",fundCommonSet,new String[]{"original_order_id","group_order_id","transcore_order_id"},new String[]{"redeem_order_id","cnl_no"}),
                genSql("fund_group_trade_${db_flag}","trade_id",fundCommonSet,null,new String[]{"group_id","cnl_no","redeem_order_id"}),
                genSql("fund_trade_refund_${db_flag}","refund_id",fundCommonSet,new String[]{"out_biz_no","original_order_id"},new String[]{"biz_fund_trans_id","biz_fund_order_id","biz_unique_id","cnl_no"}),
                genSql("fund_trade_pay_${db_flag}","pay_order_id",fundCommonSet,new String[]{"out_biz_no","biz_unique_id"},new String[]{"downstream_order_id","original_order_id","cnl_no"})
        };
    }

    @Override
    public int[] filterResultIndex() {
        return new int[]{0};
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{
                "user_id->user_id_list,refund_id|out_biz_no|original_order_id|biz_fund_trans_id|biz_fund_order_id|cnl_no|biz_unique_id->"+fundCommonSet,
                "user_id->user_id_list,order_id|original_order_id|group_order_id|transcore_order_id|redeem_order_id|cnl_no->"+fundCommonSet,
                "user_id->user_id_list,trade_id|group_id|redeem_order_id|cnl_no->"+fundCommonSet,
                /**
                 * main_order_id为fa主单号
                */
                "user_id->user_id_list,refund_id|out_biz_no|original_order_id|biz_fund_trans_id|biz_fund_order_id|cnl_no|biz_unique_id->"+fundCommonSet,
                null
        };
    }

    @Override
    public void handleContextEachSql(int index, TableData tableData, QueryContext context) {
        //后续fa取数依赖使用
        if(index == 1){
            autoPutFist(tableData, "extension_info.downstreamPayId", "main_order_id");
        }
        if(index == 3) {
            autoPutFist(tableData, "biz_fund_order_id", "main_order_id");
        }
    }

    @Override
    public void handleContextBefore (QueryContext context) {
        context.copyValueAppend("order_id",fundCommonSet);
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        super.handleContextAfter(context);
        context.copyValueAppend(fundCommonSet,"fa_trans_and_order_list");
    }
}
