package com.alipay.findataquality.service.ai.mcp;

import org.springframework.ai.support.ToolCallbacks;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;

@Configuration(proxyBeanMethods = false)
public class McpServerToolConfiguration {
    @Bean
    public ToolCallbackProvider myTools() {
        List<ToolCallback> tools =  Arrays.asList(ToolCallbacks.from(new MCPServerBKlightMockTools()));
        return ToolCallbackProvider.from(tools);
    }
}
