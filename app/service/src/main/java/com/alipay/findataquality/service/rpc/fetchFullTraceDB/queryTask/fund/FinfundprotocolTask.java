package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.fund;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
/**
 * @ClassName FinfundprotocolTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/22 18:33
 * @Version V1.0
 **/
public class FinfundprotocolTask extends AbstractQueryTask {
    public FinfundprotocolTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINFUNDPROTOCOL_Z90;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from fund_sign_contract_${db_flag} where user_id = '${user_id}'"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[1];
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[1];
    }
}
