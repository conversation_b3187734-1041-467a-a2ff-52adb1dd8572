package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetTransaction;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.accTransData.AcctransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.cardData.InstpayData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.paycoreData.PaycoreData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.prodTransData.ProdtransData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.MftransData;

import java.util.*;
import java.util.stream.Collectors;

import static com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum.*;

/**
 * @ClassName AssetTransGraph
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 23:09
 * @Version V1.0
 **/
public class FundFlowTransGraph {

    private Map<AssetPayToolEnum, List<AssetPayToolEnum>> canTransMap = new HashMap<>();

    private Class[]fundFlowClasses;

    public FundFlowTransGraph() {
        this.initGraph();
        this.initFundFlowClass();
    }

    private void initGraph() {
        canTransMap.put(YEB, Arrays.asList(//余额宝子卡
                YEB_HH, YEB_CQG, YEB_ZQK, YEB_CKK_VIRTUAL, YEB_CROSS, YEB_MCK, YEB_JTB, YEB_MERCHANT,
                //余额、银行卡、红包
                YUE, CARD, YEB_COUPON_B,
                //基金链路补节点
                FUND_DUMMY,
                //余额宝消费，余额宝->泛金融余额户
                YUE_COMMON_B
                ));
        //余额宝子卡反向到余额宝，合花资金搬迁等，走咨询链路赎回到卡、到余额
        canTransMap.put(YEB_HH, Arrays.asList(
                YEB,YUE,CARD,YUE_WE_DEP_ACT,YUE_WE_MYBANK_DEP_ACT,
                //合花子卡消费，子卡->泛金融余额户
                YUE_COMMON_B
        ));
        canTransMap.put(YUE_COMMON_B, Arrays.asList(
                //合花子卡消费退款，外部银联机构退款->泛金融余额户->合花子卡
                YEB_HH,
                //合花子卡消费，合花子卡->泛金融余额户->外部银联机构
                OUTSIDE_INST_CUP
        ));
        canTransMap.put(OUTSIDE_INST_CUP, Arrays.asList(
                //合花子卡消费退款，外部银联机构退款->泛金融余额户->合花子卡
                YUE_COMMON_B
        ));
        canTransMap.put(YEB_CQG, Arrays.asList(YEB,YUE,CARD,YUE_HONEYPAYWALLET,YEB_HH,YUE_WE_DEP_ACT,YUE_WE_MYBANK_DEP_ACT));
        //合花金发放，发放到余额子户，再搬迁到子卡
        canTransMap.put(YUE_WE_DEP_ACT, Arrays.asList(YEB_CQG,YUE));
        canTransMap.put(YEB_ZQK, Arrays.asList(YEB));
        canTransMap.put(YEB_CKK_VIRTUAL, Arrays.asList(YEB));
        canTransMap.put(YEB_MCK, Arrays.asList(YEB));
        //余额、银行卡、红包链路，其中余额->FUND_DUMMY为高保买基金链路
        //余额->余额转账链路
        canTransMap.put(YUE, Arrays.asList(
                YUE,YEB, YEB_HH,YEB_CQG, CARD, FUND_DUMMY, YEB_COUPON_B,
                //合花子卡链路，合花子卡->余额->余额托管子户
                YUE_WE_DEP_ACT
        ));
        //基金泛金融余额户->用户余额、基金专用差错户
        canTransMap.put(YUE_FUND_B, Arrays.asList(YUE,YUE_FUND_SLIP_INNER_ACCOUNT));
        canTransMap.put(CARD, Arrays.asList(YEB, YEB_HH, YEB_CQG, YUE));
        //红包户可入金至余额宝，也可退回至余额泛金融账户
        canTransMap.put(YEB_COUPON_B, Arrays.asList(YEB, YEB_COUPON_B_YUE, YUE));
        canTransMap.put(YEB_COUPON_B_YUE, Arrays.asList(YEB_COUPON_B));
        //基金撤单、赎回链路补节点
        canTransMap.put(FUND_DUMMY, Arrays.asList(YEB));
    }

    /**
     * 初始化资金识别单据类
     * @return
     */
    private void initFundFlowClass(){
        this.fundFlowClasses = new Class[]{
                AcctransData.class,
                InstpayData.class,
                ProdtransData.class,
                PaycoreData.class,
                AftransTaskData.class,
                FinancingcoreData.class,
                MftransData.class
        };
    }

    public Class[] getFundFlowClasses() {
        return fundFlowClasses;
    }

    public void setFundFlowClasses(Class[] fundFlowClasses) {
        this.fundFlowClasses = fundFlowClasses;
    }

    /**
     * 根据资金可转换地图，计算资金操作的可转换路径
     *
     * @param operationList
     * @return
     */
    public List<AssetTransaction> calculateTransPath(List<AssetOperation> operationList) {
        //获取主要账户资金操作
        List<AssetOperation> operations = operationList.stream().filter(
                x -> x.getPayToolType() != null && x.getPayToolType().isMajorAccount()).collect(Collectors.toList());

        //按照时间排序
        operations.sort(Comparator.comparing(AssetOperation::getGmtCreate));
        //安全资金操作先后顺序两两匹配
        List<AssetTransaction> transNodeList = new ArrayList<>();
        Map<AssetOperation, Boolean> operationUsed = new HashMap();

        for (int i = 0; i < operations.size(); i++) {
            AssetOperation first = operations.get(i);
            if (operationUsed.containsKey(first)) {
                continue;
            }
            if (isSingAssetOp(first)) {
                AssetTransaction transaction = new AssetTransaction(first);
                transNodeList.add(transaction);
                operationUsed.put(first, true);
                continue;
            }

            for (int j = i + 1; j < operations.size(); j++) {
                AssetOperation second = operations.get(j);
                if (operationUsed.containsKey(second)) {
                    continue;
                }

                if (isSingAssetOp(second)) {
                    AssetTransaction transaction = new AssetTransaction(second);
                    transNodeList.add(transaction);
                    operationUsed.put(second, true);
                    continue;
                }

                if (operationUsed.containsKey(first) || operationUsed.containsKey(second)) {
                    continue;
                }
                if (canCombine(first, second)) {
                    AssetTransaction transaction = new AssetTransaction(first, second,true);
                    transNodeList.add(transaction);
                    operationUsed.put(first, true);
                    operationUsed.put(second, true);
                }
            }
        }
        //循环放入交易节点
        for (AssetOperation operation : operations) {
            if (!operationUsed.containsKey(operation)) {
                AssetTransaction transaction = new AssetTransaction(operation, null,true);
                transNodeList.add(transaction);
            }
        }
        //根据时间线排序
        Collections.sort(transNodeList);
        //transNodeList.sort();
        return transNodeList;
    }

    private boolean payToolReachable(AssetPayToolEnum from, AssetPayToolEnum to) {
        if (from == null || to == null || canTransMap.get(from) == null) {
            return false;
        }
        return canTransMap.get(from).contains(to);
    }

    /**
     * 单个资金类型操作，如freeze、unfreeze等属于单节点操作
     *
     * @param op
     * @return
     */
    private boolean isSingAssetOp(AssetOperation op) {
        if (op == null) {
            return false;
        }
        return op.getActionType() == AssetActionTypeEnum.FREEZE || op.getActionType() == AssetActionTypeEnum.UNFREEZE;
    }

    private boolean canCombine(AssetOperation from, AssetOperation to) {
        //非空判断
        if (from == null || to == null || from.getActionType() == null || to.getActionType() == null
                || from.getAmount() == null || to.getAmount() == null
                || from.getPayToolType() == null || to.getPayToolType() == null) {
            return false;
        }

        //两个操作需金额相等、资金方向相反，且支付工具存在可达路径才可以合并
        return (from.getAmount().equals(to.getAmount()) &&
                (from.getActionType() == AssetActionTypeEnum.INCREASE && to.getActionType() == AssetActionTypeEnum.DECREASE
                        && payToolReachable(to.getPayToolType(), from.getPayToolType())
                        || from.getActionType() == AssetActionTypeEnum.DECREASE && to.getActionType() == AssetActionTypeEnum.INCREASE
                        && payToolReachable(from.getPayToolType(), to.getPayToolType())
                )&&
                //不同PayTool时，userId需要相同
                (!from.getPayToolType().equals(to.getPayToolType()) && from.getUserId().equals(to.getUserId()) ||
                        //不同PayTool时，若其中一个节点为红包节点、泛金融节点也可以豁免
                        !from.getPayToolType().equals(to.getPayToolType()) &&
                                (
                                        YEB_COUPON_B.equals(from.getPayToolType())
                                        || YEB_COUPON_B.equals(to.getPayToolType())

                                        || YUE_FUND_B.equals(from.getPayToolType())
                                        || YUE_FUND_B.equals(to.getPayToolType())

                                        || YUE_COMMON_B.equals(from.getPayToolType())
                                        || YUE_COMMON_B.equals(to.getPayToolType())

                                        || OUTSIDE_INST_CUP.equals(from.getPayToolType())
                                        || OUTSIDE_INST_CUP.equals(to.getPayToolType())
                                )
                        ||
                        //相同PayTool时，userId和对手方信息需要相同
                        (from.getPayToolType().equals(to.getPayToolType()) && (StrUtil.isEqual(from.getUserId(),to.getOpUserId())||StrUtil.isEqual(to.getUserId(),from.getOpUserId())))
                ));
    }
}
