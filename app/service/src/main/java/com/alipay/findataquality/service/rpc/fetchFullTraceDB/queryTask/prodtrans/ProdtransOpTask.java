package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.prodtrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName ProdtransOpTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/25 15:22
 * @Version V1.0
 **/
public class ProdtransOpTask extends ProdtransTask{
    public ProdtransOpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String opTask() {
        return "op";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
