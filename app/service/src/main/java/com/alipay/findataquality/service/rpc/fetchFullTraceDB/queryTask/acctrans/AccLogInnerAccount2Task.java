package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.acctrans;

import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName AccLogCommonOpTask
 * @Description 余额宝消费场景，资金流：余额宝->余额内部户，捞取余额内部户数据
 * <AUTHOR>
 * @Date 2024/12/25 15:24
 * @Version V1.0
 **/
public class AccLogInnerAccount2Task extends AccLogCommonTask{

    public AccLogInnerAccount2Task(QueryContext queryContext) {
        super(queryContext);
    }

    public String opTask() {
        return "acc_inner2";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
