package com.alipay.findataquality.service.rpc.fundFlowCheck.checkRule;

/**
 * @ClassName AbstractRule
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 17:42
 * @Version V1.0
 **/
public abstract class AbstractCheckRule {
    public abstract String leftTable();
    public abstract String rightTable();

    /**
     * 单边规则
     * 1.左边数据非空
     * 2.右边数据非空
     * 3.左边值判断
     * 4.右边值判断
     *
     */
    /**
     * 左边规则
     * 变量：枚举，字段，值
     * @return
     */
    public abstract Object[][]leftCheck();
    public abstract Object[][]rightCheck();

    public abstract boolean rowCntPreCheck(int leftCnt,int rightCnt);

    /**
     * 双边规则
     * 1.左右相等, =（按照最小数据条数的作为比对量，依次从上到下比对），支持#表示扩展字段
     * 2.左边约束右边{I,S}->{D,F}
     * 3.左边=右边sum, =sum
     * 4.左边sum=右边, sum=
     * 5.右边约束左边{I,S}<-{D,F}
     * 6.左右条数相等
     * 参数：枚举，左字段，右字段，关系
     */
    public abstract Object[][]crossCheckEach();

    public abstract Object[][]crossCheckAll();

}
