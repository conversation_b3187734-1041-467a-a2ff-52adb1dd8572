/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.findataquality.service.dto;

import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @version : HomologousSourceValidateResultDTO.java, v 0.1 2025年03月17日 10:20 zhaolinling Exp $
 */
public class HomologousSourceValidateResultDTO {
    boolean success = false;
    String resultDesc;
    private JSONObject matchedData;
    private JSONObject matchedStandard;
    HomologousSourceCheckResultDTO checkResult;

    public HomologousSourceValidateResultDTO() {

    }

    public HomologousSourceValidateResultDTO(boolean success, String resultDesc, JSONObject matchedData, JSONObject matchedStandard, HomologousSourceCheckResultDTO checkResult) {
        this.success = success;
        this.resultDesc = resultDesc;
        this.matchedData = matchedData;
        this.matchedStandard = matchedStandard;
        this.checkResult = checkResult;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    public HomologousSourceCheckResultDTO getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(HomologousSourceCheckResultDTO checkResult) {
        this.checkResult = checkResult;
    }

    public JSONObject getMatchedData() {
        return matchedData;
    }

    public void setMatchedData(JSONObject matchedData) {
        this.matchedData = matchedData;
    }

    public JSONObject getMatchedStandard() {
        return matchedStandard;
    }

    public void setMatchedStandard(JSONObject matchedStandard) {
        this.matchedStandard = matchedStandard;
    }
}