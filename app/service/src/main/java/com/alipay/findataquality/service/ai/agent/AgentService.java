package com.alipay.findataquality.service.ai.agent;

import com.alipay.sofa.ai.agent.Agent;
import com.alipay.sofa.ai.agent.AgentExecuteContext;
import com.alipay.sofa.ai.agent.AgentExecutor;
import com.alipay.sofa.ai.agent.react.ReActAgent;
import com.alipay.sofa.ai.codefuse.CodeFuseChatModel;
import com.alipay.sofa.ai.codefuse.CodeFuseChatOptions;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Arrays;
import java.util.Map;

@Component
public class AgentService {

    @Autowired(required = false)
    private AgentExecutor agentExecutor;

    @Autowired(required = false)
    private CodeFuseChatModel codeFuseChatClient;

    public String useReactAgent(String question) {
        PromptTemplate template = new PromptTemplate("请根据下述用户的提问，查询对应的天气信息: {input}");
        Agent agent = new ReActAgent(codeFuseChatClient, Arrays.asList("天气查询工具"));
        Map<String, Object> additionalVariables = new java.util.HashMap<>();
        additionalVariables.put("input", question);
        ChatResponse response = agentExecutor.execute(agent, new Prompt(template.render(additionalVariables),
                CodeFuseChatOptions.builder().withModel("TOOL_GPT3").build()));
        return response.getResult().getOutput().getText();
    }

    public Flux<String> useStreamReactAgent(String question) {
        PromptTemplate template = new PromptTemplate("请根据下述用户的提问，查询对应的天气信息: {input}");
        Agent agent = new ReActAgent(codeFuseChatClient, Arrays.asList("天气查询工具"));
        Map<String, Object> additionalVariables = new java.util.HashMap<>();
        additionalVariables.put("input", question);
        Flux<AgentExecuteContext> flux = agentExecutor.executeStream(agent, new Prompt(template.render(additionalVariables),
                CodeFuseChatOptions.builder().withModel("TOOL_GPT3").build()));
        return flux.map(context -> String.format("Status: %s, stage: %s",context.getStatus(),
                context.getStage()));
    }
}
