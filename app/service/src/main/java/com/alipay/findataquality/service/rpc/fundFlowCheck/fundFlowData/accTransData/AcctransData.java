package com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.accTransData;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetOperation;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;
import com.alipay.sofa.common.utils.StringUtil;
import com.iwallet.biz.common.util.money.Money;

import java.util.Locale;

/**
 * @ClassName AcctransData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 15:18
 * @Version V1.0
 **/
public class AcctransData extends AssetRecordData {

    final static String couponBAccount = "****************";

    public static final String IW_ACCOUNT_LOG = "acclog.iw_account_log";

    @Override
    public String[] dataTable() {
        return new String[]{
                IW_ACCOUNT_LOG
        };
    }

    @Override
    public AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex) {
        AssetOperation assetOperation = AssetOperation.createBaseOperation(tableData,dataIndex,"trans_account","trans_account","trans_amount");
        //提取traceId
        extractTraceId(assetOperation);
        String assetCode = tableData.getColumnIndexValue("asset_code",dataIndex);
        String transAccount = tableData.getColumnIndexValue("trans_account",dataIndex);

        if(AssetPayToolEnum.YUE_HONEYPAYWALLET.getProductCode().equals(assetCode)){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_HONEYPAYWALLET);
        }
        //泛金融余额户，余额宝红包申购使用
        else if(AssetPayToolEnum.YEB_COUPON_B_YUE.getProductCode().equals(assetCode)){
            assetOperation.setPayToolType(AssetPayToolEnum.YEB_COUPON_B_YUE);
        }
        //合花场景，余额托管子户
        else if(AssetPayToolEnum.YUE_WE_DEP_ACT.getProductCode().equals(assetCode)){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_WE_DEP_ACT);
        }
        //合花场景，网商托管子户
        else if(AssetPayToolEnum.YUE_WE_MYBANK_DEP_ACT.getProductCode().equals(assetCode)){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_WE_MYBANK_DEP_ACT);
        }
        //基金泛金融余额户，线下固定为29100000000015000156
        else if("29100000000015000156".equals(transAccount)){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_FUND_B);
        }
        //以281开头的作为泛金融余额户，使用场景为余额宝消费，资金流余额宝->泛金融余额户
        else if(transAccount!=null&&transAccount.startsWith("281")){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_COMMON_B);
        }
        //类型为余额
        else if(AssetPayToolEnum.YUE.getProductCode().equals(assetCode)){
            assetOperation.setPayToolType(AssetPayToolEnum.YUE);
        }
        else{
            //类型为余额托管子户兜底
            assetOperation.setPayToolType(AssetPayToolEnum.YUE_COMMON_DEP_ACT);
        }
        //余额账号为0156结尾，需要截去，仅保留16位uid部分
        if(StringUtil.isNotBlank(assetOperation.getUserId())&& assetOperation.getUserId().length()>16){
            assetOperation.setUserId(assetOperation.getUserId().substring(0,16));
        }
        if(assetOperation.getAmount().greaterThan(new Money(0))){
            assetOperation.setActionType(AssetActionTypeEnum.INCREASE);
        }else{
            //若为decrease，余额中记录为负，此处记录为正数
            assetOperation.setActionType(AssetActionTypeEnum.DECREASE);
            assetOperation.setAmount(assetOperation.getAmount().multiply(-1));
        }

        /**
         * 更新对手方信息
         */
        String otherAccount = assetOperation.getColumnValue("other_account");
        if(StringUtil.isNotBlank(otherAccount)&&otherAccount.length()>16){
            assetOperation.setOpUserId(otherAccount.substring(0,16));
        }

        /**
         * 若资金账户为****************，则该账号认为是泛金融出资账户，即余额宝红包出资户
         */
        if(couponBAccount.equals(assetOperation.getUserId())){
            assetOperation.setPayToolType(AssetPayToolEnum.YEB_COUPON_B_YUE);
            return assetOperation;
        }

        /**
         * 高保买基金场景，从余额出，sub_trans_code为301866，inst_id为SHUMIJJ，trans_institution为3008，作为通过余额高保买基金
         * 对于该场景，需要补一个基金的虚拟交易对手方节点
         * 线上数据参考
         * select * from iw_account_log_00 where trans_account='20881226015990010156' and iw_account_log_id = '***************'
         */
        String subTransCode = assetOperation.getColumnValue("sub_trans_code");
        String instId = assetOperation.getColumnValue("inst_id");
        String transInstitution = assetOperation.getColumnValue("trans_institution");
        if(StringUtil.isNotBlank(subTransCode)&&StringUtil.isNotBlank(instId)&&StringUtil.isNotBlank(transInstitution)){
            if(("301866").equals(subTransCode)&&("SHUMIJJ").equals(instId)&&("3008").equals(transInstitution)&&assetOperation.getActionType()==AssetActionTypeEnum.DECREASE){
                AssetOperation operationDummy = assetOperation.clone();
                operationDummy.setActionType(AssetActionTypeEnum.INCREASE);
                operationDummy.setPayToolType(AssetPayToolEnum.FUND_DUMMY);
                this.getAssetOperationData().getOperationList().add(operationDummy);
            }
        }
        return assetOperation;
    }

    private void extractTraceId(AssetOperation assetOperation){
        String content = assetOperation.getColumnValue("ext_params");
        if(StringUtil.isNotBlank(content)&&content.contains("traceId")){
            String[] splits = content.split("\\^");
            for (String item:splits) {
                String[] list = item.split("=");
                if(list.length!=2){
                    continue;
                }
                if("traceid".equals(list[0].trim().toLowerCase(Locale.ROOT))){
                    assetOperation.setTraceId(list[1].trim());
                }
            }
        }
    }
}
