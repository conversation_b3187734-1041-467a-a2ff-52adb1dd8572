package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName FinancingcoreTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/23 11:06
 * @Version V1.0
 **/
public class FinancingcoreCommonTask extends AbstractQueryTask {
    public FinancingcoreCommonTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String[] queryDependency() {
        return new String[]{"db_flag","date"};
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.FINANCINGCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select * from yeb_asset_increase_order_${db_flag} where asset_order_id in (${fc_combine_order_list}) or (asset_order_id like '${date}%' and (biz_no in (${fc_combine_order_list}) or out_biz_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list})))",
                "select * from yeb_asset_decrease_order_${db_flag} where asset_order_id in (${fc_combine_order_list}) or (asset_order_id like '${date}%' and (biz_no in (${fc_combine_order_list}) or out_biz_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list})))",
                "select * from yeb_asset_freeze_order_${db_flag} where asset_order_id in (${fc_combine_order_list}) or (asset_order_id like '${date}%' and (biz_no in (${fc_combine_order_list}) or out_biz_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list})))",
                "select * from yeb_asset_unfreeze_order_${db_flag} where asset_order_id in (${fc_combine_order_list}) or (asset_order_id like '${date}%' and (biz_no in (${fc_combine_order_list}) or out_biz_no in (${fc_combine_order_list}) or cnl_no in (${fc_combine_order_list})))",
                "select * from yeb_asset_instruction_${db_flag} where instruction_id like '${date}%' and asset_order_id in (${asset_order_id})",
                "select * from yeb_asset_batch_mftrans_order_${db_flag} where asset_order_id in (${asset_order_id})"
        };
    }

    @Override
    public String[] outcomeValue() {
        String keys = "asset_order_id->fc_combine_order_list,user_id->user_id_list,asset_order_id,payment_id,payment_id->acc_log_order_list,biz_no,asset_account_no->prodtrans_principal_id_list,user_id->prodtrans_principal_id_list,asset_account_no->common_mf_account_no_list,ext_info.fcTraceId->trace_id";
        return new String[]{keys,keys,keys,keys,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType() {
        return new OutTypeEnum[]{OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,OutTypeEnum.ARRAY_APPEND,null,null};
    }

    @Override
    public void handleContextAfter(QueryContext context) {
        context.copyValueAppend("fc_combine_order_list","acc_log_order_list");
        context.copyValueAppend("fc_combine_order_list","prodtrans_out_biz_no_list");

    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context){
        //获取uid，后续取账号信息等依赖
        String userId = tableData.getFirstValue("user_id");
        context.putGlobal("user_id",userId);

        //从扩展字段中提取对手方信息
        if(sqlIndex!=0){
            return;
        }
        String[] extInfoList = tableData.getArrayValueList("ext_info");
        if(extInfoList!=null){
            for(String extInfo:extInfoList){
                String opUserId = StrUtils.getJsonValue(extInfo,"fcOpUserId",false);
                if(StringUtil.isNotBlank(opUserId)){
                    context.putGlobal("op_db_flag",StrUtils.substrFromEnd(opUserId,3,2));
                    context.putGlobal("op_db_schema","0"+StrUtils.substrFromEnd(opUserId,3,1));
                    String dbr = DbUtils.getDbrFromDbFlag(context.getValue("op_db_flag"));
                    context.putGlobal("op_db_r",dbr);
                    break;
                }
            }
        }
    }
}
