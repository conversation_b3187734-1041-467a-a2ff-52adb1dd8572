package com.alipay.findataquality.service.rpc.fetchFullTraceDB.model;


import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;
import com.alipay.sofa.common.utils.StringUtil;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

/**
 * @ClassName QueryContext
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 17:37
 * @Version V1.0
 **/
public class QueryContext {
    //输入字段值
    private String inputValue;

    //数据库连接配置
    private Map<QueryDBEnum,DBConnectConfig> dbConnectConfigMap;

    //任务执行线程池
    private ExecutorService executorService;

    private Map<String,String> globalParams = new ConcurrentHashMap<>();


    /**
     * 获取变量值
     * @param key
     * @return
     */
    public String getValue(String key){
        return this.globalParams.get(key);
    }

    /**
     * 放置全局变量
     * @param key
     * @param value
     */
    public void putGlobal(String key,String value){
        if(key==null||value ==null){
            return;
        }
        this.globalParams.put(key,value);
    }

    /**
     * 在原有两边后按照数组形式追加
     * @param key
     * @param value
     */
    public void appendAsArray(String key,String value){
        if(key==null||value==null){
            return;
        }
        String wrapValue = StrUtils.autoWrapStr(value,"'");
        if(this.globalParams.containsKey(key)){
            this.globalParams.put(key,this.globalParams.get(key)+","+wrapValue);
        }else{
            this.globalParams.put(key,wrapValue);
        }
    }

    /**
     * 添加前判重
     * @param key
     * @param value
     */
    public void appendAsArrayUnique(String key,String value){
        if(StringUtil.isBlank(key)||StringUtil.isBlank(value)){
            return;
        }
        String current = this.getValue(key);
        String appendResult = StrUtils.autoJoinArrayUnique(current,value,'\'');
        if(!StringUtil.isBlank(appendResult)){
            putGlobal(key,appendResult);
        }
    }

    /**
     * 将原始值赋值给目标值
     * @param srcKey
     * @param destKey
     */
    public void copyValue(String srcKey, String destKey){
        if(this.globalParams.get(srcKey)!=null){
            this.globalParams.put(destKey,this.globalParams.get(srcKey));
        }
    }

    /**
     * 将原始值追加给目标值
     * @param srcKey
     * @param destKey
     */
    public void copyValueAppend(String srcKey, String destKey){
        String srcStr = this.globalParams.get(srcKey);
        if(!StringUtil.isBlank(srcStr)){
            String joinStr = StrUtils.autoJoinArrayUnique(this.getValue(destKey),srcStr,'\'');
            if(!StringUtil.isBlank(joinStr)){
                this.globalParams.put(destKey,joinStr);
            }
        }
    }

    /**
     * 将原始值追加给目标值
     * @param srcKey
     * @param destKey
     */
    public void copyValueAsLikeType(String srcKey, String destKey, String likeVar){
        String srcStr = this.globalParams.get(srcKey);
        if(!StringUtil.isBlank(srcStr)){
            String joinStr = StrUtils.autoJoinSqlLikeCondition(likeVar,srcStr,false);
            if(!StringUtil.isBlank(joinStr)){
                this.globalParams.put(destKey,joinStr);
            }
        }
    }

    public void copyValueAsLikeWrapHead(String srcKey, String destKey, String likeVar){
        String srcStr = this.globalParams.get(srcKey);
        if(!StringUtil.isBlank(srcStr)){
            String joinStr = StrUtils.autoJoinSqlLikeCondition(likeVar,srcStr,true);
            if(!StringUtil.isBlank(joinStr)){
                this.globalParams.put(destKey,joinStr);
            }
        }
    }

    /**
     * 将原有值添加左右两边操作符，如 (a,"'")，则更新后为 'a'
     * 若原有值本身两边已经添加操作符，使用autoWrapStr方法不会重复添加
     * @param key
     * @param wrap
     */
    public void updateValueWithWrap(String key,String wrap){
        String value = this.getValue(key);
        if(value!=null&&wrap!=null){
            this.globalParams.put(key, StrUtils.autoWrapStr(value,wrap));
        }
    }

    public String getInputValue() {
        return inputValue;
    }

    public void setInputValue(String inputValue) {
        this.inputValue = inputValue;
    }

    public Map<String, String> getGlobalParams() {
        return globalParams;
    }

    public void setGlobalParams(Map<String, String> globalParams) {
        this.globalParams = globalParams;
    }

    public Map<QueryDBEnum, DBConnectConfig> getDbConnectConfigMap() {
        return dbConnectConfigMap;
    }

    public void setDbConnectConfigMap(Map<QueryDBEnum, DBConnectConfig> dbConnectConfigMap) {
        this.dbConnectConfigMap = dbConnectConfigMap;
    }

    public ExecutorService getExecutorService() {
        return executorService;
    }

    public void setExecutorService(ExecutorService executorService) {
        this.executorService = executorService;
    }
}
