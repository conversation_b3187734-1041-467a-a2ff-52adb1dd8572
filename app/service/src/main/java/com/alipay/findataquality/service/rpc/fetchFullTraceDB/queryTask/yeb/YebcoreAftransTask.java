package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreAftransTask extends AbstractQueryTask {

    public YebcoreAftransTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //分账任务捞取
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${order_no}) or cnl_no in (${order_no}))",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${order_no}) or cnl_no in (${order_no}))",
                "select * from yebs_subcard_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no in (${order_no}) or cnl_no in (${order_no}))"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[3];
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[3];
    }

    @Override
    public void handleContextBefore(QueryContext context) {
        context.updateValueWithWrap("order_no","'");
    }
}
