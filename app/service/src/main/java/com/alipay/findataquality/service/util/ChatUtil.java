package com.alipay.findataquality.service.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.common.tracer.util.TracerContextUtil;
import com.alipay.maya.MayaClient;
import com.alipay.maya.config.MayaClientConfig;
import com.alipay.sofa.ai.max.MaxChatModel;
import com.alipay.sofa.ai.max.MaxChatOptions;
import com.alipay.sofa.ai.max.api.MaxChatServiceApi;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.LinkedList;
import java.util.List;
import java.util.zip.GZIPInputStream;

public class ChatUtil {

    private static final Logger logger = LoggerFactory.getLogger(ChatUtil.class);

    private static final String Qwen_app_token = "f3574380-739c-4e38-848d-f6fae16a8d7d";

    private static final String BAILING_URL_PROD = "https://antchat.alipay.com/v1/chat/completions";

    private static final String BAILING_API_KEY = "Bearer DHM7cyawt17OydNP9LggPGM2IeuElxVM";

    private static final String BAILING_MODEL_TYPE = "Bailing-4.0-80B-16K-Chat";


    public static void main(String[] args){
        String traceId= TracerContextUtil.getTraceId();

        StringBuffer systemInfoBuffer = new StringBuffer();
        systemInfoBuffer.append("你是一个资金运营专家，你能根据一组有按时间正序排列的资金流转阶段推测出这是一笔什么交易场景。\n" +
                "此处有几个预测需要遵守的规则：“\n" +
                "规则1：资金流转前后顺序以时间前后顺序为准。\n" +
                "规则2：资金流转过程中若资金流存在从“余额宝攒钱卡申购余额宝”，也存在“余额宝申购基金”，并且这两段资金流涉及的资金不相等并且“余额宝攒钱卡申购余额宝”涉及的金额小于“余额宝申购基金”则表示余额宝自身也发生了出金，则可以推断此交易场景包含阶段为：余额宝攒钱卡+余额宝申购申购基金。\n" +
                "\n" +
                "为更准确的推测交易场景，此处举几个例子，\n" +
                "例子1：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额1元]余额宝攒钱卡申购余额宝；\n" +
                "[金额3元]余额宝申购基金（高保基金支付链路）；\n" +
                "对应的交易场景是：余额宝攒钱卡+余额宝申购基金（高保基金支付链路）\n" +
                "\n" +
                "例子2：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额112.25元]红包+余额宝攒钱卡申购余额宝；\n" +
                "[金额113元]余额宝申购基金（高保基金支付链路）；\n" +
                "[金额113元]基金撤单到余额宝\n" +
                "[金额0.5元]余额宝红包奖励发放退回”\n" +
                "对应的交易场景是：红包+余额宝攒钱卡+余额宝申购基金（高保基金支付链路），并且基金撤单到余额宝以及红包退回。\n" +
                "\n" +
                "例子3：一组有按时间正序排列的资金流转阶段：\n" +
                "“[金额112.25元]银行卡+红包+余额宝攒钱卡申购余额宝；\n" +
                "[金额113元]余额宝申购基金（高保非基金支付链路）；\n" +
                "[金额1.5元]基金部分退回到余额宝\n" +
                "[金额0.5元]余额宝红包奖励发放退回”\n" +
                "对应的交易场景是：银行卡+红包+余额宝攒钱卡+余额宝申购基金（高保非基金支付链路），并且基金部分退款到余额宝以及红包退回。\n");

        //补充本次需要推断的交易场景
        StringBuffer callInfoBuffer = new StringBuffer();
        callInfoBuffer.append("现在有一段一组有按时间正序排列的资金运转文本，请推测对应的交易场景是什么。资金流转文本为：\n\""+
                "[金额112.25元]银行卡+余额宝攒钱卡申购余额宝；\n" +
                "[金额113元]余额宝申购基金（高保非基金支付链路）；\n" +
                "[金额113元]基金撤单到余额宝\n" +
                "[金额0.5元]余额宝退回银行卡\n\"");
        callInfoBuffer.append("注意：一句话给出具体交易场景，然后换行并简单给出推理即可，不需要额外的回答");

        String response = onewayChatByBaiLing(systemInfoBuffer.toString(),callInfoBuffer.toString(),traceId);
        logger.info("推测模型：{}",BAILING_MODEL_TYPE);
        logger.info("response:{}",response);

//        System.out.println(BAILING_MODEL_TYPE+"推断场景");
//        System.out.println(response);

    }

    /**
     * Bailing单次对话,同步对话-HTTP请求
     * @param systemInfo
     * @param callInfo
     * @param sofaTraceId
     * @return
     */
    public static String onewayChatByBaiLing(String systemInfo,String callInfo,String sofaTraceId) {
        String responseContent = "";

        JSONObject requestJsonObj = new JSONObject();
        requestJsonObj.put("model",BAILING_MODEL_TYPE);

        List<JSONObject> messages = new LinkedList<>();
        JSONObject messageJsonObj = new JSONObject();
        messageJsonObj.put("role","system");
        messageJsonObj.put("content",systemInfo);
        messages.add(messageJsonObj);
        JSONObject userJsonObj = new JSONObject();
        userJsonObj.put("role","user");
        userJsonObj.put("content",callInfo);
        messages.add(userJsonObj);
        requestJsonObj.put("messages",messages);

        logger.info("Bailing-http-request:{}",requestJsonObj.toString());
        String response = httpPost(BAILING_URL_PROD, requestJsonObj.toString(), BAILING_API_KEY);
        logger.info("Bailing-http-response:{}",response);

        //提取返回内容
        JSONObject jsonObject = JSON.parseObject(response);
        // 获取choices数组
        JSONArray choicesArray = jsonObject.getJSONArray("choices");
        // 获取第一个choice的message.content
        if (choicesArray != null && choicesArray.size() > 0) {
            JSONObject firstChoice = choicesArray.getJSONObject(0);
            responseContent = firstChoice.getJSONObject("message").getString("content");
        } else {
            logger.error("LLM没返回结果。");
        }
        return responseContent;
    }

    /**
     * Qwen单次对话,同步对话-TR请求
     * @param input
     * @return
     */
    public static String onewayChatByQwen(String input) {
        String res = "";
        //初始化maxModel
        MayaClientConfig config = new MayaClientConfig();
        MaxChatServiceApi maxChatServiceApi = new MaxChatServiceApi(MayaClient.getInstance(config));
        MaxChatModel chatClient = new MaxChatModel(maxChatServiceApi,
                MaxChatOptions.builder()
                        .withSceneName("Qwen2_72B_Instruct_vllm")
                        .withChainName("v1")
                        .withModelType("OPENAI")
                        .withAppToken(Qwen_app_token)
                        .build());
        //同步
        try{
            ChatResponse response = chatClient.call(
                new Prompt(input));
            res = response.toString();
        } catch (Exception e) {
            logger.error("Max chat error: ", e);
        }
        return res;
    }

    /**
     * Qwen单次对话,流式对话-TR请求
     * @param input
     * @return
     */
    public static String streamChatByQwen(String input) {
        //初始化maxModel
        MayaClientConfig config = new MayaClientConfig();
        MaxChatServiceApi maxChatServiceApi = new MaxChatServiceApi(MayaClient.getInstance(config));
        MaxChatModel chatClient = new MaxChatModel(maxChatServiceApi,
                MaxChatOptions.builder()
                        .withSceneName("Qwen2_72B_Instruct_vllm")
                        .withChainName("v1")
                        .withModelType("OPENAI")
                        .withAppToken(Qwen_app_token)
                        .build());
        //流式
        Flux<ChatResponse> response = chatClient.stream(
                new Prompt(input));
        return response.toStream().toString();
    }


    /**
     * http post请求
     * @param urlString
     * @param jsonInputString
     * @param token
     * @return
     */
    public static String httpPost(String urlString,String jsonInputString,String token){
        StringBuilder response = new StringBuilder();
        try {
            // 创建URL对象
            URL url = new URL(urlString);
            // 打开连接
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置请求方法为POST
            connection.setRequestMethod("POST");
            // 添加请求头
            connection.setRequestProperty("Authorization", token);
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true); // 允许输出请求体

            // 发送请求体
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonInputString.getBytes("utf-8");
                os.write(input, 0, input.length);
            }

            // 获取响应状态码
            int responseCode = connection.getResponseCode();
            logger.info("http-Response Code :{} " , responseCode);

            // 处理响应
            InputStream responseStream;
            String contentEncoding = connection.getHeaderField("Content-Encoding");

            if ("gzip".equalsIgnoreCase(contentEncoding)) {
                responseStream = new GZIPInputStream(connection.getInputStream());
            } else {
                responseStream = connection.getInputStream();
            }

            // 读取响应内容
            try (BufferedReader br = new BufferedReader(new InputStreamReader(responseStream, "utf-8"))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                // 打印响应内容
//                logger.info("http-Response : {}" , response.toString());
            }

        } catch (Exception e) {
            logger.error("http-post error: ", e);
        }
        return response.toString();
    }


}
