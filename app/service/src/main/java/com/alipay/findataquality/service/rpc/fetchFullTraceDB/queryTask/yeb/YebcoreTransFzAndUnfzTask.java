package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreTransFzAndUnfzTask extends AbstractQueryTask {

    public YebcoreTransFzAndUnfzTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                //直代销转入表
                "select * from yebs_freeze_order_${db_flag} where order_no = '${order_no}'",
                "select * from fmp_yeb_fz_order_${db_flag} where order_no = '${order_no}'",
                //查询对应微账号信息，用于后续查询
                "select mf_account_no,af_account_no from yebs_sub_contract_${db_flag} where user_id = '${user_id}' and product_type='0100' and mf_account_no is not null and af_account_no is not null and status ='A'",
                "select * from fmp_yeb_ext_${db_flag} where order_no = '${order_no}'",
                //分账任务捞取
                "select * from yebs_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no = '${order_no}' or cnl_no = '${order_no}')",
                "select * from yeb_aftrans_task_${db_flag} where id like '${date}%' and (biz_order_no = '${order_no}' or cnl_no = '${order_no}')"
        };
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{"user_id","user_id","mf_account_no,af_account_no",null,null,null};
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[]{OutTypeEnum.SINGLE,OutTypeEnum.SINGLE,OutTypeEnum.SINGLE,null,null,null};
    }

    @Override
    public int[] filterResultIndex() {
        return new int[]{2};
    }
}
