package com.alipay.findataquality.service.rpc.fundFlowCheck.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.HomologousSourceCheckService;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.BusinessErrorEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.BusinessException;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.HomologousSourceModel;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowCheckRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowHomoCheckResult;
import com.alipay.findataquality.service.dto.*;
import com.alipay.findataquality.service.dto.HomologousSourceValidateResultDTO;
import com.alipay.findataquality.service.repository.FullTraceSameOriginNoCoreConfigRepository;
import com.alipay.findataquality.service.repository.FullTraceSameOriginValidateBaselineRepository;
import com.alipay.findataquality.service.repository.FullTraceValidateDataFetchRecordRepository;
import com.alipay.sofa.common.utils.StringUtil;
import com.alipay.sofa.rpc.api.annotation.RpcProvider;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

@RpcProvider
public class HomologousSourceCheckServiceImpl implements HomologousSourceCheckService {

    private static final Logger logger = LoggerFactory.getLogger(HomologousSourceCheckService.class);

    static final String[] supportTableName = new String[]{"yeb_asset_increase_order","yeb_asset_decrease_order","yeb_asset_freeze_order","yeb_asset_unfreeze_order"};
    static final String[] YEBASSETINCREASEORDER_BUSINESS_TARGET_KEYS = new String[]{"business_type","asset_account_type","biz_type","sub_biz_type","inst_id","ext_info.assetOpertype",
            "ext_info.yebSaleByShumi","ext_info.yebBizType","ext_info.currentMasterInstId","ext_info.BIZ_PROD","ext_info.BIZ_MODE","ext_info.BIZ_ACTION_TYPE",
            "ext_info.ORG_BIZ_PRODUCT","ext_info.ORG_BIZ_MODE","ext_info.ORG_BIZ_ACTION_TYPE", "ext_info.fcHighAvailable"};
    static final String[] YEBASSETDECREASEORDER_BUSINESS_TARGET_KEYS = new String[]{"business_type","asset_account_type","biz_type","sub_biz_type","inst_id","ext_info.assetOpertype",
            "ext_info.yebSaleByShumi","ext_info.yebBizType","ext_info.currentMasterInstId","ext_info.BIZ_PROD","ext_info.BIZ_MODE","ext_info.BIZ_ACTION_TYPE",
            "ext_info.ORG_BIZ_PRODUCT","ext_info.ORG_BIZ_MODE","ext_info.ORG_BIZ_ACTION_TYPE","ext_info.mfAftransTaskInfo", "ext_info.planExeMode", "ext_info.fcHighAvailable"};
    static final String[] YEBASSETFREEZEORDER_BUSINESS_TARGET_KEYS = new String[]{"business_type","asset_account_type","freeze_type","biz_type","sub_biz_type","inst_id","ext_info.assetOpertype",
            "ext_info.yebSaleByShumi","ext_info.yebBizType","ext_info.currentMasterInstId","ext_info.BIZ_PROD","ext_info.BIZ_MODE","ext_info.BIZ_ACTION_TYPE",
            "ext_info.fcHighAvailable"};
    static final String[] YEBASSETUNFREEZEORDER_BUSINESS_TARGET_KEYS = new String[]{"business_type","asset_account_type","freeze_type","biz_type","sub_biz_type","inst_id","ext_info.assetOpertype",
            "ext_info.yebSaleByShumi","ext_info.yebBizType","ext_info.currentMasterInstId","ext_info.BIZ_PROD","ext_info.BIZ_MODE","ext_info.BIZ_ACTION_TYPE",
            "ext_info.orderType","ext_info.fcAssetType", "ext_info.fcHighAvailable"};
    //更新于0531，去除"ext_info.sceneType"、"ext_info.orderType"、"ext_info.fcYebBankInstId","ext_info.fcOpSaleInst","ext_info.balanceSubAssetTypeCode", "ext_info.channelType", "ext_info.fcAssetType",
    //"ext_info.financingExtraParams", "ext_info.mfAftransTaskInfo", "ext_info.payeeAssetType", "ext_info.payeeAssetTypeCode","ext_info.payerAssetType", "ext_info.payerAssetTypeCode", "ext_info.planExeChannel",
    // "ext_info.planExeFinAgent", "ext_info.planExeFinMode","ext_info.supportCkk","ext_info.fcAssetTypeCode", "ext_info.mfProdkey", "ext_info.accCardChargeLimit",
    static final String[] GLOBAL_IGNORES = new String[]{
        "asset_account_no", "asset_order_id", "bill_detail_id", "biz_context", "biz_dt",
                "biz_ev_code", "biz_no", "biz_pd_code", "biz_request_id", "clear_dt",
                "cnl_ev_code", "cnl_no", "cnl_pd_code", "ext_info.ORG_ID", "ext_info.ORIG_TRANS_OUT_NO",
                "ext_info.analysisInfo", "ext_info.assignedAssetClearingSettleSerialNo",
                "ext_info.fcAssetTypeAum",
                 "ext_info.fcOpUserId", "ext_info.fcTraceId","ext_info.fcOpCode",
                 "ext_info.fcYebBankInstructId", "ext_info.fcYebPurchaseBizNo",
                 "ext_info.fundCouponBizNo",
                 "ext_info.mybankCheckNo",
                "ext_info.orgPmtDt",
               "ext_info.targetFundCode", "ext_info.yebClearDt",
                "fund_code", "fund_inst", "gmt_commit", "gmt_create", "gmt_modified",
                "memo", "original_order_id", "out_biz_no", "payment_id", "pmt_dt",
                "quotient", "real_amount", "trans_dt", "user_id"
    };

    @Autowired
    private FullTraceValidateDataFetchRecordRepository fullTraceValidateDataFetchRecordRepository;

    @Autowired
    private FullTraceSameOriginValidateBaselineRepository fullTraceSameOriginValidateBaselineRepository;

    @Autowired
    private FullTraceSameOriginNoCoreConfigRepository fullTraceSameOriginNoCoreConfigRepository;


    @Override
    public FundFlowHomoCheckResult homologousSourceCheck(FundFlowCheckRequest fundFlowCheckRequest) {
        FundFlowHomoCheckResult result = new AbstractExecuteTemplate<FundFlowHomoCheckResult, FundFlowHomoCheckResult>() {
        }.doExecute("HomologousSourceCheckService-homologousSourceCheck", new AbstractExecuteTemplate.ExecuteInvoke<FundFlowHomoCheckResult, FundFlowHomoCheckResult>() {
            @Override
            public void checkParam() {
                if(StringUtil.isBlank(fundFlowCheckRequest.getShareCode())){
                    throw new BusinessException(BusinessErrorEnum.ILLEGAL_PARAMETER_ERROR,"shareCode不能为空");
                }
            }

            @Override
            public void assembleResult(FundFlowHomoCheckResult result, FundFlowHomoCheckResult model) {
                if(model!=null){
                    result.setSuccess(true);
                    result.setCheckResult(model.getCheckResult());
                }else{
                    result.setSuccess(false);
                }
            }

            @Override
            public FundFlowHomoCheckResult getResultInstance() {
                return new FundFlowHomoCheckResult();
            }

            @Override
            public FundFlowHomoCheckResult execute() {
                FundFlowHomoCheckResult result = new FundFlowHomoCheckResult();
                List<HomologousSourceModel> homologousSourceModels = check(fundFlowCheckRequest.getShareCode());
                result.setCheckResult(homologousSourceModels);
                return result;
            }
        });
        return result;
    }


    /**
     * 同源校验核心处理
     * @param shareCode
     * @return
     */
    public List<HomologousSourceModel> check(String shareCode){
        List<HomologousSourceModel> result = new LinkedList<>();
        //查询full_trace_validate_data_fetch_record
        FullTraceValidateDataFetchRecordDTO dto = fullTraceValidateDataFetchRecordRepository.queryByShareCode(shareCode);

        //2.同源校验
        List<TableData> tableDataList = JSON.parseArray(dto.getContent(), TableData.class);
        for (TableData tableData: tableDataList){
            //1.过滤出fc的单据，组成json串
            String tableName = tableData.getTableName().substring(0,tableData.getTableName().length()-3);
            if (Arrays.asList(supportTableName).contains(tableName)){
                JSONArray tableDataJsonArray = shipmentTableData(tableData.getColumnName(),tableData.getColumnData());
                //2.提取business_code
                for (int i =0;i<tableDataJsonArray.size();i++){
                    Object data = tableDataJsonArray.get(i);
                    //组装homologousSourceModel
                    HomologousSourceModel homologousSourceModel = new HomologousSourceModel();
                    homologousSourceModel.setDbName(tableData.getDbName());
                    homologousSourceModel.setTableName(tableData.getTableName());
//                    homologousSourceModel.setColumnData(JsonSorterUtil.sortJsonObject((JSONObject) data));
                    homologousSourceModel.setColumnData((JSONObject)data);
                    homologousSourceModel.setColumnIndex(String.valueOf(i));//设置被取数记录在原表中的第几条

//                    logger.info("data：{}",JSON.toJSONString(data));
                    String businessCode= extractBusinessCode(tableName,(JSONObject) data);
                    logger.info("businessCode：{}",businessCode);
                    //查询full_trace_same_origin_validate_online
                    List<FullTraceSameOriginValidateBaselineDTO> fullTraceSameOriginValidateBaselineDTOS = fullTraceSameOriginValidateBaselineRepository.queryList("YEB",tableName,businessCode);
                    JSONObject checkResult;
                    if (fullTraceSameOriginValidateBaselineDTOS != null && (!fullTraceSameOriginValidateBaselineDTOS.isEmpty())){
                        FullTraceSameOriginValidateBaselineDTO fullTraceSameOriginValidateBaselineDTO = fullTraceSameOriginValidateBaselineDTOS.get(0);
                        logger.info("fullTraceSameOriginValidateBaseline.id：{}",fullTraceSameOriginValidateBaselineDTO.getId());
                        List<JSONObject> standards=JSON.parseArray(fullTraceSameOriginValidateBaselineDTO.getStandardCheckSource()).toJavaList(JSONObject.class);
                        List<String> noNeedCheck = JSON.parseArray(fullTraceSameOriginValidateBaselineDTO.getIgnoreColumns(), String.class);
//                        //特殊处理：noNeedCheck
//                        List<String> sepecialNoNeedCheck = new ArrayList<>();
//                        sepecialNoNeedCheck.add("memo");
//                        noNeedCheck.removeAll(sepecialNoNeedCheck);

                        //3.查询full_trace_same_origin_not_core_config获取非核心校验字段。
                        FullTraceSameOriginNotCoreDTO fullTraceSameOriginNotCoreDTO = fullTraceSameOriginNoCoreConfigRepository.selectByBusinessRegionAndDbNameAndTableName("YEB",tableData.getDbName(),tableName);
                        String[] noCoreKeys = fullTraceSameOriginNotCoreDTO.getNotCoreColumns()==null?null:fullTraceSameOriginNotCoreDTO.getNotCoreColumns().split(",");
                        //4.根据homologousSource执行校验
                        checkResult  = validate(false,(JSONObject) data,standards,noNeedCheck,noCoreKeys);
                        homologousSourceModel.setBaselineId(fullTraceSameOriginValidateBaselineDTO.getId());//设置被查询出的full_trace_same_origin_validate_baseline的id
                    }else{
                        checkResult  = validate(true,(JSONObject) data,null,null,null);
                    }
//                    homologousSourceModel.setCheckResult(JsonSorterUtil.sortJsonObject(checkResult));
                    homologousSourceModel.setCheckResult(checkResult);
                    logger.info("checkResult：{}",JSON.toJSONString(checkResult));
                    result.add(homologousSourceModel);
                }
            }
        }
        return result;
    }

    /**
     * 表记录data与匹配上business_code的homologousSource进行比对：
     * -若出现data相比homologousSource：抛出no_need_check范围内字段，能匹配standard_check_source_list中的一条记录则匹配成功
     * -若出现data相比homologousSource：多出的key、缺少的key、以及key-value不匹配的情况，则匹配失败
     * @param data
     * @return
     */
    public JSONObject validate(boolean noSource,JSONObject data, List<JSONObject> standards,List<String> noNeedCheck,String[] noCoreKeys) {
        //没有匹配数据源，则直接返回结果
        if (noSource){
            return buildResponse(true,false,null);
        }

        // 记录原始数据以便调试
        logger.info("原始数据: {}", data.toJSONString());
        logger.info("原始标准数据: {}", standards.get(0).toJSONString());

        // 替换原有展平逻辑
        Set<String> noNeedCheckSet = new HashSet<>(noNeedCheck);

        // 解析嵌套结构
        JSONObject parsedData = parseExtInfo(data);
        List<JSONObject> parsedStandards = standards.stream()
                .map(this::parseExtInfo)
                .collect(Collectors.toList());

        // 执行校验
        List<HomologousSourceValidateResultDTO> reports = new LinkedList<>();
        for (int i = 0; i < parsedStandards.size(); i++) {
            HomologousSourceValidateResultDTO report = new HomologousSourceValidateResultDTO();
            JSONObject standard = parsedStandards.get(i);
            JSONObject originalStandard = standards.get(i);

            HomologousSourceCheckResultDTO check = compare(parsedData, standard, noNeedCheckSet,noCoreKeys);
            //校验成功，直接返回
            if (check.isPerfectMatch()) {
                report.setSuccess(true);
                report.setMatchedStandard(originalStandard);
                report.setCheckResult(check);
                reports.add(report);
                return buildResponse(false,true,reports);
            }else{
                report.setSuccess(false);
                report.setMatchedStandard(originalStandard);
                report.setCheckResult(check);
                reports.add(report);
            }
        }
        return buildResponse(false,false,reports);
    }

    private JSONObject parseExtInfo(JSONObject obj) {
        JSONObject cloned = JSONObject.parseObject(JSON.toJSONString(obj));
        if (cloned.containsKey("ext_info")) {
            cloned.put("ext_info", parseNestedObject(cloned.get("ext_info"))); // 递归解析
        }
        return cloned;
    }

    /**
     * 构造返回结果
     * @param noSource
     * @param haSuccess
     * @param reports
     * @return
     */
    private JSONObject buildResponse(boolean noSource,boolean haSuccess, List<HomologousSourceValidateResultDTO> reports) {
        JSONObject json = new JSONObject(true);
        //无匹配数据源，则直接返回"校验失败-无数据源"
        if (noSource){
            json.put("checkResultType", "NO_DATA");//校验失败-无数据源
            return json;
        }
        if (haSuccess){
            json.put("checkResultType", "PASS");//校验成功
        }else{
            json.put("checkResultType", "FAIL");//校验失败
        }
        JSONArray jsonArray = new JSONArray();
        for (HomologousSourceValidateResultDTO report: reports){
            JSONObject jsonDetail = new JSONObject(true);
            if (report.getCheckResult().isPerfectMatch()){
                jsonDetail.put("checkResultType", "0");//校验成功
            }else{
                jsonDetail.put("checkResultType", "1");//校验成功
            }
            jsonDetail.put("checkStandardSource", report.getMatchedStandard());//此处为空，见校验差异
            jsonDetail.put("checkDifference", report.getCheckResult().getDifferences());

            jsonArray.add(jsonDetail);
        }
        //排序1：checkResultType=SUCCESS排第一
        jsonArray.sort((o1, o2) -> {
            JSONObject obj1 = (JSONObject) o1;
            JSONObject obj2 = (JSONObject) o2;
            return obj1.getString("checkResultType").compareTo(obj2.getString("checkResultType"));
        });
        //排序2：根据missings个数、extras个数、mismatches个数、no_core_mismatches个数排序
        jsonArray.sort((o1, o2) -> {
            JSONObject obj1 = (JSONObject) o1;
            JSONObject obj2 = (JSONObject) o2;
            int similarity1 = sortCheckStandardSource(obj1.getJSONObject("checkDifference"));
            int similarity2 = sortCheckStandardSource(obj2.getJSONObject("checkDifference"));
            return Integer.compare(similarity1,similarity2);

        });
        json.put("checkDetail", jsonArray);
        return json;
    }

    /**
     * 排序计算相似度：越不相似，值越大
     * 优化：计算相似度时extras、missings、mismatches在ignores中记1，否则记10
     * @param report
     * @return
     */
    private int sortCheckStandardSource(JSONObject report) {
        Map<String, Object> extras = (Map<String, Object>) report.get("extras");
        Map<String, Object> missings = (Map<String, Object>) report.get("missings");
        Map<String, Object[]> mismatches = (Map<String, Object[]>) report.get("mismatches");
//        Map<String, Object[]> no_core_mismatches = (Map<String, Object[]>) report.get("no_core_mismatches");
        List<String> ignores = (List<String>) report.get("ignores");

        int noSimilarity = 0;
        if (extras != null && !extras.isEmpty()){
            for (String key:extras.keySet()){
                if (!CollectionUtils.isEmpty(ignores) && ignores.contains(key)){
                    noSimilarity+=1;
                }else{
                    noSimilarity+=10;
                }
            }
        }
        if (missings != null && !missings.isEmpty()){
            for (String key:missings.keySet()){
                if (!CollectionUtils.isEmpty(ignores) && ignores.contains(key)){
                    noSimilarity+=1;
                }else{
                    noSimilarity+=10;
                }
            }
        }
        if (mismatches != null && !mismatches.isEmpty()){
            for (String key:mismatches.keySet()){
                if (!CollectionUtils.isEmpty(ignores) && ignores.contains(key)){
                    noSimilarity+=1;
                }else{
                    noSimilarity+=10;
                }
            }
        }
//        if (no_core_mismatches != null && !no_core_mismatches.isEmpty()){
//            noSimilarity+=no_core_mismatches.keySet().size();
//        }
        return noSimilarity;
    }


    private void compareNestedFields(
            JSONObject data,
            JSONObject standard,
            Set<String> noNeedCheck,
            HomologousSourceCheckResultDTO result,
            String parentPath
    ) {
        Set<String> allKeys = new HashSet<>();
        if (data != null) allKeys.addAll(data.keySet());
        if (standard != null) allKeys.addAll(standard.keySet());

        for (String key : allKeys) {
            String currentPath = parentPath.isEmpty() ? key : parentPath + "." + key;

            boolean dataHas = data != null && data.containsKey(key);
            boolean stdHas = standard != null && standard.containsKey(key);
            boolean isIgnored = noNeedCheck.stream().anyMatch(currentPath::startsWith);

            // CASE 1: Data有而Standard无（包含嵌套字段）
            if (dataHas && !stdHas) {
                Object dataValue = data.get(key);
                result.addExtra(currentPath, dataValue);
                // 递归检测嵌套结构差异
                detectNestedExtras(dataValue, currentPath, noNeedCheck, result);
                continue;
            }
            // CASE 2: Data无而Standard有（包含嵌套字段）
            if (!dataHas && stdHas) {
                Object stdValue = standard.get(key);
                // 若Standard的字段值为"null"，则不加入missings
                if (!"null".equals(String.valueOf(stdValue))) {
                    result.addMissing(currentPath, stdValue);
                    // 递归检测嵌套缺失
                    detectNestedMissings(stdValue, currentPath, noNeedCheck, result);
                }
                continue;
            }

            // CASE 3: 双方都存在
            Object dataValue = data.get(key);
            Object stdValue = standard.get(key);

            // 处理嵌套结构递归比较
            JSONObject dataObj = parseNestedObject(dataValue);
            JSONObject stdObj = parseNestedObject(stdValue);
            if (dataObj != null || stdObj != null) {
                compareNestedFields(dataObj, stdObj, noNeedCheck, result, currentPath);
            }
            // 处理值差异（仅当非忽略字段时）
            else if (!isIgnored && !Objects.equals(dataValue, stdValue)) {
                result.addMismatch(currentPath, dataValue, stdValue);
            }
        }
    }

    // 新增ignores嵌套字段检测逻辑
    private void detectNestedIgnores(
            Object dataValue,
            String basePath,
            Set<String> noNeedCheck,
            HomologousSourceCheckResultDTO result
    ) {
        JSONObject dataObj = parseNestedObject(dataValue);
        if (dataObj == null) return;

        for (String key : dataObj.keySet()) {
            String nestedPath = basePath.isEmpty() ? key : basePath + "." + key;
            Object value = dataObj.get(key);
            if (noNeedCheck.contains(nestedPath)){
                result.addIgnore(nestedPath);
            }
            detectNestedIgnores(value, nestedPath, noNeedCheck, result);
        }
    }

    // 新增多出嵌套字段检测逻辑
    private void detectNestedExtras(
            Object dataValue,
            String basePath,
            Set<String> noNeedCheck,
            HomologousSourceCheckResultDTO result
    ) {
        JSONObject dataObj = parseNestedObject(dataValue);
        if (dataObj == null) return;

        for (String key : dataObj.keySet()) {
            String nestedPath = basePath + "." + key;
            Object value = dataObj.get(key);
            result.addExtra(nestedPath, value);
            detectNestedExtras(value, nestedPath, noNeedCheck, result);
        }
    }

    // 新增缺失嵌套字段检测逻辑
    private void detectNestedMissings(Object stdValue, String basePath, Set<String> noNeedCheck, HomologousSourceCheckResultDTO result) {
        JSONObject stdObj = parseNestedObject(stdValue);
        if (stdObj == null) return;

        // 获取当前已记录的缺失路径
        Set<String> missingPaths = result.getMissings().keySet();

        for (String key : stdObj.keySet()) {
            String nestedPath = basePath.isEmpty() ? key : basePath + "." + key;
            Object value = stdObj.get(key);

            // 规则1：如果父路径已缺失，跳过子字段检查
            if (isParentPathMissing(nestedPath, missingPaths)) {
                continue;
            }

            // 规则2：如果当前路径被标记为"null"，跳过
            if ("null".equals(String.valueOf(value))) {
                continue;
            }

            // 规则3：将当前路径加入missings（无需检查noNeedCheck，存在性差异始终记录）
            result.addMissing(nestedPath, value);

            // 规则4：递归处理子字段时携带已更新的missingPaths
            detectNestedMissings(value, nestedPath, noNeedCheck, result);
        }
    }

    /**
     * 检查当前路径的任意父路径是否已存在于缺失列表中
     */
    private boolean isParentPathMissing(String currentPath, Set<String> missingPaths) {
        String[] parts = currentPath.split("\\.");
        StringBuilder parentPathBuilder = new StringBuilder();

        for (int i = 0; i < parts.length - 1; i++) { // 不检查自身，只检查父级
            if (i > 0) parentPathBuilder.append(".");
            parentPathBuilder.append(parts[i]);
            if (missingPaths.contains(parentPathBuilder.toString())) {
                return true;
            }
        }
        return false;
    }

    // 修改JSON解析方法增强容错
    private JSONObject parseNestedObject(Object value) {
        if (value instanceof JSONObject) {
            return (JSONObject) value;
        }
        try {
            if (value instanceof String) {
                return JSON.parseObject((String) value);
            }
            return (JSONObject) value;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 对比匹配：多出的key、缺少的key、以及key-value不匹配的情况，则匹配失败
     * @param data
     * @param standard
     * @return
     */
    private HomologousSourceCheckResultDTO compare(JSONObject data, JSONObject standard, Set<String> noNeedCheck,String[] noCoreKeys) {
        HomologousSourceCheckResultDTO result = new HomologousSourceCheckResultDTO();
        compareNestedFields(data, standard, noNeedCheck, result, "");

        //设置ignores
        Set<String> globalNoNeedCheckSet = new HashSet<>(Arrays.asList(GLOBAL_IGNORES));
        detectNestedIgnores(data, "", globalNoNeedCheckSet, result);
        detectNestedIgnores(standard, "", globalNoNeedCheckSet, result);

        //设置no_core_mismatches桶
        if ((!result.getMismatches().isEmpty()) && noCoreKeys!=null && noCoreKeys.length>0) {
            List<String> noCoreKeyList = Arrays.asList(noCoreKeys);
            for (String key : result.getMismatches().keySet()) {
                if (noCoreKeyList.contains(key)) {
                    result.addNoCoreMismatch(key, result.getMismatches().get(key)[0], result.getMismatches().get(key)[1]);
                }
            }
        }
        return result;
    }

    /**
     * 装载columnName与columnData获取一组json表数据
     * @param columnName
     * @param columnData
     * @return
     */
    public JSONArray shipmentTableData(List<String> columnName,List<List<String>> columnData) {
        JSONArray result = new JSONArray();

        for (List<String> row : columnData) {
            Map<String, String> jsonObj = new LinkedHashMap<>();  // 保持字段顺序
            for (int i = 0; i < columnName.size(); i++) {
                // 安全获取值，防止index越界
                String value = (i < row.size()) ? row.get(i) : null;
                jsonObj.put(columnName.get(i), value);
            }
            //map转jasonObject
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(jsonObj));
            result.add(jsonObject);
        }

        return result;
    }

    /**
     * 从原始表数据中提取businessCode
     * @param origData
     * @return
     */
    public static String extractBusinessCode(String tableName,JSONObject origData){
        List<String> keysToExtract = null;
        switch (tableName){
            case "yeb_asset_increase_order":
                keysToExtract = Arrays.asList(YEBASSETINCREASEORDER_BUSINESS_TARGET_KEYS);
                break;
            case "yeb_asset_decrease_order":
                keysToExtract = Arrays.asList(YEBASSETDECREASEORDER_BUSINESS_TARGET_KEYS);
                break;
            case "yeb_asset_freeze_order":
                keysToExtract = Arrays.asList(YEBASSETFREEZEORDER_BUSINESS_TARGET_KEYS);
                break;
            case "yeb_asset_unfreeze_order":
                keysToExtract = Arrays.asList(YEBASSETUNFREEZEORDER_BUSINESS_TARGET_KEYS);
                break;
            default:
                keysToExtract = Arrays.asList(YEBASSETINCREASEORDER_BUSINESS_TARGET_KEYS);
                break;
        }
        StringBuilder resultBuilder = new StringBuilder();
        for (String key : keysToExtract) {
            String value = null;
            // 处理 normal keys
            if (origData.containsKey(key)) {
                value = origData.getString(key);
            }
            // 处理 ext_info 开头的 keys
            if (key.startsWith("ext_info.")) {
                String extKey = key.substring("ext_info.".length());
                if (origData.containsKey("ext_info")) {
                    String extInfo = origData.getString("ext_info");
                    JSONObject extInfoObj = JSON.parseObject(extInfo);
                    if (extInfoObj.containsKey(extKey)) {
                        value = extInfoObj.getString(extKey);
                    }
                }
                //单独处理以“ext_info”开头的key
                StringBuffer extInfoBuffer = new StringBuffer();
                extInfoBuffer.append("extInfo_").append(extKey);
                key=extInfoBuffer.toString();
            }
            // 如果找到了对应的 value，就拼接到 resultBuilder
            if (resultBuilder.length() > 0) {
                resultBuilder.append(",");
            }
            resultBuilder.append(key).append("=").append(value);
        }
        // 输出最终结果
        return resultBuilder.toString();
    }

}