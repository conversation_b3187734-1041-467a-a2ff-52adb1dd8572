package com.alipay.findataquality.service.dto;

public class HomologousSourceData {
    private String id;
    private String business_code;
    private String ignore_columns;
    private String standard_check_source;

    public HomologousSourceData(String id,  String business_code, String ignore_columns,String standard_check_source){
        this.id = id;
        this.business_code = business_code;
        this.ignore_columns = ignore_columns;
        this.standard_check_source = standard_check_source;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getBusiness_code() {
        return business_code;
    }

    public void setBusiness_code(String business_code) {
        this.business_code = business_code;
    }

    public String getIgnore_columns() {
        return ignore_columns;
    }

    public void setIgnore_columns(String ignore_columns) {
        this.ignore_columns = ignore_columns;
    }

    public String getStandard_check_source() {
        return standard_check_source;
    }

    public void setStandard_check_source(String standard_check_source) {
        this.standard_check_source = standard_check_source;
    }

    @Override
    public String toString() {
        return "{"
                + "\"id\":\""
                + id + '\"'
                + ",\"business_code\":\""
                + business_code + '\"'
                + ",\"ignore_columns\":\""
                + ignore_columns + '\"'
                + ",\"standard_check_source\":\""
                + standard_check_source + '\"'
                + "}";
    }
}

