package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;

/**
 * 应用场景：FundFlowStageEnum.YEB_CKK_TO_YEB（余额宝存款卡申购余额宝）
 *
 */
public class YebCkkCheck extends AbstractCheckScene {

    public YebCkkCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "存款卡资金数据检查";
    }

    @AutoCheck(ruleDesc="[存款卡]financingcore子卡核心要素一致性检查")
    public RuleCheckResult 存款卡增核心要素一致性检查(FinancingcoreData fc) {
        AssetRecordData left = fc.findTable(FinancingcoreData.INCREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'  && asset_account_type == 'VIRTUAL_CKK_FUND_ACCT'")
                .findVars("user_id", "inst_id", "clear_dt");
        AssetRecordData right = fc.findTable(FinancingcoreData.INCREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'  && asset_account_type == 'CKK_FUND_ACCT'")
                .findVars("user_id", "inst_id", "clear_dt");
        return autoCheckCombineEqual(left, right);
    }

    @AutoCheck(ruleDesc="[存款卡]financingcore子卡核心要素一致性检查")
    public RuleCheckResult 存款卡增减核心要素一致性检查(FinancingcoreData fc) {
        AssetRecordData left = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'  && asset_account_type == 'VIRTUAL_CKK_FUND_ACCT'").findVars("user_id", "inst_id", "clear_dt");
        AssetRecordData right = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'  && asset_account_type == 'CKK_FUND_ACCT'").findVars("user_id", "inst_id", "clear_dt");
        return autoCheckCombineEqual(left, right);
    }

    @AutoCheck(ruleDesc = "[存款卡]financingcore子卡：1003增.real_amount==1004增.real_amount之和")
    public RuleCheckResult 存款卡主卡增等于子卡增之和(FinancingcoreData fc) {
        AssetRecordData left = fc.findTable(FinancingcoreData.INCREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'  && asset_account_type == 'VIRTUAL_CKK_FUND_ACCT'").findVars("real_amount");
        AssetRecordData right = fc.findTable(FinancingcoreData.INCREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_PURCHASE'  && asset_account_type == 'CKK_FUND_ACCT'").findVars("real_amount");
        return autoCheckSumEqual(left,right,1);
    }

    @AutoCheck(ruleDesc = "[存款卡]financingcore子卡：1003减.real_amount==1004减.real_amount之和")
    public RuleCheckResult 存款卡主卡减等于子卡减之和(FinancingcoreData fc) {
        AssetRecordData left = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'  && asset_account_type == 'VIRTUAL_CKK_FUND_ACCT'")
                .findVars("real_amount");
        AssetRecordData right = fc.findTable(FinancingcoreData.DECREASE)
                .findData("inst_id == 'SHUMIJJ' && business_type == 'OUTER_REDEEM'  && asset_account_type == 'CKK_FUND_ACCT'")
                .findVars("real_amount");
        return autoCheckSumEqual(left,right,1);
    }


}
