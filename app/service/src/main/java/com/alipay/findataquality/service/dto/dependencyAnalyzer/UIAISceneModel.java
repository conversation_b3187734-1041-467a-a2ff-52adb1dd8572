package com.alipay.findataquality.service.dto.dependencyAnalyzer;

public class UIAISceneModel {

    String bakeryId;
    String caseName;
    String serviceName;

    String sceneName;

    String request;

    String response;

    String bakeryUrl;

    String staticUrl;
    String sprintId;
    String projectId;
    String previewSprintId;
    String startPage;
    String pageActionUrl;
    String pageShowUrl;

    public String getBakeryId() {
        return bakeryId;
    }

    public String getCaseName() {
        return caseName;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }

    public void setBakeryId(String bakeryId) {
        this.bakeryId = bakeryId;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getRequest() {
        return request;
    }

    public void setRequest(String request) {
        this.request = request;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public String getBakeryUrl() {
        return bakeryUrl;
    }

    public void setBakeryUrl(String bakeryUrl) {
        this.bakeryUrl = bakeryUrl;
    }

    public String getStaticUrl() {
        return staticUrl;
    }

    public void setStaticUrl(String staticUrl) {
        this.staticUrl = staticUrl;
    }

    public String getSprintId() {
        return sprintId;
    }

    public void setSprintId(String sprintId) {
        this.sprintId = sprintId;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getPreviewSprintId() {
        return previewSprintId;
    }

    public void setPreviewSprintId(String previewSprintId) {
        this.previewSprintId = previewSprintId;
    }

    public String getStartPage() {
        return startPage;
    }

    public void setStartPage(String startPage) {
        this.startPage = startPage;
    }

    public String getPageActionUrl() {
        return pageActionUrl;
    }

    public void setPageActionUrl(String pageActionUrl) {
        this.pageActionUrl = pageActionUrl;
    }

    public String getPageShowUrl() {
        return pageShowUrl;
    }

    public void setPageShowUrl(String pageShowUrl) {
        this.pageShowUrl = pageShowUrl;
    }

}
