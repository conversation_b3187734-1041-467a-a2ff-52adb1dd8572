/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.findataquality.service.dto;

import com.alibaba.fastjson.JSONObject;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version : FundFlowHomoCheckResult.java, v 0.1 2025年03月17日 10:20 zhaolinling Exp $
 */
public class HomologousSourceCheckResultDTO {
    private final Map<String, Object> extras = new LinkedHashMap<>();
    private final Map<String, Object> missings = new LinkedHashMap<>();
    private final Map<String, Object[]> mismatches = new LinkedHashMap<>();
    private final List<String> ignores = new LinkedList<>();
    private final Map<String, Object[]> no_core_mismatches = new LinkedHashMap<>();

    /**
     * 是否检测判定为异常，
     * 1. 缺失字段
     * 2. 额外字段
     * 3. 字段值不一致
     * @return
     */
    public boolean isPerfectMatch() {
        return extras.isEmpty() && missings.isEmpty() && mismatches.isEmpty();
    }

    public void addExtra(String key, Object value) {
        extras.put(key, value);
    }

    public void addMissing(String key, Object value) {
        missings.put(key, value);
    }

    public void addMismatch(String key, Object actual, Object expected) {
        mismatches.put(key, new Object[]{actual, expected});
    }

    public void addIgnore(String key) {
        ignores.add(key);
    }

    public void addNoCoreMismatch(String key, Object actual, Object expected) {
        no_core_mismatches.put(key, new Object[]{actual, expected});
    }

    public JSONObject getDifferences() {
        JSONObject diff = new JSONObject(true);
        if (!extras.isEmpty()) {
            diff.put("extras", extras);
        }
        if (!missings.isEmpty()) {
            diff.put("missings", missings);
        }
        if (!ignores.isEmpty()) {
            diff.put("ignores", ignores);
        }
        if (!mismatches.isEmpty()) {
            JSONObject mismatch = new JSONObject(true);
            mismatches.forEach((k, v) ->
                    {
                        Map<String, Object> value = new java.util.HashMap<>();
                        value.put("actural_value", v[0]);
                        value.put("expected_value", v[1]);
                        mismatch.put(k, value);
                    }
            );
            diff.put("mismatches", mismatch);
        }
        if (!no_core_mismatches.isEmpty()) {
            JSONObject noCoreMismatches = new JSONObject(true);
            no_core_mismatches.forEach((k, v) ->
                    {
                        Map<String, Object> value = new java.util.HashMap<>();
                        value.put("actural_value", v[0]);
                        value.put("expected_value", v[1]);
                        noCoreMismatches.put(k, value);
                    }
            );
            diff.put("no_core_mismatches", noCoreMismatches);
        }
        return diff;
    }

    public Map<String, Object> getExtras() {
        return extras;
    }

    public Map<String, Object> getMissings() {
        return missings;
    }

    public Map<String, Object[]> getMismatches() {
        return mismatches;
    }


}
