package com.alipay.findataquality.service.repository;

import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceValidateDataFetchRecordDOExtendMapper;
import com.alipay.findataquality.dal.mybatis.model.single.FullTraceValidateDataFetchRecordDO;
import com.alipay.findataquality.service.dto.FullTraceValidateDataFetchRecordDTO;
import com.alipay.findataquality.service.dto.MarkContentData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableData;
import com.alipay.findataquality.service.util.JsonExtractUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public class FullTraceValidateDataFetchRecordRepositoryImpl implements FullTraceValidateDataFetchRecordRepository {

    private static final Logger logger = LoggerFactory.getLogger(FullTraceValidateDataFetchRecordRepository.class);

    @Autowired(required = false)
    private FullTraceValidateDataFetchRecordDOExtendMapper fullTraceValidateDataFetchRecordDOExtendMapper;

    @Override
    public FullTraceValidateDataFetchRecordDTO queryByShareCode(String shareCode) {
        List<FullTraceValidateDataFetchRecordDO> fullTraceValidateDataFetchRecordDOS= fullTraceValidateDataFetchRecordDOExtendMapper.selectByShareCode(shareCode);
        if(fullTraceValidateDataFetchRecordDOS == null || fullTraceValidateDataFetchRecordDOS.isEmpty()) {
            logger.warn("FullTraceValidateDataFetchRecordRepositoryImpl.queryByShareCode() 查询结果为空，查询shareCode为{0}", shareCode);
            return null;
        }
        return convertDO2DTO(fullTraceValidateDataFetchRecordDOS.get(0));
    }

    @Override
    public Long insert(FullTraceValidateDataFetchRecordDTO dto) {
        FullTraceValidateDataFetchRecordDO doObj = convertDTO2DO(dto);
        Long id = fullTraceValidateDataFetchRecordDOExtendMapper.insert(doObj);
        dto.setId(id);
        return id;
    }

    private FullTraceValidateDataFetchRecordDO convertDTO2DO(FullTraceValidateDataFetchRecordDTO dto) {
        FullTraceValidateDataFetchRecordDO doObj = new FullTraceValidateDataFetchRecordDO();
        BeanUtils.copyProperties(dto, doObj);
        return doObj;
    }

    private FullTraceValidateDataFetchRecordDTO convertDO2DTO(FullTraceValidateDataFetchRecordDO doObj) {
        FullTraceValidateDataFetchRecordDTO dto = new FullTraceValidateDataFetchRecordDTO();
        BeanUtils.copyProperties(doObj, dto);
        String content = doObj.getContent();
        String keyData = doObj.getKeyData();
        String markContent = doObj.getMarkContent();
        if (JsonExtractUtil.isValidJsonArray(content)) {
            dto.setTableDataList(JSONObject.parseArray(content, TableData.class));
        }
        if (JsonExtractUtil.isValidJsonArray(keyData)) {
            dto.setKeyDataList(JSONObject.parseArray(keyData, TableData.class));
        }
        if (JsonExtractUtil.isValidJson(markContent)) {
            dto.setMarkContentData(JSONObject.parseObject(markContent, MarkContentData.class));
        }

        return dto;
    }

}
