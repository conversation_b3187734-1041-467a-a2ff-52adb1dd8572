package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.yeb;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.OutTypeEnum;
import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QueryDBEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.AbstractQueryTask;

/**
 * @ClassName YebcoreAccountTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 15:50
 * @Version V1.0
 **/
public class YebcoreAccountPreTask extends AbstractQueryTask {

    public YebcoreAccountPreTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public QueryDBEnum queryDb() {
        return QueryDBEnum.YEBCORE;
    }

    @Override
    public String[] querySql() {
        return new String[]{
                "select mf_account_no,af_account_no from yebs_sub_contract_${db_flag} where user_id = '${user_id}' and product_type='0100' and mf_account_no is not null and af_account_no is not null and status ='A'"};
    }

    @Override
    public String[] outcomeValue() {
        return new String[]{"mf_account_no,af_account_no"};
    }

    @Override
    public OutTypeEnum[] outcomeType(){
        return new OutTypeEnum[]{OutTypeEnum.SINGLE};
    }

    @Override
    public int[] filterResultIndex() {
        return new int[]{0};
    }
}
