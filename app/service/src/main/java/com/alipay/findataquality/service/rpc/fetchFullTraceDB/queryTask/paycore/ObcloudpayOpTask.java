package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.paycore;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model.TableData;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;

/**
 * @ClassName ObcloudpayOpTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/25 15:23
 * @Version V1.0
 **/
public class ObcloudpayOpTask extends ObcloudpayTask{
    public ObcloudpayOpTask(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public String opTask() {
        return "op";
    }

    @Override
    public void handleContextAfter(QueryContext context) {
    }

    @Override
    public void handleContextEachSql(int sqlIndex, TableData tableData, QueryContext context) {
    }

    @Override
    public void handleContextBefore(QueryContext context) {
    }
}
