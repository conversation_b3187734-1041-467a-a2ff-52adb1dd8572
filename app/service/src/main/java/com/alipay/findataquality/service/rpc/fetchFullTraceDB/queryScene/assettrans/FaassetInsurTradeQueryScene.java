package com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.assettrans;

import com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums.QuerySceneEnum;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.model.QueryContext;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryScene.AbstractQueryScene;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetFainsTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetFincomeTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.queryTask.assettrans.FaassetTradeTask;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.DbUtils;
import com.alipay.findataquality.service.rpc.fetchFullTraceDB.utils.StrUtils;

/**
 * @ClassName FaassetTransQueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/6/21 11:14
 * @Version V1.0
 **/
public class FaassetInsurTradeQueryScene extends AbstractQueryScene {
    /**
     * BaseQueryScene
     *
     * @param queryContext
     */
    public FaassetInsurTradeQueryScene(QueryContext queryContext) {
        super(queryContext);
    }

    @Override
    public Class[] queryTask() {
        return new Class[]{
                FaassetTradeTask.class,
                FaassetFincomeTradeTask.class,
                FaassetFainsTradeTask.class
        };
    }

    @Override
    public int[] queryTaskRank() {
        return new int[]{0,0,0};
    }

    @Override
    public QuerySceneEnum queryScene() {
        return QuerySceneEnum.FAASSET_INSUR_TRADE_QUERY;
    }

    @Override
    public void handleContext(QueryContext context, String inputValue) {
        String uniqueId = context.getInputValue();
        String[] uniqueIds =  uniqueId.split("-");
        if(uniqueIds.length>1){
            //uniqueId的典型格式如下，取中间值的倒数12、11位作为分表位
            //22010005-20240129001080012004500000120743-52024012950400107042
            String dbFlag = StrUtils.substrFromEnd(uniqueIds[1],12,2);
            context.putGlobal("db_flag", dbFlag);
            context.putGlobal("db_schema","0"+StrUtils.substrFromEnd(uniqueIds[1],12,1));
            context.putGlobal("date",StrUtils.substrFromStart(uniqueIds[1],0,8));
            String dbr = DbUtils.getDbrFromDbFlag(context.getValue("db_flag"));
            context.putGlobal("db_r",dbr);
        }
    }
}
