package com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AbstractModelCheckScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TableDataContext;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionGroup;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.ModelCheckResult;
import java.util.List;

/**
 * @ClassName FundFlowModelMainGraphCheck
 * @Description 资金模型检查-主图检查
 * <AUTHOR>
 * @Date 2025/5/6 14:57
 * @Version V1.0
 **/
public class FundFlowModelMainGraphCheck extends AbstractModelCheckScene {
    public FundFlowModelMainGraphCheck(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    public String checkSceneName() {
        return "资金模型总图检查";
    }

    @AutoCheck(ruleDesc = "总图资金平衡检查：所有账户的流入=所有账户的流出总和")
    public ModelCheckResult 所有账户的流入金额等于所有账户的流出金额(List<TransactionGroup> mainGraphtransactionGroupList) {
        return autoCheckGraphBalance(mainGraphtransactionGroupList);
    }
}
