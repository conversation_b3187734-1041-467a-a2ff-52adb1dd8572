package com.alipay.findataquality.service.repository;


import com.alipay.findataquality.service.dto.FullTraceValidateDataFetchRecordDTO;

/**
 * 对全链路联调数据捞取记录进行查询、新增、更新等操作
 */
public interface FullTraceValidateDataFetchRecordRepository {

    /**
     * 通过shareCode查询用户资产
     * @param shareCode
     * @return
     */
    FullTraceValidateDataFetchRecordDTO queryByShareCode(String shareCode);

    /**
     * 新增一条数据取数记录
     * @param dto
     * @return
     */
    public Long insert(FullTraceValidateDataFetchRecordDTO dto);
}
