package com.alipay.findataquality.service.config;

import org.apache.log4j.xml.DOMConfigurator;

/**
 * @ClassName GlobalConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/22 11:18
 * @Version V1.0
 **/
public class GlobalConfig {
    /**
     * 日志配置文件
     */
    public static final String logConfig = "src/test/java/com/alipay/findataquality/service/config/log_reload4j_config.xml";
    /**
     * 数据库配置文件
     */
    public static final String dbConfig = "src/test/java/com/alipay/findataquality/service/config/db_config.json";
    /**
     * 全链路取数配置文件，当文件存在时进行真实连db取数，否则直接跳过
     */
    public static final String fetchDataConfig = "src/test/java/com/alipay/findataquality/service/config/local_test_on.txt";


    public static void initLog(){
        //加载配置文件
        DOMConfigurator.configure(GlobalConfig.logConfig);
    }
}
