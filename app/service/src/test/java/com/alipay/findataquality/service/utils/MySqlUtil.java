package com.alipay.findataquality.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.service.config.GlobalConfig;
import com.alipay.findataquality.service.model.FundFlowTestData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName MySqlHelpler
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/2 15:58
 * @Version V1.0
 **/
public class MySqlUtil {
    private static final Logger logger = LoggerFactory.getLogger(MySqlUtil.class);

    public static List<FundFlowTestData> fetchData(String sql){
        List<FundFlowTestData> list = new ArrayList<>();

        try {
            String config = getContext(GlobalConfig.dbConfig);
            if(config==null||config.isEmpty()){
                logger.info("db_config.json文件不存在");
                return null;
            }
            JSONObject object = JSON.parseObject(config);
            String url = object.get("url").toString();
            String username = object.get("username").toString();
            String password = object.get("password").toString();

            // 加载MySQL JDBC驱动
            Class.forName("com.mysql.jdbc.Driver");
            // 建立数据库连接
            Connection connection = DriverManager.getConnection(url, username, password);

            // 创建Statement对象
            Statement statement = connection.createStatement();

            // 执行查询操作
            ResultSet resultSet = statement.executeQuery(sql);

            // 遍历查询结果
            while (resultSet.next()) {
                // 获取查询结果中的列值
                String share_code = resultSet.getString("share_code");
                String entrance_code = resultSet.getString("entrance_code");
                String content = resultSet.getString("content");
                String sceneCode = resultSet.getString("scene_code");
                // 输出查询结果
                FundFlowTestData data = new FundFlowTestData(share_code,entrance_code,content,sceneCode);
                list.add(data);
            }
            // 关闭连接和Statement对象
            resultSet.close();
            statement.close();
            connection.close();
        } catch (Exception e) {
            //e.printStackTrace();
        }
        logger.info("总共获取数据条数:{}",list.size());
        return list;
    }

    public static String getContext(String filePath) {
        try {
            File file = new File(filePath);
            if(!file.exists()){
                return null;
            }

            FileReader fileReader = new FileReader(filePath);
            BufferedReader bufferedReader = new BufferedReader(fileReader);
            String line;
            StringBuilder sb = new StringBuilder();
            while ((line = bufferedReader.readLine()) != null) {
                //System.out.println(line);
                sb.append(line).append("\n");
            }
            bufferedReader.close();
            return sb.toString();
        } catch (IOException e) {
            //e.printStackTrace();
        }
        return null;
    }
}