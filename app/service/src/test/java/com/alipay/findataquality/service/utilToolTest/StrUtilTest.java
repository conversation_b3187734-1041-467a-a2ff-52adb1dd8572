//package com.alipay.findataquality.service.utilToolTest;
//
//import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;
//import org.junit.Assert;
//import org.junit.Test;
//import java.util.Map;
//
///**
// * @ClassName StrUtilTest
// * @Description TODO
// * <AUTHOR>
// * @Date 2025/4/24 11:34
// * @Version V1.0
// **/
//public class StrUtilTest {
//
//    /**
//     * [单测用例]测试场景：测试空字符串
//     */
//    @Test
//    public void testGetJsonKvMapWithEmptyString() {
//        String jsonData = "";
//        Map<String, Object> result = StrUtil.getJsonKvMap(jsonData);
//        Assert.assertTrue(result.isEmpty());
//    }
//
//    /**
//     * [单测用例]测试场景：测试非空字符串
//     */
//    @Test
//    public void testGetJsonKvMapWithNonEmptyString() {
//        String jsonData = "{\"key1\":\"value1\",\"key2\":\"value2\"}";
//        Map<String, Object> result = StrUtil.getJsonKvMap(jsonData);
//        Assert.assertEquals(2, result.size());
//        Assert.assertEquals("value1", result.get("key1"));
//        Assert.assertEquals("value2", result.get("key2"));
//    }
//
//    /**
//     * [单测用例]测试场景：测试非法字符串
//     */
//    @Test
//    public void testGetJsonKvMapWithInvalidString() {
//        String jsonData = "invalid json string";
//        Map<String, Object> result = StrUtil.getJsonKvMap(jsonData);
//        Assert.assertTrue(result.isEmpty());
//    }
//
//    /**
//     * [单测用例]测试场景：测试null字符串
//     */
//    @Test
//    public void testGetJsonKvMapWithNullString() {
//        String jsonData = null;
//        Map<String, Object> result = StrUtil.getJsonKvMap(jsonData);
//        Assert.assertTrue(result.isEmpty());
//    }
//}
