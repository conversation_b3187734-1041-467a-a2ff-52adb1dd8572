package com.alipay.findataquality.service.fundFlowCheckTest;

import org.junit.jupiter.api.Test;
import static com.alipay.findataquality.service.utils.FundFlowUtil.runAnalyze;

/**
 * @ClassName FullTraceCheckTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/21 16:42
 * @Version V1.0
 **/
public class FundFlowAnalyzeTest {

    @Test
    public void run(){
        runAnalyze("20241230042223768973312");
    }

    //======================================
    //银行卡专题
    @Test
    public void 银行卡_入合花子卡_正向操作_卡扣款成功(){
        runAnalyze("20250711043456777833472");
    }

    @Test
    public void 银行卡_入合花子卡失败_后从余额宝扣款成功_卡入不应展示(){
        runAnalyze("20250712079420736690176");
    }
    //======================================
    @Test
    public void 银行卡_红包买买基金撤单_退回银行卡失败_再退回到余额户(){
        runAnalyze("20250713002364623703040");
    }

    @Test
    public void 银行卡红包买余额宝_存在未知节点(){
        runAnalyze("20250523062677066578944");
    }

    @Test
    public void 余额托管子户到余额再到合花子卡(){
        runAnalyze("20250711041311876700160");
    }

    @Test
    public void 余额宝转出到银行卡失败_到余额(){
        runAnalyze("20250715007719032343552");
    }


    /**
     * 合花子卡消费场景
     * 合花子卡->泛金融余额户
     */
    @Test
    public void 合花子卡消费到泛金融余额户(){
        runAnalyze("20250712068595013798912");
    }

    @Test
    public void k12子卡转入零花钱(){
        //资金流：0700子卡->余额托管子户
        runAnalyze("20250416062865844861952");
    }

    @Test
    public void k12成员转入群主(){
        //资金流：成员余额宝解冻->成员余额->群主余额->群主余额宝->群主0700子卡
        runAnalyze("20250101042092235193344");
    }

    /**
     * 跨成员0700到余额，当前暂不支持
     */
    @Test
    public void 成员转出操作_群主0700子卡_资金转出到成员余额(){
        //资金流：群主0700子卡->转出至成员余额
        runAnalyze("20250712076056549542912");
    }


    /**
     * ======================================
     * 合花子卡相关项目
     * 合化子卡->余额->余额托管子户
     */
    @Test
    public void 合化子卡_余额_余额托管子户(){
        //资金流：群主0700子卡->转出至成员余额
        runAnalyze("20250712076470032291840");
    }

    @Test
    public void 卡加红包买基金撤单回到卡(){
        //资金流：卡+红包买基金，后买基金撤单
        runAnalyze("20250409066263507926016");
    }

    @Test
    public void 攒着红包买基金_买基金撤单(){
        //资金流：攒着+红包买基金，后买基金撤单
        runAnalyze("20250106052021778054144");
    }

    @Test
    public void 余额宝转出到余额(){
        //资金流：攒着+红包买基金，后买基金撤单
        runAnalyze("20250417078143055068160");
    }
}
