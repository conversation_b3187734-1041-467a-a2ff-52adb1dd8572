<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <param name="Target" value="System.out"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1}:%L - %m%n"/>
        </layout>
        <!-- 过滤低于WARN级别的日志 -->
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="levelMin" value="INFO"/>
            <param name="levelMax" value="FATAL"/>
        </filter>
    </appender>
    <!-- 滚动文件输出 -->
    <appender name="FILE" class="org.apache.log4j.RollingFileAppender">
        <param name="File" value="logs/app.log"/>
        <param name="Append" value="true"/>
        <param name="MaxFileSize" value="100MB"/>
        <param name="MaxBackupIndex" value="10"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{ISO8601} [%t] %-5p %c{36} - %m%n"/>
        </layout>
    </appender>
    <!-- 异步日志输出（性能优化） -->
    <appender name="ASYNC" class="org.apache.log4j.AsyncAppender">
        <param name="BufferSize" value="1000"/>
        <appender-ref ref="FILE"/>
    </appender>
    <!-- 包级自定义配置 -->
    <logger name="com.alipay.findataquality.facade">
        <level value="DEBUG"/>
    </logger>
    <logger name="com.alipay.findataquality.service">
        <level value="DEBUG"/>
    </logger>
    <!-- 根Logger配置 -->
    <root>
        <priority value="INFO"/>
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC"/>
    </root>

</log4j:configuration>
