package com.alipay.findataquality.service.utils;

import com.alibaba.fastjson.JSON;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowAnalyzeRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.ModelCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.service.config.GlobalConfig;
import com.alipay.findataquality.service.model.FundFlowTestData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph.FundFlowAnalyze;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph.FundFlowTransGraph;
import com.alipay.findataquality.service.rpc.fundFlowCheck.impl.FundFlowCheckServiceImpl;
import com.alipay.sofa.common.utils.AssertUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.alipay.findataquality.service.config.GlobalConfig.initLog;

/**
 * @ClassName CheckUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/24 17:48
 * @Version V1.0
 **/
public class FundFlowUtil {
    private static final Logger logger = LoggerFactory.getLogger(FundFlowUtil.class);

    /**
     * 根据shareCode进行数据校验
     * @param checkSceneClass
     * @param shareCode
     * @return
     */
    public static FundFlowCheckResult checkData(Class checkSceneClass, String shareCode){
        GlobalConfig.initLog();
        //获取数据
        String sql = "SELECT * FROM full_trace_validate_data_fetch_record where share_code = '"+shareCode+"'";
        List<FundFlowTestData> dataList = MySqlUtil.fetchData(sql);
        if(dataList==null){
            return null;
        }
        //执行校验场景
        for (FundFlowTestData data:dataList) {
            TableDataContext context = new TableDataContext();
            FundFlowTransGraph graph = new FundFlowTransGraph();

            //获取资金流图
            FundFlowAnalyze analyze = new FundFlowAnalyze();
            List<TransactionGroup> groupList = analyze.analyzeFundFlowGroup(data.getContent());

            context.initFromContent(data.getContent(),graph.getFundFlowClasses(),groupList);
            ExecutorService service = Executors.newFixedThreadPool(10);
            context.setExecutorService(service);
            AbstractCheckScene checkScene = null;
            try {
                checkScene = (AbstractCheckScene)checkSceneClass.getConstructor(TableDataContext.class).newInstance(context);
                checkScene.setDataContext(context);
                FundFlowCheckResult result = new FundFlowCheckResult();
                result = checkScene.executeTask();
//                logger.info("result:{}",result.toString());
                logger.info("result:{}", JSON.toJSONString(result));

                return result;
            } catch (Exception e) {
                //e.printStackTrace();
                continue;
            }
        }
        return null;
    }

    public static String printString(FundFlowCheckResult result) {
        StringBuilder sb = new StringBuilder();

        if (!CollectionUtils.isEmpty(result.getCheckResultList())){
            sb.append("当前【资金规则校验】执行结果共包含"+result.getCheckResultList().size()+"条规则\n");
            for (int i = 0; i < result.getCheckResultList().size(); i++) {
                sb.append("========================\n");
                RuleCheckResult curr = result.getCheckResultList().get(i);
                if(curr!=null){
                    sb.append("第"+(i+1)+"条规则执行详情如下:\n");
                    sb.append(result.getCheckResultList().get(i).getPrintStr());
                }else{
                    sb.append("第"+(i+1)+"条规则执行结果为null\n");
                }
            }
        }

        if (!CollectionUtils.isEmpty(result.getModelCheckResultList())) {
            sb.append("当前【资金模型校验】执行结果共包含" + result.getModelCheckResultList().size() + "条规则\n");
            for (int i = 0; i < result.getModelCheckResultList().size(); i++) {
                sb.append("========================\n");
                ModelCheckResult curr = result.getModelCheckResultList().get(i);
                if (curr != null) {
                    sb.append("第" + (i + 1) + "条规则执行详情如下:\n");
                    sb.append(result.getModelCheckResultList().get(i).getPrintStr());
                } else {
                    sb.append("第" + (i + 1) + "条规则执行结果为null\n");
                }
            }
        }
        sb.append("========================\n执行统计信息如下：\n"+result.getExtInfo());
        return sb.toString();
    }


    public static void runAnalyze(String shareCode){
        initLog();
        String sql = "SELECT * FROM full_trace_validate_data_fetch_record where share_code = '"+shareCode+"' order by gmt_create desc limit 100";
        //sql = "SELECT * FROM full_trace_validate_data_fetch_record where scene_code = 'FINANCE_ASSET_QUERY' order by gmt_create asc limit 200";
        List<FundFlowTestData> dataList = MySqlUtil.fetchData(sql);
        if(dataList==null||dataList.isEmpty()){
            logger.error("获取执行结果为空，执行终止");
            return;
        }

        for (int i = 0; i < dataList.size(); i++) {
            FundFlowTestData data = dataList.get(i);
            logger.info("处理数据,sceneCode={},entranceCode={},shareCode={}", data.getSceneCode(), data.getEntranceCode(), data.getShareCode());
            FundFlowAnalyzeRequest request = new FundFlowAnalyzeRequest();
            request.setDataJson(data.getContent());
            analyzeData(request);
        }
    }

    public static void analyzeData(FundFlowAnalyzeRequest request) {
        FundFlowCheckServiceImpl fundFlowCheckService = new FundFlowCheckServiceImpl();
        logger.info("资金流程如下：");
        List<TransactionGroup> groupList = fundFlowCheckService.analyzeFundFlowGraph(request).getTransactionGroupList();
        if(groupList==null||groupList.isEmpty()){
            logger.info("未获取到资金流程");
        }
        String transactionStr = null;
        for(int i=0;i<groupList.size();i++){
            List<AssetTransaction> list = groupList.get(i).getTransactionList();
            System.out.println(groupList.get(i).getTraceId());
            for (int j = 0; j < list.size(); j++) {
                AssetTransaction transaction = list.get(j);
                transactionStr = transaction.getAssetTransactionContent(false);
                System.out.println(transactionStr);
            }
            if(i!=groupList.size()-1){
                logger.info("============================================");
            }
        }

        logger.info("绘图流程如下：");
        for(int i=0;i<groupList.size();i++){
            TransactionGroup current = groupList.get(i);
            current.getNodeList();
            ;
            for (int j=1; j<=current.getNodeList().size();j++) {
                GraphNode node = current.getNodeList().get(j-1);
                System.out.println("node"+j+", id="+node.getId()+",label="+node.getLabel()+",extInfo="+node.getExtInfo());
            }
            for (int k=1; k<=current.getEdgeList().size();k++) {
                GraphEdge edge = current.getEdgeList().get(k-1);
                System.out.println("edge"+k+", source="+edge.getSource()+",to="+edge.getTarget()+",label="+edge.getLabel());
            }
            if(i!=groupList.size()-1){
                System.out.println("============================================");
            }
        }
        //List<TransactionView> views = transactions.stream().map(x->new TransactionView(x)).collect(Collectors.toList());
        System.out.println("");
        AssertUtil.isTrue(transactionStr!=null&&!transactionStr.contains("未知"));
    }
}
