package com.alipay.findataquality.service.fundFlowCheckTest;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowCheckRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.service.config.GlobalConfig;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelMainGraphCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelNodeCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.balanceModel.FundFlowModelSubGraphCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb.YebFcSubCardCommonCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.checkScene.yeb.YebFcTimeCommonCheck;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.AftransTaskData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowData.yebData.FinancingcoreData;
import com.alipay.findataquality.service.rpc.fundFlowCheck.fundFlowGraph.FundFlowTransGraph;
import com.alipay.findataquality.service.rpc.fundFlowCheck.impl.FundFlowCheckServiceImpl;
import com.alipay.findataquality.service.model.FundFlowTestData;
import com.alipay.findataquality.service.utils.FundFlowUtil;
import com.alipay.findataquality.service.utils.MySqlUtil;
import org.junit.jupiter.api.Test;
import java.util.List;

import static com.alipay.findataquality.service.utils.FundFlowUtil.checkData;

/**
 * @ClassName FundFlowCheckTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/13 17:37
 * @Version V1.0
 **/
public class FundFlowCheckTest {

    @Test
    public void testCheck(){
        FundFlowCheckServiceImpl service = new FundFlowCheckServiceImpl();
        GlobalConfig.initLog();
        String sql = "SELECT * FROM full_trace_validate_data_fetch_record where share_code = '20241216052422793978880'";
        //String sql = "SELECT * FROM full_trace_validate_data_fetch_record where share_code = '20250408078449835257856'";

        List<FundFlowTestData> dataList = MySqlUtil.fetchData(sql);
        if(dataList==null){
            return;
        }
        for (FundFlowTestData data:dataList
             ) {
            TableDataContext context = new TableDataContext();
            FundFlowTransGraph graph = new FundFlowTransGraph();
            context.initFromContent(data.getContent(),graph.getFundFlowClasses(),null);
            AssetRecordData fcData = context.getTableDataMapByClass().get(FinancingcoreData.class);

            List<String> joinVarsList = fcData.joinVars("real_amount","inst_id");

            Integer realAmount = fcData.findVars("real_amount").sum();

            AssetRecordData newRecordExt = fcData.findJsonData("ext_info","assetOpertype == 'T1_CHANGE' || BIZ_ACTION_TYPE == 'REVOKEPAY'");

            AssetRecordData newRecord = fcData.findData("sub_biz_type == '039001' || asset_order_id == '20241129000940091000470010405296'");

            Integer realAmount2 = newRecord.findVars("real_amount").sum(100);

            Integer realAmount3 = newRecord.findVars("real_amount").sum();

            AssetRecordData aftransData = context.getTableDataMapByClass().get(AftransTaskData.class).findData
                    ("id == '20241129009131901100470000604061' || command_type != 'AFTRANS_INCREASE'");

            AssetRecordData aftransDataFind = aftransData.findTable("yebcore.yebs_subcard_aftrans_task_47");

            List<DataCoordinate> dataCoordinateList = fcData.findVars("real_amount").getAllDataCoordinate();

            FundFlowCheckRequest request = new FundFlowCheckRequest();
            request.setDataJson(data.getContent());
//            System.out.println(request);
            FundFlowCheckResult result = service.checkFundFlow(request);
            System.out.println(result.toString());
        }
    }



    @Test
    public void fc子卡数据同分账数据汇总金额相等校验_为空(){
        checkData(YebFcSubCardCommonCheck.class,"20250607089674606332928");
    }

    @Test
    public void fc子卡数据同分账数据汇总金额相等校验_非空(){
        checkData(YebFcSubCardCommonCheck.class,"20241216052422793978880");
    }

    @Test
    public void 资金模型单节点校验(){
        checkData(FundFlowModelNodeCheck.class,"20241216052422793978880");
    }

    @Test
    public void 资金模型总图校验(){
        FundFlowUtil.checkData(FundFlowModelMainGraphCheck.class,"20241216052422793978880");
    }
    @Test
    public void 资金模型分图校验(){
        checkData(FundFlowModelSubGraphCheck.class,"20241216052422793978880");
    }

}
