package com.alipay.findataquality.service.fundFlowCheckTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.TreeMap;

public class HomologousSourceCheckServiceTest {

    public static void main(String[] args) {
        // 原始无序JSON示例
        String originalJson = "{\n" +
                "        \"gmt_create\": \"2024-11-26 17:04:06.0\",\n" +
                "        \"biz_dt\": \"2024-11-26 17:02:34.0\",\n" +
                "        \"memo\": \"红包奖励发放\",\n" +
                "        \"biz_context\": \"{}\",\n" +
                "        \"gmt_modified\": \"2024-11-26 17:04:08.0\",\n" +
                "        \"cnl_pd_code\": \"UR220100100000000001\",\n" +
                "        \"biz_pd_code\": \"UR220100100000000001\",\n" +
                "        \"fund_code\": \"050003\",\n" +
                "        \"sub_biz_type\": \"060001\",\n" +
                "        \"biz_request_id\": \"3f82afa33485a61b469a20d961426d29\",\n" +
                "        \"cnl_ev_code\": \"22010002\",\n" +
                "        \"biz_no\": \"20241126009350001010670000441986\",\n" +
                "        \"payment_id\": \"2024112630000000670640277716\",\n" +
                "        \"business_type\": \"T1_CHANGE\",\n" +
                "        \"clear_dt\": \"2024-11-26 17:02:34.0\",\n" +
                "        \"inst_id\": \"SHUMIJJ\",\n" +
                "        \"trans_dt\": \"2024-11-26 17:04:06.538\",\n" +
                "        \"pd_code\": \"********************\",\n" +
                "        \"ev_code\": \"********\",\n" +
                "        \"asset_account_type\": \"fundpay_share\",\n" +
                "        \"asset_account_no\": \"60102277515666710156\",\n" +
                "        \"biz_type\": \"100\",\n" +
                "        \"bill_detail_id\": \"2024112630000109670614035783\",\n" +
                "        \"fund_inst\": \"BSFDE2CN\",\n" +
                "        \"out_biz_no\": \"20241126009350001010670000443234\",\n" +
                "        \"ext_info\": \"{\\\"assetOpertype\\\":\\\"T1_CHANGE\\\",\\\"payerAssetTypeCode\\\":\\\"ALIPAY_FINANCING_TRANS_VOUCHER\\\",\\\"fcOpUserId\\\":\\\"****************\\\",\\\"fcOpSaleInst\\\":\\\"SHUMIJJ\\\",\\\"orgPmtDt\\\":\\\"*************\\\",\\\"yebBizType\\\":\\\"FCTRANSFER\\\",\\\"BIZ_MODE\\\":\\\"FP_SENIOR\\\",\\\"BIZ_PROD\\\":\\\"fncpay20003\\\",\\\"mfProdkey\\\":\\\"N\\\",\\\"payeeAssetTypeCode\\\":\\\"MONEY_FUND\\\",\\\"fundSource\\\":\\\"FUND\\\",\\\"fcOpCode\\\":\\\"050003\\\",\\\"strategyConsultMark\\\":\\\"Y\\\",\\\"fcTraceId\\\":\\\"0b7c8f1a1732611843506343933389\\\",\\\"payeeAssetType\\\":\\\"MONEYFUND\\\",\\\"currentMasterInstId\\\":\\\"SHUMIJJ\\\",\\\"VOUCHERCUSTOP\\\":\\\"Y\\\",\\\"transType\\\":\\\"voucherTrans\\\",\\\"transMode\\\":\\\"Y\\\",\\\"payerAssetType\\\":\\\"VOUCHER\\\",\\\"BIZ_ACTION_TYPE\\\":\\\"PAY\\\",\\\"mybankCheckNo\\\":\\\"20241126009350001010670000441986\\\",\\\"fcAcType\\\":\\\"fundpay_share\\\",\\\"fcHighAvailable\\\":\\\"T\\\"}\",\n" +
                "        \"pmt_dt\": \"2024-11-26 17:02:34.0\",\n" +
                "        \"cnl_no\": \"20241126009350003010670000346052\",\n" +
                "        \"gmt_commit\": \"2024-11-26 17:04:08.492241\",\n" +
                "        \"asset_order_id\": \"20241126000940091000670012226941\",\n" +
                "        \"real_amount\": \"100\",\n" +
                "        \"user_id\": \"****************\",\n" +
                "        \"biz_ev_code\": \"********\",\n" +
                "        \"status\": \"S\"\n" +
                "      }";

        // 转换为排序后的JSON对象
//        Object sorted = sortJson(JSON.parse(originalJson));
//
//        // 生成格式化后的JSON字符串
//        String sortedJson = JSON.toJSONString(sorted,
//                SerializerFeature.PrettyFormat,
//                SerializerFeature.WriteMapNullValue,
//                SerializerFeature.DisableCircularReferenceDetect);
//
//        System.out.println(sortedJson);

        JSONArray jsonArray = new JSONArray();
        JSONObject jsonDetail1 = new JSONObject(true);
        jsonDetail1.put("checkResultType", "1");//1-FAIL
        jsonArray.add(jsonDetail1);

        JSONObject jsonDetail2 = new JSONObject(true);
        jsonDetail2.put("checkResultType", "0");//0=SUCCESS
        jsonArray.add(jsonDetail2);

        System.out.println("排序前："+jsonArray.toJSONString());

        jsonArray.sort((o1, o2) -> {
            JSONObject obj1 = (JSONObject) o1;
            JSONObject obj2 = (JSONObject) o2;
            return obj1.getString("checkResultType").compareTo(obj2.getString("checkResultType"));
        });
        System.out.println("排序后："+jsonArray.toJSONString());

    }

    private static Object sortJson(Object obj) {
        if (obj instanceof JSONObject) {
            // 处理JSON对象
            JSONObject jsonObject = (JSONObject) obj;
            TreeMap<String, Object> sortedMap = new TreeMap<>();

            jsonObject.forEach((key, value) -> {
                sortedMap.put(key, sortJson(value));
            });

            return sortedMap;
        } else if (obj instanceof JSONArray) {
            // 处理JSON数组
            JSONArray array = (JSONArray) obj;
            JSONArray sortedArray = new JSONArray(array.size());

            array.forEach(item -> {
                sortedArray.add(sortJson(item));
            });

            return sortedArray;
        }
        return obj;
    }
}
