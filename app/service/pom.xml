<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <parent>
		<groupId>com.alipay</groupId>
		<artifactId>findataquality-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>findataquality-service</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>findataquality-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>findataquality-model</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>ddcs-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>rpc-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>alipay-security-core</artifactId>
					<groupId>com.alipay.security</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-max-sofa-boot-starter</artifactId>
			<version>1.7.2</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-max</artifactId>
			<version>1.7.2</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.common</groupId>
			<artifactId>tracer</artifactId>
		</dependency>
		<!--日志打印，此处使用log4j-->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		</dependency>
		<!--log4j的provider，此处使用reload4j-->
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-reload4j</artifactId>
		</dependency>
		<!--单元测试-->
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<!--动态对象创建-->
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
		<!--chair tfapi-->
		<dependency>
			<groupId>com.alipay.chair.facade</groupId>
			<artifactId>tfapi-facade</artifactId>
			<version>1.6.0</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>easyexcel</artifactId>-->
<!--		</dependency>-->
		<!-- Apache POI for Excel parsing -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.3</version>
		</dependency>
		<!-- Commons IO (必需) -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.16.1</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-mcp-server-webmvc-sofa-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>alipay-security-core</artifactId>
					<groupId>com.alipay.security</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-mcp-client-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-codefuse-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-antllm-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-modelops-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>sofa-ai-bom</artifactId>
			<version>1.7.2</version>
			<scope>import</scope>
			<type>pom</type>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>tracer-core</artifactId>
			<version>4.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.alipay.common</groupId>
			<artifactId>tracer</artifactId>
			<version>4.0.3</version>
		</dependency>
	</dependencies>

</project>
