<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay</groupId>
		<artifactId>findataquality-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>findataquality-bootstrap</artifactId>
	<version>0.0.1-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.alipay</groupId>
			<artifactId>findataquality-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>actuator-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>insight-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>isle-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>log4j2-alipay-sofa-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa</groupId>
			<artifactId>tracer-alipay-sofa-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.skyscreamer</groupId>
					<artifactId>jsonassert</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<outputDirectory>../../target/boot</outputDirectory>
					<classifier>executable</classifier>
					<excludes>
						<exclude>
							<groupId>ch.qos.logback</groupId>
							<artifactId>logback-classic</artifactId>
						</exclude>
						<exclude>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-log4j12</artifactId>
						</exclude>
						<exclude>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-jdk14</artifactId>
						</exclude>
						<exclude>
							<groupId>org.jboss.slf4j</groupId>
							<artifactId>slf4j-jboss-logging</artifactId>
						</exclude>
						<exclude>
							<groupId>org.slf4j</groupId>
							<artifactId>slf4j-jcl</artifactId>
						</exclude>
						<exclude>
							<groupId>org.apache.logging.log4j</groupId>
							<artifactId>log4j-to-slf4j</artifactId>
						</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<id>package</id>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<dependencies>
					<dependency>
						<groupId>com.alipay.sofa</groupId>
						<artifactId>sofa-boot-alipay-loader-tools</artifactId>
						<version>${sofa.boot.alipay.version}</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>2.6</version>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<encoding>UTF-8</encoding>
							<outputDirectory>../../target</outputDirectory>
							<resources>
								<resource>
									<directory>../../conf</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-configs</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<encoding>UTF-8</encoding>
							<outputDirectory>../../target/config</outputDirectory>
							<resources>
								<resource>
									<directory>./src/main/resources/config/${app}</directory>
									<includes>
										<include>*.properties</include>
										<include>*.yaml</include>
										<include>*.yml</include>
									</includes>
								</resource>
								<resource>
									<directory>./src/main/resources/config</directory>
									<includes>
										<include>*.properties</include>
										<include>*.yaml</include>
										<include>*.yml</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>16</source>
                    <target>16</target>
                </configuration>
            </plugin>
        </plugins>
	</build>

</project>
