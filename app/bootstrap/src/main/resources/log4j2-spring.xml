<?xml version="1.0" encoding="UTF-8"?>
    <Configuration>
        <Appenders>
            <Console name="STDOUT-APPENDER" target="SYSTEM_OUT">
                <PatternLayout pattern="%d %-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
            </Console>

            <Console name="STDERR-APPENDER" target="SYSTEM_ERR">
                <PatternLayout pattern="%d %-5p %c{2} - %m%n%throwable" charset="UTF-8"/>
            </Console>

            <RollingFile name="ERROR-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/common-error.log"
                         filePattern="${ctx:logging.path}/${ctx:spring.application.name}/common-error.log.%d{yyyy-MM-dd}"
                         append="true">
                <PatternLayout
                        pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                        charset="UTF-8"/>
                <TimeBasedTriggeringPolicy/>
                <DefaultRolloverStrategy/>
                <ThresholdFilter level="ERROR"/>
            </RollingFile>

            <RollingFile name="APP-DEFAULT-APPENDER" fileName="${ctx:logging.path}/${ctx:spring.application.name}/app-default.log"
                         filePattern="${ctx:logging.path}/${ctx:spring.application.name}/app-default.log.%d{yyyy-MM-dd}"
                         append="true">
                <PatternLayout
                        pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                        charset="UTF-8"/>
                <TimeBasedTriggeringPolicy/>
                <DefaultRolloverStrategy/>
            </RollingFile>

            <RollingFile name="SPRING-APPENDER" fileName="${ctx:logging.path}/spring/spring.log"
                         filePattern="${ctx:logging.path}/spring/spring.log.%d{yyyy-MM-dd}"
                         append="true">
                <PatternLayout
                        pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                        charset="UTF-8"/>
                <TimeBasedTriggeringPolicy/>
                <DefaultRolloverStrategy/>
            </RollingFile>

            <RollingFile name="NO-USAGE-APPENDER" fileName="${ctx:logging.path}/no-usage/no-usage.log"
                         filePattern="${ctx:logging.path}/no-usage/no-usage.log.%d{yyyy-MM-dd}"
                         append="true">
                <PatternLayout
                        pattern="%d [%X{traceId} %X{rpcId} - %X{loginUserEmail}/%X{loginUserID}/%X{remoteAddr}/%X{clientId} - %X{requestURIWithQueryString}] %-5p %c{2} - %m%n%throwable"
                        charset="UTF-8"/>
                <TimeBasedTriggeringPolicy/>
                <DefaultRolloverStrategy/>
            </RollingFile>
        </Appenders>

        <Loggers>
            <AsyncLogger name="STDOUT" additivity="false" level="info">
                <AppenderRef ref="STDOUT-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="STDERR" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="STDERR-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="com.taobao.tair" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="NO-USAGE-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="com.taobao.vipserver" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="NO-USAGE-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="com.taobao.remoting" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="NO-USAGE-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="com.alipay.findataquality.acts.test" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="APP-DEFAULT-APPENDER"/>
                <AppenderRef ref="STDOUT-APPENDER"/>
                <AppenderRef ref="ERROR-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="com.alipay.findataquality" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="APP-DEFAULT-APPENDER"/>
                <AppenderRef ref="ERROR-APPENDER"/>
                <AppenderRef ref="STDOUT-APPENDER"/>
            </AsyncLogger>

            <AsyncLogger name="org.springframework" additivity="false" level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="ERROR-APPENDER"/>
                <AppenderRef ref="SPRING-APPENDER"/>
            </AsyncLogger>

            <AsyncRoot level="${ctx:logging.level.com.alipay.findataquality}">
                <AppenderRef ref="APP-DEFAULT-APPENDER"/>
                <AppenderRef ref="ERROR-APPENDER"/>
            </AsyncRoot>
        </Loggers>
    </Configuration>