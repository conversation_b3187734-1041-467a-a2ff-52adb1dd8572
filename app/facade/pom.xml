<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alipay</groupId>
		<artifactId>findataquality-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>
	<artifactId>findataquality-facade</artifactId>
	<version>${facade.version}</version>

	<properties>
		<java.version>8</java.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.sofa.common</groupId>
			<artifactId>sofa-common-tools</artifactId>
		</dependency>
		<dependency>
			<groupId>alipay-common-lang</groupId>
			<artifactId>alipay-common-lang</artifactId>
		</dependency>
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alipay.zoneclient</groupId>
			<artifactId>zoneclient-core</artifactId>
		</dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

		<!--		<dependency>-->
<!--			<groupId>com.alipay.sofa</groupId>-->
<!--			<artifactId>sofa-ai-core</artifactId>-->
<!--			<version>1.7.2</version>-->
<!--		</dependency>-->
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>9</source>
					<target>9</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
