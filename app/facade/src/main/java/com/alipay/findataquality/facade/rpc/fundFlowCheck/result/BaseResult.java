package com.alipay.findataquality.facade.rpc.fundFlowCheck.result;
import java.io.Serializable;

/**
 * @ClassName CommonResult
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 15:26
 * @Version V1.0
 **/
public class BaseResult implements Serializable {

    /**  */
    private static final long serialVersionUID = 1L;


    private boolean           success          = false;


    private String            resultCode;


    private String            resultDesc;


    private String            resultView;

    private String            traceId;

    /**
     * Getter method for property success.
     *
     * @return property value of success
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * Setter method for property success.
     *
     * @param success value to be assigned to property success
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }

    /**
     * Getter method for property resultCode.
     *
     * @return property value of resultCode
     */
    public String getResultCode() {
        return resultCode;
    }

    /**
     * Setter method for property resultCode.
     *
     * @param resultCode value to be assigned to property resultCode
     */
    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    /**
     * Getter method for property resultDesc.
     *
     * @return property value of resultDesc
     */
    public String getResultDesc() {
        return resultDesc;
    }

    /**
     * Setter method for property resultDesc.
     *
     * @param resultDesc value to be assigned to property resultDesc
     */
    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    /**
     * Getter method for property resultView.
     *
     * @return property value of resultView
     */
    public String getResultView() {
        return resultView;
    }

    /**
     * Setter method for property resultView.
     *
     * @param resultView value to be assigned to property resultView
     */
    public void setResultView(String resultView) {
        this.resultView = resultView;
    }

    /**
     * Getter method for property traceId.
     *
     * @return property value of traceId
     */
    public String getTraceId() {
        return traceId;
    }

    /**
     * Setter method for property traceId.
     *
     * @param traceId value to be assigned to property traceId
     */
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    @Override
    public String toString() {
        return "CommonResult{" +
                "success=" + success +
                ", resultCode='" + resultCode + '\'' +
                ", resultDesc='" + resultDesc + '\'' +
                ", resultView='" + resultView + '\'' +
                ", traceId='" + traceId + '\'' +
                '}';
    }
}