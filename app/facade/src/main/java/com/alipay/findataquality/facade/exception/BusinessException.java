package com.alipay.findataquality.facade.exception;

/**
 * @ClassName BusinessException
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/15 16:52
 * @Version V1.0
 **/
public class BusinessException extends RuntimeException{
    private static final long serialVersionUID = -2814396186361053871L;
    private final String errorCode;

    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(BusinessErrorEnum errorEnum, String message) {
        super(errorEnum.getDesc()+":"+message);
        this.errorCode = errorEnum.getCode();
    }

    public String getErrorCode() {
        return this.errorCode;
    }
}
