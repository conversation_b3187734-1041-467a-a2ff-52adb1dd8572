package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result;

import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.DependencyAnalyzerModel;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.BaseResult;

import java.util.List;

/**
 * 强弱依赖分析结果
 */
public class DependencyAnalyzerResult  extends BaseResult {

    private static final long serialVersionUID = 1L;

    /*
     * 场景id
     */
    String sceneId;
    /**
     * 场景名称
     */
    String sceneName;
    /**
     * 迭代地址
     */
    String iterateAddress;
    /**
     * 对照组
     */
    DependencyAnalyzerModel comparisonGroup;

    /**
     * 实验组
     */
    List<DependencyAnalyzerModel>  treatmentGroupList;

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getIterateAddress() {
        return iterateAddress;
    }

    public void setIterateAddress(String iterateAddress) {
        this.iterateAddress = iterateAddress;
    }

    public DependencyAnalyzerModel getComparisonGroup() {
        return comparisonGroup;
    }

    public void setComparisonGroup(DependencyAnalyzerModel comparisonGroup) {
        this.comparisonGroup = comparisonGroup;
    }

    public List<DependencyAnalyzerModel> getTreatmentGroupList() {
        return treatmentGroupList;
    }

    public void setTreatmentGroupList(List<DependencyAnalyzerModel> treatmentGroupList) {
        this.treatmentGroupList = treatmentGroupList;
    }
}
