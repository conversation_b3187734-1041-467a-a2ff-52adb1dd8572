package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums;

/**
 * @ClassName QueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 14:59
 * @Version V1.0
 **/
public enum QuerySceneEnum {
    /**
     * 余额宝
     */
    YEB_SUPER_TRADE_QUERY("YEB_SUPER_TRADE_QUERY","余额宝交易N合一", QueryDomainEnum.YEB,"order_no","直/代销/转入/转出/冻结/解冻/子卡交易/fc等各类单号",true),
    //余额宝账户
    YEB_ACCOUNT_QUERY("YEB_ACCOUNT_QUERY","余额宝账户签约", QueryDomainEnum.YEB,"user_id","账户",false),
    //余额宝子卡交易
    YEB_SUBCARD_TRADE_QUERY("YEB_SUBCARD_TRADE_QUERY","余额宝子卡交易", QueryDomainEnum.YEB,"order_no","子卡交易单号",true),
    //余额宝转入
    YEB_TRANS_IN_QUERY("YEB_TRANS_IN_QUERY","余额宝转入", QueryDomainEnum.YEB,"order_no","直代销转入单号",true),
    //余额宝转出
    YEB_TRANS_OUT_QUERY("YEB_TRANS_OUT_QUERY","余额宝转出", QueryDomainEnum.YEB,"order_no","直代销转出单号",true),
    //余额宝冻结解冻
    YEB_TRANS_FZ_AND_UNFZ_QUERY("YEB_TRANS_FZ_AND_UNFZ_QUERY","余额宝冻结/解冻", QueryDomainEnum.YEB,"order_no","直代销冻结/解冻单号",true),
    //余额宝fc交易
    YEB_FINANCINGCORE_QUERY("YEB_FINANCINGCORE_QUERY","余额宝FC交易", QueryDomainEnum.YEB,"asset_order_id","FinancingCore单号",true),
    //余额宝转入并切基
    YEB_TRANS_IN_AND_SWITCH("YEB_TRANS_IN_AND_SWITCH","余额宝转入并切基", QueryDomainEnum.YEB,"order_no","直代销转入单号",true),
    /**
     * 资金平台
     */
    //FA取数模型
    FINANCE_ASSET_QUERY("FINANCE_ASSET_QUERY","FA支付", QueryDomainEnum.FINANCE_ASSET,"main_order_id","FinanceAsset主单号",true),
    //FA取数模型-基金策略场景
    FINANCE_ASSET_FINSTRATEGY_QUERY("FINANCE_ASSET_FINSTRATEGY_QUERY","FA支付-基金策略", QueryDomainEnum.FINANCE_ASSET,"strategy_main_order_id","Finstrategy主单号",true),

    /**
     * 基金交易
     */
    //基金交易取数模型
    FINFUNDTRADE_QUERY("FINFUNDTRADE_QUERY","基金交易", QueryDomainEnum.FUND,"order_id","基金交易单号",true),

    /**
     * 资产账
     */
    FAASSET_ACCOUNT_QUERY("FAASSET_ACCOUNT_QUERY","资产账账户", QueryDomainEnum.FAASSET,"asset_id","资产账",false),

    FAASSET_ACCOUNTING_QUERY("FAASSET_ACCOUNTING_QUERY","资产账登账", QueryDomainEnum.FAASSET,"id","资产登账",false),

    FAASSET_FUND_TRADE_QUERY("FAASSET_FUND_TRADE_QUERY","资产账基金场景", QueryDomainEnum.FAASSET,"order_id","基金交易单号",false),

    FAASSET_INSUR_TRADE_QUERY("FAASSET_INSUR_TRADE_QUERY","资产账保险场景", QueryDomainEnum.FAASSET,"unique_id","资产账幂等号",false),


    /**
     * 计收费
     */
    FUND_CHARGE_C_QUERY("FUND_CHARGE_C_QUERY","C类交易计收费", QueryDomainEnum.CHARGE,"bizDate-fundCode","日期及基金编码（使用-分隔）",false);


    private String code;
    private String desc;
    //所属域
    private QueryDomainEnum domainEnum;
    //入口取数参数
    private String inputName;
    //入口取数参数说明
    private String inputDesc;
    //是否支持资金分析
    private boolean supportFundFlow;


    QuerySceneEnum(String code, String desc, QueryDomainEnum domainEnum,String inputName, String inputDesc, boolean supportFundFlow) {
        this.code = code;
        this.desc = desc;
        this.domainEnum = domainEnum;
        this.inputName = inputName;
        this.inputDesc = inputDesc;
        this.supportFundFlow = supportFundFlow;
    }

    public static QuerySceneEnum getByCode(String code){
        for (QuerySceneEnum queryScene : values()) {
            if (queryScene.getCode().equalsIgnoreCase(code)) {
                return queryScene;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public QueryDomainEnum getDomainEnum() {
        return domainEnum;
    }

    public void setDomainEnum(QueryDomainEnum domainEnum) {
        this.domainEnum = domainEnum;
    }

    public String getInputName() {
        return inputName;
    }

    public void setInputName(String inputName) {
        this.inputName = inputName;
    }

    public String getInputDesc() {
        return inputDesc;
    }

    public void setInputDesc(String inputDesc) {
        this.inputDesc = inputDesc;
    }

    public boolean isSupportFundFlow() {
        return supportFundFlow;
    }

    public void setSupportFundFlow(boolean supportFundFlow) {
        this.supportFundFlow = supportFundFlow;
    }
}
