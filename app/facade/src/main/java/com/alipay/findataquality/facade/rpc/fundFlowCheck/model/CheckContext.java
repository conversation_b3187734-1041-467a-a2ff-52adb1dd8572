package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName CheckContext
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/22 15:17
 * @Version V1.0
 **/
public class CheckContext {
    /**
     * 上下文，第一个key为函数名，第二个key为参数名，value为参数值，该上下文主要用于函数内存放计算过的前置变量值，用于后续的校验
     * 如，YeFcCheck#checkFunc1, key->value
     */
    private Map<String,Map<String,String>> context= new ConcurrentHashMap<>();

    private Map<String,Object> prepareDataResult;//存储被捞取的数据

    /**
     * 放入上下文值
     * @param funcName
     * @param key
     * @param value
     */
    public void putVar(String funcName, String key, String value){
        if (context.containsKey(funcName)){
            context.get(funcName).put(key, value);
        }else {
            Map<String,String> map = new ConcurrentHashMap<>();
            map.put(key,value);
            context.put(funcName,map);
        }
    }

    public Map<String, Object> getPrepareDataResult() {
        return prepareDataResult;
    }

    public void setPrepareDataResult(Map<String, Object> prepareDataResult) {
        this.prepareDataResult = prepareDataResult;
    }

    /**
     * 获取上下文值
     * @param funcName
     * @param key
     * @return
     */
    public String getValue(String funcName, String key){
        if (context.containsKey(funcName)){
            return context.get(funcName).get(key);
        }
        return null;
    }

    public Map<String, Map<String, String>> getContext() {
        return context;
    }

    public void setContext(Map<String, Map<String, String>> context) {
        this.context = context;
    }
}
