package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.sofa.common.utils.StringUtil;
import com.iwallet.biz.common.util.money.Money;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AssetTransNode
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/30 23:28
 * @Version V1.0
 **/
public class AssetTransaction implements Comparable<AssetTransaction>{
    private AssetOperation from;
    private AssetOperation to;
    //资金自操作，如冻结、解冻场景
    private AssetOperation singleOp;
    //private List<AssetOperation> operationList = new ArrayList<>();
    private Money amount;
    private String memo;
    private boolean checkBalance = false;
    private boolean singleOpType = false;
    private Date earliestTime;

    public String getAssetTransactionContent(boolean simple){
        StringBuilder sb = new StringBuilder();
        sb.append((simple?"":this.getEarLiestTimeStr())+"["+this.checkBalance+"] ");
        if(singleOpType){
            sb.append(singleOp.getPayToolType().getDesc()+","+singleOp.getActionType().getDesc()+
                    (simple?"":("," +singleOp.getAmount()+"元")));
        }else{
            sb.append(this.getFrom().getPayToolType().getDesc()+"--"
                    + (simple?"":(this.getAmount()+"元"))+
                    "-->"+this.getTo().getPayToolType().getDesc());
        }
        return sb.toString();
    }

    public AssetTransaction(AssetOperation op1, AssetOperation op2, boolean supplyUnknown){
        //资金减的一方作为from，资金增的一方作为to
        if(op1==null&&op2==null){
            return;
        }
        if (op1 == null) {
            op1=op2;
            op2=null;
        }
        //op1、op2其中一个为null，另一个不为null
        if(op2==null){
            if(op1.getActionType()==AssetActionTypeEnum.DECREASE){
                this.from = op1;
                if(supplyUnknown){
                    this.to= AssetOperation.createUnknown();
                }
            }else{
                this.to = op1;
                this.from = AssetOperation.createUnknown();
            }
            this.amount = op1.getAmount();
            this.checkBalance = false;
            this.earliestTime = op1.getGmtCreate();
        }else{
            //两个都非空
            if (op1.getActionType() == AssetActionTypeEnum.DECREASE) {
                this.from = op1;
                this.to = op2;
            }else{
                this.from = op2;
                this.to = op1;
            }
            this.amount = op1.getAmount();
            this.checkBalance = op1.getAmount().equals(op2.getAmount());
            this.earliestTime = op1.getGmtCreate().before(op2.getGmtCreate())?op1.getGmtCreate():op2.getGmtCreate();
        }
    }

    /**
     * 获取traceId
     * @param
     * @return
     */
    public String getTraceId(){
        if(this.getOperationList().isEmpty()){
            return null;
        }
        //寻找第一个非空traceId
        for (AssetOperation operation:this.getOperationList()) {
            if(StringUtil.isNotBlank(operation.getTraceId())){
                return operation.getTraceId();
            }
        }
        return null;
    }

    /**
     * 获取当前资金交易流程中的所有资金节点
     * @return
     */
    public List<AssetOperation> getOperationList() {
        List<AssetOperation> operationList = new ArrayList<>();
        if(this.from!=null){
            operationList.add(from);
        }
        if(this.to!=null){
            operationList.add(to);
        }
        if(this.singleOp!=null){
            operationList.add(singleOp);
        }
        return operationList;
    }

    /**
     * 单资金操作
     * @param
     */
    public AssetTransaction(AssetOperation op){
        if(op==null){
            return;
        }
        this.singleOp = op;
        this.earliestTime = op.getGmtCreate();
        this.checkBalance = true;
        this.singleOpType = true;
    }

    public AssetOperation getSingleOperation() {
        return singleOp;
    }

    public void setSingleOpType(AssetOperation singleOpType) {
        this.singleOp = singleOpType;
    }

    public Date getEarliestTime() {
        return earliestTime;
    }

    public String getEarLiestTimeStr(){
        // 创建一个SimpleDateFormat对象，并指定格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 使用SimpleDateFormat对象将Date对象格式化为字符串
        return formatter.format(this.getEarliestTime());
    }

    public void setEarliestTime(Date earliestTime) {
        this.earliestTime = earliestTime;
    }

    public boolean isBalance() {
        return checkBalance;
    }

    public void setBalance(boolean balance) {
        checkBalance = balance;
    }

    public AssetOperation getFrom() {
        return from;
    }

    public void setFrom(AssetOperation from) {
        this.from = from;
    }

    public AssetOperation getTo() {
        return to;
    }

    public void setTo(AssetOperation to) {
        this.to = to;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public boolean isSingleOpType() {
        return singleOpType;
    }

    public void setSingleOp(boolean singleOp) {
        singleOpType = singleOp;
    }

    public Date getTransactionDt(){
        if(this.getTo()!=null&&this.getTo().getTransDt()!=null){
            return this.getTo().getTransDt();
        }
        if(this.getFrom()!=null&&this.getFrom().getTransDt()!=null){
            return this.getFrom().getTransDt();
        }
        if(this.getSingleOperation()!=null&&this.getSingleOperation().getTransDt()!=null){
            return this.getSingleOperation().getTransDt();
        }
        return null;
    }

    @Override
    public int compareTo(AssetTransaction o) {
        //先比较创建时间，创建时间早的排前面
        if(this.getEarliestTime().before(o.getEarliestTime())){
            return -1;
        }
        if(this.getEarliestTime().after(o.getEarliestTime())){
            return 1;
        }
        //再比较账务时间，账务时间早的排前面
        if (this.getTransactionDt()!=null&&o.getTransactionDt()!=null) {
            if(this.getTransactionDt().before(o.getTransactionDt())){
                return -1;
            }
            if(this.getTransactionDt().after(o.getTransactionDt())){
                return 1;
            }
        }

        //当资金操作node1->node2,其中node2增，且node2冻结时，先转入再冻结
        if(this.getTo()!=null&&o.getSingleOperation()!=null&&this.getTo().getPayToolType() == o.getSingleOperation().getPayToolType()
        && o.getSingleOperation().getActionType() == AssetActionTypeEnum.FREEZE){
            return -1;
        }

        if(o.getTo()!=null&&this.getSingleOperation()!=null&&o.getTo().getPayToolType() == this.getSingleOperation().getPayToolType()
                && this.getSingleOperation().getActionType() == AssetActionTypeEnum.FREEZE){
            return 1;
        }

        //先解冻再转出
        if(this.getFrom()!=null&&o.getSingleOperation()!=null&&this.getFrom().getPayToolType() == o.getSingleOperation().getPayToolType()
                && o.getSingleOperation().getActionType() == AssetActionTypeEnum.UNFREEZE){
            return 1;
        }

        if(o.getFrom()!=null&&this.getSingleOperation()!=null&&o.getFrom().getPayToolType() == this.getSingleOperation().getPayToolType()
                && this.getSingleOperation().getActionType() == AssetActionTypeEnum.UNFREEZE){
            return -1;
        }

        //串形资金形式，A->B,B->C，则按照资金顺序排序，即A->B在B->C之前
        if(this.getTo()!=null&&o.getFrom()!=null&& this.getTo().getPayToolType()==o.getFrom().getPayToolType()){
            return -1;
        }

        if(o.getTo()!=null&&this.getFrom()!=null&& o.getTo().getPayToolType()==this.getFrom().getPayToolType()){
            return 1;
        }

        return 0;
    }
}
