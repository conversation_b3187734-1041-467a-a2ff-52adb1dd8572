package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.sofa.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AssetTransactionGroup
 * @Description 资金流组
 * <AUTHOR>
 * @Date 2024/12/5 14:51
 * @Version V1.0
 **/
public class TransactionGroup {
    private static Logger logger =  LoggerFactory.getLogger(TransactionGroup.class);
    /**
     * traceId
     */
    private String traceId;
    /**
     * 对应的交易列表
     */
    private List<AssetTransaction>transactionList = new ArrayList<>();
    /**
     * 转换为端上透出值结构
     */
    private List<GraphNode> nodeList = new ArrayList<>();
    private List<GraphEdge> edgeList = new ArrayList<>();

    public void addTransaction(AssetTransaction transaction){
        this.transactionList.add(transaction);
        if(transaction!=null && StringUtil.isNotBlank(transaction.getTraceId())){
            this.traceId = transaction.getTraceId();
        }
    }

    public void covertTransactionToGraph(){
        Map<String,Boolean> transactionMap = new HashMap<>();
        for (int i=0; i<this.transactionList.size(); i++) {
            AssetTransaction current = this.transactionList.get(i);
            GraphEdge edge = createEdgeFromTransaction(current,i+1);
            if(edge==null){
                logger.warn("covertTransactionToGraph出现空边，跳过处理, 资金操作={}",current);
                continue;
            }
            edgeList.add(edge);
            List<AssetOperation> list =  current.getOperationList();
            list.stream().forEach(op->{
                if(op!=null){
                    if(!transactionMap.containsKey(op.getGraphUniqueId())){
                        GraphNode node = new GraphNode(op.getGraphUniqueId(),getPayToolLabel(op),"");
                        node.setColorByOp(op);
                        this.nodeList.add(node);
                        transactionMap.put(op.getGraphUniqueId(),true);
                    }
                }
            });
        }
    }

    /**
     * 获取边的Label描述
     * @param op
     * @return
     */
    private String getPayToolLabel(AssetOperation op){
        //余额宝支付工具展示主体信息
        if (op!=null&&op.isShowCurrentInstIdLabel()) {
            String masterInstId = null;
            if(op.getCurrentMasterInstId()!=null){
                masterInstId = "("+op.getCurrentMasterInstId().getMasterInstDesc()+")";
            }
            return op.getPayToolType().getDesc()+masterInstId;
        }
        return op.getPayToolType().getDesc();
    }

    private GraphEdge createEdgeFromTransaction(AssetTransaction transaction, int step){
        if(transaction==null){
            return null;
        }
        if(transaction.isSingleOpType()){
            AssetOperation op = transaction.getSingleOperation();
            String label = "("+step+")"+op.getActionType().getDesc()+","+
                    op.getAmount()+"元";
            //label = label + getYebOpInstLabelFromTransaction(transaction);
            return new GraphEdge(transaction.getSingleOperation().getGraphUniqueId(),transaction.getSingleOperation().getGraphUniqueId(),label);
        }else{
            String label = "("+step+")"+transaction.getAmount()+"元";
            //label = label + getYebOpInstLabelFromTransaction(transaction);
            return new GraphEdge(transaction.getFrom().getGraphUniqueId(),transaction.getTo().getGraphUniqueId(),label);
        }
    }

    /**
     * 获取交易的双边的流入流出渠道标签
     * @param transaction
     * @return
     */
    private String getYebOpInstLabelFromTransaction(AssetTransaction transaction){
        StringBuilder sb = new StringBuilder();
        if(transaction.isSingleOpType()) {
            AssetOperation op = transaction.getSingleOperation();
            //单节点操作增加主体信息，如:冻结,SHUMIJJ
            if (op.isShowInstIdLabel()) {
                sb.append("," + op.getInstId().getFcInstDesc());
            }
        }else{
            AssetOperation fromOp = transaction.getFrom();
            AssetOperation toOp = transaction.getTo();
            if(fromOp!=null&&fromOp.isShowInstIdLabel()){
                sb.append(","+fromOp.getInstId().getFcInstDesc()+"出");
            }
            if(toOp!=null&&toOp.isShowInstIdLabel()){
                sb.append(","+toOp.getInstId().getFcInstDesc()+"入");
            }
        }
        return sb.toString();
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public List<AssetTransaction> getTransactionList() {
        return transactionList;
    }

    public void setTransactionList(List<AssetTransaction> transactionList) {
        this.transactionList = transactionList;
    }

    public List<GraphNode> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<GraphNode> nodeList) {
        this.nodeList = nodeList;
    }

    public List<GraphEdge> getEdgeList() {
        return edgeList;
    }

    public void setEdgeList(List<GraphEdge> edgeList) {
        this.edgeList = edgeList;
    }
}
