package com.alipay.findataquality.facade.rpc.fundFlowCheck.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.common.utils.StringUtil;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName StrUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/6 22:01
 * @Version V1.0
 **/
public class StrUtil {

    /**
     * 从字符串中获取18位2088uid
     * @param str
     * @return
     */
    public static String get2088UidFromStr(String str){
        if(isBlank(str)||!str.startsWith("2088")||str.length()<16){
            return null;
        }
        return str.substring(0,16);
    }

    public static boolean isBlank(String str){
        return StringUtil.isBlank(str);
    }

    /**
     * 从json数据中放置key值
     * @param jsonData
     * @param key
     * @param value
     * @return
     */
    public static String putJsonValue(String jsonData, String key, String value) {
        if (StringUtil.isBlank(key) || StringUtil.isBlank(value)) {
            return jsonData;
        }
        try {
            //将String数据转换为Json格式
            JSONObject obj = null;
            if (StringUtil.isBlank(jsonData)) {
                obj = new JSONObject();
            } else {
                obj = (JSONObject) JSONObject.parse(jsonData);
            }
            //当value为json格式时，特殊处理，先转换成jsonObj再放入结果中
            if(JSON.isValid(value)){
                try{
                    //先转换成jsonObj再放入结果中
                    JSONObject jsonObject = JSONObject.parseObject(value);
                    obj.put(key,jsonObject);
                }catch (Exception e){
                    try{
                        //若失败转换成jsonArray再放入结果中
                        JSONArray jsonArray = JSONObject.parseArray(value);
                        obj.put(key, jsonArray);
                    }
                    catch (Exception e2){
                        //兜底处理，直接放入结果中
                        obj.put(key, value);
                    }
                }
            }else{
                obj.put(key, value);
            }
            return obj.toJSONString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从json数据中获取key值
     * @param jsonData
     * @return
     */
    public static Map<String,Object> getJsonKvMap(String jsonData) {
        Map<String,Object> map = new HashMap<>();
        if (StringUtil.isBlank(jsonData)) {
            return map;
        }
        try {
            //将String数据转换为Json格式
            JSONObject obj = (JSONObject) JSONObject.parse(jsonData);
            for (String key : obj.keySet()) {
                map.put(key,obj.get(key));
            }
            return map;
        } catch (Exception e) {
            return map;
        }
    }
    /**
     * 判断两个字符串是否相等
     * @param str1
     * @param str2
     * @return
     */
    public static boolean isEqual(String str1, String str2){
        if(str1==null){
            return str2==null;
        }
        return str1.equals(str2);
    }

    /**
     * 计算Md5值
     * @param input
     * @return
     */
    public static String calculateMD5(String input) {
        try {
            // 获取MessageDigest实例，使用MD5算法
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 计算输入字符串的MD5哈希值
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            //throw new RuntimeException("MD5算法不可用", e);
            return null;
        }
    }
}
