package com.alipay.findataquality.facade.rpc.fundFlowCheck.utils;

import java.util.*;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.MissingPropertyException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName ExpressionEvaluatorUtil
 * @Description 表达式计算工具类
 * <AUTHOR>
 * @Date 2025/03/04 18:10
 * @Version V1.0
 **/
public class ExpressionEvaluatorUtil {
    private static final Logger logger = LoggerFactory.getLogger(ExpressionEvaluatorUtil.class);

    /**
     * 计算布尔表达式的值
     * 使用示例：
     * Map<String, Object> variables = Map.of(
     *                 "a", 1,
     *                 "b", "falses",
     *                 "d","2",
     *                 "c", true
     * );
     *
     * // 示例布尔表达式
     * String expression = "a==1 && (b!='falses' || c) && d+a=='21'";
     *
     * // 计算布尔表达式的值
     * Boolean result = evaluateBooleanExpression(variables, expression);
     * @param variables
     * @param expression
     * @param missVarIgnore 是否忽略缺失变量，对于为true时，若表达式中变量缺失，则直接将该变量设为null，重新加入map中继续执行
     * @return
     */
    public static Boolean evaluateBooleanExpression(Map<String, Object> variables, String expression, boolean missVarIgnore, Map<String,String>varSupplyMap) {
        try {
            // 创建一个Binding对象，用于存储变量
            Binding binding = new Binding();

            // 将Java的Map中的变量添加到Binding对象中
            for (Map.Entry<String, Object> entry : variables.entrySet()) {
                binding.setVariable(entry.getKey(), entry.getValue());
            }

            if(missVarIgnore && varSupplyMap!=null){
                // 对于variables中缺失的变量，从varSupplyMap中补充
                for (Map.Entry<String, String> entry : varSupplyMap.entrySet()) {
                    if(!variables.containsKey(entry.getKey())){
                        binding.setVariable(entry.getKey(), entry.getValue());
                    }
                }
            }

            // 创建一个GroovyShell对象，用于执行Groovy脚本
            GroovyShell shell = new GroovyShell(binding);

            // 执行布尔表达式并返回结果
            Object result = shell.evaluate(expression);
            return (Boolean) result;
        }
        catch (MissingPropertyException e){
            logger.warn("表达式{}缺失变量{},自动将该值设为null进行重试", expression,e.getProperty());
            if(missVarIgnore){
                //variables.put(e.getProperty(), null);
                if(varSupplyMap==null){
                    varSupplyMap = new HashMap<>();
                }
                varSupplyMap.put(e.getProperty(), null);
                return evaluateBooleanExpression(variables, expression, missVarIgnore, varSupplyMap);
            }
            return null;
        }
        catch (Exception e) {
            logger.error("表达式执行失败:{},e={}", expression, e);
            return null;
        }
    }
}