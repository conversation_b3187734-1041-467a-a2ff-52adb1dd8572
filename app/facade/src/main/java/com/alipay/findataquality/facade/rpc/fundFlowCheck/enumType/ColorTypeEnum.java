package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName ColorTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/20 20:41
 * @Version V1.0
 **/
public enum ColorTypeEnum {
    RED("RED","红色","#dd0000"),
    GREEN("GREEN","绿色","#008800"),
    BLACK("BLACK","黑色","#000000");

    private String colorType;
    private String colorName;
    private String colorCode;

    ColorTypeEnum(String type, String name, String code) {
        this.colorType = type;
        this.colorName = name;
        this.colorCode = code;
    }

    public String getColorType() {
        return colorType;
    }

    public String getColorName() {
        return colorName;
    }

    public String getColorCode() {
        return colorCode;
    }
}
