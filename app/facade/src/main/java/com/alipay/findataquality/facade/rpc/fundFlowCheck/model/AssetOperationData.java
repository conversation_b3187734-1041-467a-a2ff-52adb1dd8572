package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName AssetOperateBaseData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/19 00:08
 * @Version V1.0
 **/
public class AssetOperationData implements Cloneable{
    /**
     * 资金模型操作序列
     */
    private List<AssetOperation> operationList = new ArrayList<>();

    public List<AssetOperation> getOperationList() {
        return operationList;
    }

    public void setOperationList(List<AssetOperation> operationList) {
        this.operationList = operationList;
    }

    /**
     * clone
     * @return
     */
    public AssetOperationData clone() {
        try {
            AssetOperationData operationData = (AssetOperationData) super.clone();
            List<AssetOperation> operationList = new ArrayList<>();
            for (AssetOperation operation : this.operationList) {
                operationList.add(operation.clone());
            }
            operationData.setOperationList(operationList);
            return operationData;
        } catch (CloneNotSupportedException e) {
            return null;
        }
    }
}
