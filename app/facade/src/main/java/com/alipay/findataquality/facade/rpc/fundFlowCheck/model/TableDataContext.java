package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * @ClassName TableDataContainer
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/19 00:40
 * @Version V1.0
 **/
public class TableDataContext {
    private static final Logger logger = LoggerFactory.getLogger(TableDataContext.class);

    /**
     * 执行线程池
     */
    private ExecutorService executorService;

    /**
     * 表数据列表
     */
    private List<TableData> tableDataList= new ArrayList<>();
    /**
     * 表数据映射，key为表名，value为表数据列表
     */
    private Map<String, List<TableData>> tableDataMap = new HashMap<>();

    /**
     * 表数据映射，key为class，value为表数据列表
     */
    private Map<Class,AssetRecordData> tableDataMapByClass = new HashMap<>();

    /**
     * 资金流图组合
     */
    private List<TransactionGroup> transactionGroupList = new LinkedList();


    /**
     * 校验执行上下文
     */
    private CheckContext checkContext = new CheckContext();

    /**
     * 原始数据
     */
    private String originalData;

    public ExecutorService getExecutorService() {
        return executorService;
    }

    public void setExecutorService(ExecutorService executorService) {
        this.executorService = executorService;
    }

    /**
     * 从文本中初始化上下文
     * @param content
     * @param classes
     */
    public void initFromContent(String content, Class[] classes, List<TransactionGroup> transactionGroups) {
        List<TableData> tableDataList = parseTableData(content);
        initFromDataList(tableDataList);
        initTableDataContext(tableDataList,classes);

        initTransactionGroupList(transactionGroups);

    }



    /**
     * 初始化TransactionGroupList
     * @param transactionGroups
     */
    private void initTransactionGroupList(List<TransactionGroup> transactionGroups) {
        this.transactionGroupList = transactionGroups;
    }


    /**
     * 初始化列表并更新tableDataMap
     * @param tableDataList
     */
    private void initFromDataList(List<TableData> tableDataList) {
        for (TableData tableData : tableDataList) {
            this.tableDataList.add(tableData);
            String key = tableData.getTableFullPath();
            //若存在相同表名的数据，则合并
            if(tableDataMap.containsKey(key)){
                this.getTableDataMap().get(key).add(tableData);
            }else{
                List<TableData> newList = new ArrayList<>();
                newList.add(tableData);
                this.tableDataMap.put(key,newList);
            }
        }

        //设置行号
        for(TableData tableData: this.tableDataList){
            if(!tableData.getColumnData().isEmpty()){
                tableData.getRowIndexList().clear();
                for(int i=0;i<tableData.getColumnData().size();i++){
                    tableData.getRowIndexList().add(i);
                }
            }
        }
    }

    /**
     * 初始化数据实例并放入DataMapByClass
     * @param tableDataList
     * @param classes
     */
    private void initTableDataContext(List<TableData> tableDataList,Class[] classes){
        try {
            for (Class assetClass:classes) {
                Object obj = assetClass.newInstance();
                if(obj instanceof AssetRecordData){
                    AssetRecordData assetRecordData = (AssetRecordData)obj;
                    assetRecordData.initFromDataContext(this);
                    this.getTableDataMapByClass().put(assetRecordData.getClass(),assetRecordData);
                }
            }
        } catch (InstantiationException e) {
            logger.error("tableData上下文初始化异常,e={}",e);
        } catch (IllegalAccessException e) {
            logger.error("tableData上下文初始化非法访问异常,e={}",e);
        }
    }

    /**
     * 从content中解析表数据
     * @param content
     * @return
     */
    private List<TableData> parseTableData(String content){
        List<TableData> tableDataList = new ArrayList<>();
        JSONArray arrayData = JSON.parseArray(content);
        for (int i = 0; i < arrayData.size(); i++) {
            JSONObject obj = (JSONObject) arrayData.get(i);
            String dbName = (String)obj.get("dbName");
            String tableName = (String)obj.get("tableName");
            //创建TableData
            TableData tableData = new TableData();
            tableData.setTableName(tableName);
            tableData.setDbName(dbName);
            //解析列名
            JSONArray columnNameList = (JSONArray)obj.get("columnName");
            for (int j = 0; j < columnNameList.size(); j++) {
                String columnObj = (String)columnNameList.get(j);
                tableData.addColumnName(columnObj);
            }
            //解析列数据
            JSONArray columnDataList = (JSONArray)obj.get("columnData");
            for (int j = 0; j < columnDataList.size(); j++) {
                JSONArray array = (JSONArray)columnDataList.get(j);
                List<String> data = new ArrayList<>();
                for (int k = 0; k < array.size(); k++) {
                    data.add((String)array.get(k));
                }
                tableData.getColumnData().add(data);
                tableData.getRowIndexList().add(j);
            }
            tableDataList.add(tableData);
        }
        return tableDataList;
    }

    public List<TableData> getTableDataList() {
        return tableDataList;
    }

    public void setTableDataList(List<TableData> tableDataList) {
        this.tableDataList = tableDataList;
    }

    public Map<String, List<TableData>> getTableDataMap() {
        return tableDataMap;
    }

    public void setTableDataMap(Map<String, List<TableData>> tableDataMap) {
        this.tableDataMap = tableDataMap;
    }

    public String getOriginalData() {
        return originalData;
    }

    public void setOriginalData(String originalData) {
        this.originalData = originalData;
    }

    public CheckContext getCheckContext() {
        return checkContext;
    }

    public void setCheckContext(CheckContext checkContext) {
        this.checkContext = checkContext;
    }

    public Map<Class, AssetRecordData> getTableDataMapByClass() {
        return tableDataMapByClass;
    }

    public void setTableDataMapByClass(Map<Class, AssetRecordData> tableDataMapByClass) {
        this.tableDataMapByClass = tableDataMapByClass;
    }

    public List<TransactionGroup> getTransactionGroupList() {
        return transactionGroupList;
    }

    public void setTransactionGroupList(List<TransactionGroup> transactionGroupList) {
        this.transactionGroupList = transactionGroupList;
    }

}
