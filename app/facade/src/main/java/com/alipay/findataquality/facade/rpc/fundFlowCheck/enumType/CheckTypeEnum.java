package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName CheckResultTypeEnum
 * @Description 检查类型枚举
 * <AUTHOR>
 * @Date 2024/11/8 16:19
 * @Version V1.0
 **/
public enum CheckTypeEnum {
    //资金规则检查
    RULE_CHECK("RULE_CHECK", "规则检查"),
    //资金模型检查
    MODEL_CHECK("MODEL_CHECK", "模型检查");

    private String code;

    private String desc;

    CheckTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举值
     * @param code
     * @return
     */
    public static CheckTypeEnum getByCode(String code){
        for (CheckTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
