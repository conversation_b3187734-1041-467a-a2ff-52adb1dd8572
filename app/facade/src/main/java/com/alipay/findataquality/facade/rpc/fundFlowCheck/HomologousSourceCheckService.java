package com.alipay.findataquality.facade.rpc.fundFlowCheck;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowCheckRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowHomoCheckResult;
import com.alipay.zoneclient.zoneapi.annotation.ZonePublish;

@ZonePublish(zoneType={"GZ"})
public interface HomologousSourceCheckService {

    /**
     * 同源校验
     * @param fundFlowCheckRequest
     * @return
     */
    public FundFlowHomoCheckResult homologousSourceCheck(FundFlowCheckRequest fundFlowCheckRequest);

    
}
