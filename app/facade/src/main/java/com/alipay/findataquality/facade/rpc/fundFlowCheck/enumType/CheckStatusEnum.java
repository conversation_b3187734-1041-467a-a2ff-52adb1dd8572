package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName CheckResultTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 16:19
 * @Version V1.0
 **/
public enum CheckStatusEnum {
    //规则初始状态
    INIT("INIT", "初始"),
    //规则有效且执行成功
    SUCCESS("SUCCESS", "通过"),
    //规则有效，但执行失败
    FAIL("FAIL", "失败"),
    //规则执行异常
    EXCEPTION("EXCEPTION", "异常"),
    //规则忽略
    IGNORE("IGNORE", "忽略"),
    OTHERS("OTHERS", "其他");


    private String code;

    private String desc;

    CheckStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据code获取枚举值
     * @param code
     * @return
     */
    public static CheckStatusEnum getByCode(String code) {
        for (CheckStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
