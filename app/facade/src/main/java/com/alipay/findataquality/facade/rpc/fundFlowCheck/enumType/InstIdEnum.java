package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

import com.alipay.sofa.common.utils.StringUtil;

/**
 * @ClassName InstTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 18:47
 * @Version V1.0
 **/
public enum InstIdEnum {
    /**
     * 余额宝主体类型
     * 第一列为financingcore#instId类型
     * 第二列为financingcore#instId描述
     * 第三列为financingcore#masterInstId类型
     * 第四列为financingcore#masterInstId描述
     */
    MYBANK("MYBANK","网商","MYBANK","网商"),
    SHUMIJJ("SHUMIJJ","数米","SHUMIJJ","数米"),
    THFUND("3008","天弘","3008","天弘"),
    THCFUND("THCFUND","天弘第二直销","3008","天弘"),

    /**
     * 兜底主体类型
     */
    OTHER("OTHER","其他","OTHER","其他");

    //financingcore流入、流出渠道code
    private String fcInstCode;

    //financingcore流入、流出渠道中文描述
    private String fcInstDesc;
    private String masterInstCode;
    private String masterInstDesc;

    InstIdEnum(String fcInstCode,String fcInstDesc,String masterInstCode,String masterInstDesc) {
        this.fcInstCode = fcInstCode;
        this.fcInstDesc = fcInstDesc;
        this.masterInstCode = masterInstCode;
        this.masterInstDesc = masterInstDesc;
    }

    public static InstIdEnum getByFcInstCode(String code){
        if(StringUtil.isBlank(code)){
            return null;
        }
        for (InstIdEnum instId: values()
             ) {
            if(instId.getFcInstCode().equals(code)){
                return instId;
            }
        }
        return null;
    }

    public static InstIdEnum getByMasterInstCode(String code){
        if(StringUtil.isBlank(code)){
            return null;
        }
        for (InstIdEnum instId: values()
        ) {
            if(instId.getMasterInstCode().equals(code)){
                return instId;
            }
        }
        return null;
    }

    public String getFcInstCode() {
        return fcInstCode;
    }

    public void setFcInstCode(String fcInstCode) {
        this.fcInstCode = fcInstCode;
    }

    public String getFcInstDesc() {
        return fcInstDesc;
    }

    public void setFcInstDesc(String fcInstDesc) {
        this.fcInstDesc = fcInstDesc;
    }

    public String getMasterInstDesc() {
        return masterInstDesc;
    }

    public void setMasterInstDesc(String masterInstDesc) {
        this.masterInstDesc = masterInstDesc;
    }

    public String getMasterInstCode() {
        return masterInstCode;
    }

    public void setMasterInstCode(String masterInstCode) {
        this.masterInstCode = masterInstCode;
    }
}
