package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.request;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.BaseRequest;

/**
 * @ClassName FetchDataRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/21 14:46
 * @Version V1.0
 **/
public class FetchDataRequest extends BaseRequest {
    /**
     * 场景码
     */
    private String sceneCode;
    /**
     *  入口单号值
     */
    private String intputValue;
    /**
     * 取数环境
     */
    private String env;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作人id
     */
    private String operatorId;

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getIntputValue() {
        return intputValue;
    }

    public void setIntputValue(String intputValue) {
        this.intputValue = intputValue;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }
}
