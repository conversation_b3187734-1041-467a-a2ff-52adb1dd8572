package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.iwallet.biz.common.util.money.Money;

import java.util.List;
import java.util.Map;

/**
 * 交易阶段场景所组合得到的组合场景
 */
public class TransactionRuleCheckResult {

    String rule;

    boolean ruleCheckResult=false;

    String ruleCheckResultType;

    String ruleCheckMsg;

    FundFlowStageEnum fundFlowStageEnum;

    List<CriteriaCheckData> criteriaCheckDatas;

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public boolean isRuleCheckResult() {
        return ruleCheckResult;
    }

    public void setRuleCheckResult(boolean ruleCheckResult) {
        this.ruleCheckResult = ruleCheckResult;
    }

    public String getRuleCheckMsg() {
        return ruleCheckMsg;
    }

    public void setRuleCheckMsg(String ruleCheckMsg) {
        this.ruleCheckMsg = ruleCheckMsg;
    }

    public FundFlowStageEnum getFundFlowStageEnum() {
        return fundFlowStageEnum;
    }

    public void setFundFlowStageEnum(FundFlowStageEnum fundFlowStageEnum) {
        this.fundFlowStageEnum = fundFlowStageEnum;
    }

    public String getRuleCheckResultType() {
        return ruleCheckResultType;
    }

    public void setRuleCheckResultType(String ruleCheckResultType) {
        this.ruleCheckResultType = ruleCheckResultType;
    }

    public List<CriteriaCheckData> getCriteriaCheckDatas() {
        return criteriaCheckDatas;
    }

    public void setCriteriaCheckDatas(List<CriteriaCheckData> criteriaCheckDatas) {
        this.criteriaCheckDatas = criteriaCheckDatas;
    }
}
