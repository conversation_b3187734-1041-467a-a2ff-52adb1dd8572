package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums;

/**
 * @ClassName QueryOutEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 17:21
 * @Version V1.0
 **/
public enum OutTypeEnum {
    //单值类型
    SINGLE("single ","单值"),
    //列表值类型，后续放置会覆盖前置值
    ARRAY("array","数组值"),
    //数组支持追加，后续放置会在前置值结尾追加
    ARRAY_APPEND("array_append","数组值支持追加");

    private final String code;
    private final String desc;

    OutTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
