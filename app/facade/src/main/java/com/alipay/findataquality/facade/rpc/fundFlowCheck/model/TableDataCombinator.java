package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName TableDataCombinator
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/6 16:41
 * @Version V1.0
 **/
public class TableDataCombinator {
    private List<String> columnName = new ArrayList<>();
    private List<String> columnValue = new ArrayList<>();
    private TableData tableData;
    private int rowIndex;
    private boolean used = false;
    private String md5;

    public TableDataCombinator(TableData tableData){
        this.tableData = tableData;
    }

    /**
     * 添加字段
     * @param columnName
     * @param columnValue
     * @param rowIndex
     */
    public void addColumnValue(String columnName,String columnValue, int rowIndex){
        this.columnName.add(columnName);
        this.columnValue.add(columnValue);
        this.rowIndex = rowIndex;
    }

    /**
     * 更新数据value合并在一起的md5值
     */
    public void calculateMd5(){
        if(columnValue.isEmpty()){
            return;
        }
        StringBuilder sb = new StringBuilder();
        for (String value: columnValue) {
            //对于获取到的为NULL的设置为特殊字符，也用于比对
            String newValue = value == null ? "____@@@@NULL@@@@____" : value;
            sb.append(StrUtil.calculateMD5(newValue));
        }
        this.setMd5(StrUtil.calculateMD5(sb.toString()));
    }

    public List<String> getColumnName() {
        return columnName;
    }

    public void setColumnName(List<String> columnName) {
        this.columnName = columnName;
    }

    public List<String> getColumnValue() {
        return columnValue;
    }

    public void setColumnValue(List<String> columnValue) {
        this.columnValue = columnValue;
    }

    public TableData getTableData() {
        return tableData;
    }

    public void setTableData(TableData tableData) {
        this.tableData = tableData;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public boolean isUsed() {
        return used;
    }

    public void setUsed(boolean used) {
        this.used = used;
    }
}
