package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

/**
 * @ClassName GraphEdge
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 13:09
 * @Version V1.0
 **/
public class GraphEdge {
    private String source;
    private String target;
    private String label;
    private String extInfo;

    public GraphEdge(String source, String target, String label) {
        this.source = source;
        this.target = target;
        this.label = label;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}
