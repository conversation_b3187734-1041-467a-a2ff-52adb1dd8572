package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

/**
 * @ClassName DataCoordinate
 * @Description 校验数据坐标
 * <AUTHOR>
 * @Date 2025/4/6 11:17
 * @Version V1.0
 **/
public class DataCoordinate {
    //数据库
    private String dbName;
    //数据表
    private String tableName;
    //字段名
    private String columnName;
    //字段值所属行，从1开始
    private int valueIndex;
    //字段值
    private String columnValue;

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public int getValueIndex() {
        return valueIndex;
    }

    public void setValueIndex(int valueIndex) {
        this.valueIndex = valueIndex;
    }

    public String getColumnValue() {
        return columnValue;
    }

    public void setColumnValue(String columnValue) {
        this.columnValue = columnValue;
    }
}
