package com.alipay.findataquality.facade.rpc.fundFlowCheck.result;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 资金模型校验返回值
 */
public class ModelCheckResult extends RuleCheckResult{

    private static final Logger logger =  LoggerFactory.getLogger(ModelCheckResult.class);

    List<TransactionGroup> transactionGroupList;

    AssetModelNode assetModelNode;

    String assetModelCheckType;
    /**
     * 转换为端上透出值结构
     */
    private List<GraphNode> nodeList = new ArrayList<>();
    private List<GraphEdge> edgeList = new ArrayList<>();


    /**
     * 初始化
     * @param checkStatus
     * @param checkDetail
     */
    public ModelCheckResult(CheckStatusEnum checkStatus, String checkDetail, List<TransactionGroup> transactionGroupList, AssetModelNode assetModelNode) {
        super(checkStatus, checkDetail, null, null);
        this.transactionGroupList = transactionGroupList;
        this.assetModelNode = assetModelNode;
        //总图、分图检测模型的绘图初始化
        this.covertTransactionGroupListToGraph(checkStatus);
        //单节点检测模型的绘图初始化
        this.covertAssetModelNodeToGraph(checkStatus);
    }


    /**
     * 基于assetModelNode的绘图:用于单节点的资金模型检测绘图结果
     */
    public void covertAssetModelNodeToGraph(CheckStatusEnum checkStatus){
        if (this.assetModelNode==null){
            return;
        }
        //设置自身节点
        AssetOperation selfAssetOperation = this.assetModelNode.getSelfAssetOperation();
        GraphNode node = new GraphNode(selfAssetOperation.getGraphUniqueId(),getPayToolLabel(selfAssetOperation),"");
        node.setColorForNode(selfAssetOperation,checkStatus);
        this.nodeList.add(node);

        //设置流入边
        List<AssetOperation> inFlows = this.assetModelNode.getInFlows();
        for (AssetOperation inAssetOperation : inFlows){
            //设置节点：
            GraphNode inNode = new GraphNode(inAssetOperation.getGraphUniqueId(),getPayToolLabel(inAssetOperation),"");
            inNode.setColorForNode(inAssetOperation,CheckStatusEnum.OTHERS);
            this.nodeList.add(inNode);
            //设置边
            GraphEdge edge = createEdgeFromAssetModel(inAssetOperation,selfAssetOperation,AssetActionTypeEnum.INCREASE);
            if(edge==null){
                logger.warn("covertTransactionToGraph绘制流入渠道出现空边，跳过处理, 资金操作={}",selfAssetOperation);
                continue;
            }
            edgeList.add(edge);
        }
        //设置流出边
        List<AssetOperation> outFlows = this.assetModelNode.getOutFlows();
        for (AssetOperation outAssetOperation : outFlows){
            //设置节点：
            GraphNode outNode = new GraphNode(outAssetOperation.getGraphUniqueId(),getPayToolLabel(outAssetOperation),"");
            outNode.setColorForNode(outAssetOperation,CheckStatusEnum.OTHERS);
            this.nodeList.add(outNode);
            //设置边
            GraphEdge edge = createEdgeFromAssetModel(selfAssetOperation,outAssetOperation,AssetActionTypeEnum.DECREASE);
            if(edge==null){
                logger.warn("covertTransactionToGraph绘制流出渠道出现空边，跳过处理, 资金操作={}",selfAssetOperation);
                continue;
            }
            edgeList.add(edge);
        }
        //设置冻结边
        List<AssetOperation> freezeFlows = this.assetModelNode.getFreezeSingleOps();
        for (AssetOperation freezeAssetOperation : freezeFlows){
            //设置节点：
            GraphNode freezeNode = new GraphNode(freezeAssetOperation.getGraphUniqueId(),getPayToolLabel(freezeAssetOperation),"");
            freezeNode.setColorForNode(freezeAssetOperation,CheckStatusEnum.OTHERS);
            this.nodeList.add(freezeNode);
            //设置边
            GraphEdge edge = createEdgeFromAssetModel(freezeAssetOperation,freezeAssetOperation,AssetActionTypeEnum.FREEZE);
            if(edge==null){
                logger.warn("covertTransactionToGraph绘制冻结渠道出现空边，跳过处理, 资金操作={}",selfAssetOperation);
                continue;
            }
            edgeList.add(edge);
        }
        //设置解冻边
        List<AssetOperation> unfreezeFlows = this.assetModelNode.getUnfreezeSingleOps();
        for (AssetOperation unfreezeAssetOperation : unfreezeFlows){
            //设置节点：
            GraphNode unfreezeNode = new GraphNode(unfreezeAssetOperation.getGraphUniqueId(),getPayToolLabel(unfreezeAssetOperation),"");
            unfreezeNode.setColorForNode(unfreezeAssetOperation,CheckStatusEnum.OTHERS);
            this.nodeList.add(unfreezeNode);
            //设置边
            GraphEdge edge = createEdgeFromAssetModel(unfreezeAssetOperation,unfreezeAssetOperation,AssetActionTypeEnum.UNFREEZE);
            if(edge==null){
                logger.warn("covertTransactionToGraph绘制解冻渠道出现空边，跳过处理, 资金操作={}",selfAssetOperation);
                continue;
            }
            edgeList.add(edge);
        }
        //优化nodeList使其保持unique
        Map<String, GraphNode> nodeMap = new LinkedHashMap<>();  // 使用LinkedHashMap保持插入顺序
        for (GraphNode graphNode : this.nodeList) {
            nodeMap.putIfAbsent(graphNode.getId(), graphNode);  // 使用Java 8的新方法
        }
        this.nodeList = new ArrayList<>(nodeMap.values());
    }

    /**
     * 基于transactionGroupList绘边
     * @return
     */
    private GraphEdge createEdgeFromAssetModel(AssetOperation fromAssetOperation,AssetOperation toAssetOperation,AssetActionTypeEnum direction){
        if(direction.equals(AssetActionTypeEnum.FREEZE) || direction.equals(AssetActionTypeEnum.UNFREEZE) ){
            String label;
            if (fromAssetOperation.getActionType()!=null){
                label = fromAssetOperation.getActionType().getDesc();
            }else{
                label = fromAssetOperation.getActionType().getDesc()+
                        fromAssetOperation.getAmount()+"元"; //label金额取冻结/解冻的金额：fromAssetOperation=toAssetOperation
            }
            return new GraphEdge(fromAssetOperation.getGraphUniqueId(),toAssetOperation.getGraphUniqueId(),label);
        }else if (direction.equals(AssetActionTypeEnum.INCREASE)){
            String label = fromAssetOperation.getAmount()+"元"; //label金额取流入渠道的金额
            return new GraphEdge(fromAssetOperation.getGraphUniqueId(),toAssetOperation.getGraphUniqueId(),label);
        }else if (direction.equals(AssetActionTypeEnum.DECREASE)){
            String label = toAssetOperation.getAmount()+"元"; //label金额取流出渠道的金额
            return new GraphEdge(fromAssetOperation.getGraphUniqueId(),toAssetOperation.getGraphUniqueId(),label);
        }
        return null;
    }

    /**
     * 基于transactionGroupList的绘图:用于总图和分图的资金模型检测绘图结果
     */
    public void covertTransactionGroupListToGraph(CheckStatusEnum checkStatus){
        if (this.transactionGroupList==null || this.transactionGroupList.size()==0){
            return;
        }
        //更新每个资金group对应的画图展示
        for (TransactionGroup group:this.transactionGroupList) {
            group.covertTransactionToGraph();
            Map<String,Boolean> transactionMap = new HashMap<>();
            List<AssetTransaction> transactions = group.getTransactionList();
            for (int i=0; i<transactions.size(); i++) {
                AssetTransaction current = transactions.get(i);
                GraphEdge edge = createEdgeFromTransaction(current,i+1);
                if(edge==null){
                    logger.warn("covertTransactionToGraph出现空边，跳过处理, 资金操作={}",current);
                    continue;
                }
                edgeList.add(edge);
                List<AssetOperation> list =  current.getOperationList();
                list.stream().forEach(op->{
                    if(op!=null){
                        if(!transactionMap.containsKey(op.getGraphUniqueId())){
                            GraphNode node = new GraphNode(op.getGraphUniqueId(),getPayToolLabel(op),"");
                            node.setColorForNode(op,checkStatus);
                            this.nodeList.add(node);
                            transactionMap.put(op.getGraphUniqueId(),true);
                        }
                    }
                });
            }
        }
        //优化nodeList使其保持unique
        Map<String, GraphNode> nodeMap = new LinkedHashMap<>();  // 使用LinkedHashMap保持插入顺序
        for (GraphNode graphNode : this.nodeList) {
            nodeMap.putIfAbsent(graphNode.getId(), graphNode);  // 使用Java 8的新方法
        }
        this.nodeList = new ArrayList<>(nodeMap.values());
    }

    /**
     * 获取边的Label描述
     * @param op
     * @return
     */
    private String getPayToolLabel(AssetOperation op){
        //余额宝支付工具展示主体信息
        if (AssetPayToolEnum.YEB.equals(op.getPayToolType())) {
            String masterInstId = null;
            if(op.getCurrentMasterInstId()!=null){
                masterInstId = "("+op.getCurrentMasterInstId().getMasterInstDesc()+")";
            }
            return op.getPayToolType().getDesc()+masterInstId;
        }
        return op.getPayToolType().getDesc();
    }

    /**
     * 基于transactionGroupList绘边
     * @param transaction
     * @param step
     * @return
     */
    private GraphEdge createEdgeFromTransaction(AssetTransaction transaction, int step){
        if(transaction==null){
            return null;
        }
        if(transaction.isSingleOpType()){
            String label = "("+step+")"+""+transaction.getSingleOperation().getActionType().getDesc()+
                    transaction.getSingleOperation().getAmount()+"元";
            return new GraphEdge(transaction.getSingleOperation().getGraphUniqueId(),transaction.getSingleOperation().getGraphUniqueId(),label);
        }else{
            String label = "("+step+")"+""+transaction.getAmount()+"元";
            return new GraphEdge(transaction.getFrom().getGraphUniqueId(),transaction.getTo().getGraphUniqueId(),label);
        }
    }

    /**
     * 将结果以String类型输出
     * @return
     */
    public String getPrintStr(){
        StringBuilder sb = new StringBuilder();
        sb.append("assetModeCheckType:"+this.getAssetModelCheckType()+"\n"+
                "checkScene:"+this.getCheckScene()+"\n"+
                "checkSceneName:"+this.getCheckSceneName()+"\n"+
                "checkMethod:"+this.getCheckMethod()+"\n"+
                "ruleDesc:"+this.getRuleDesc()+"\n"+
                "checkStatus:"+this.getCheckStatus()+"\n"+
                "checkDetail:"+this.getCheckDetail()+"\n"+
                "extInfo:"+this.getExtInfo()+"\n"
        );
        return sb.toString();
    }

    /**
     * 放入关联表扩展信息
     * @param key
     * @param data
     */
    public void putRelatedTableExt(String key, AssetRecordData data){
        if (data == null) {
            return;
        }
        Map<String,Integer> tableCntMap = data.findUniqueTableCntMap(true);
        // 解析extInfo字符串为JSONObject
        JSONObject jsonObject = JSON.parseObject(this.getExtInfo());
        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }
        // 获取已有的relatedTables类，如果不存在则创建一个新的
        JSONObject relatedTablesObj = jsonObject.getJSONObject(key);
        if (relatedTablesObj == null) {
            relatedTablesObj = new JSONObject();
        }
        relatedTablesObj.putAll(tableCntMap);

        // 更新JSONObject中的relatedTables
        jsonObject.put(key, relatedTablesObj);
        this.setExtInfo(jsonObject.toJSONString());
    }

    public String getAssetModelCheckType() {
        return assetModelCheckType;
    }

    public void setAssetModelCheckType(String assetModelCheckType) {
        this.assetModelCheckType = assetModelCheckType;
    }

    public List<TransactionGroup> getTransactionGroupList() {
        return transactionGroupList;
    }

    public void setTransactionGroupList(List<TransactionGroup> transactionGroupList) {
        this.transactionGroupList = transactionGroupList;
    }

    public AssetModelNode getAssetModelNode() {
        return assetModelNode;
    }

    public void setAssetModelNode(AssetModelNode assetModelNode) {
        this.assetModelNode = assetModelNode;
    }

    public List<GraphNode> getNodeList() {
        return nodeList;
    }

    public void setNodeList(List<GraphNode> nodeList) {
        this.nodeList = nodeList;
    }

    public List<GraphEdge> getEdgeList() {
        return edgeList;
    }

    public void setEdgeList(List<GraphEdge> edgeList) {
        this.edgeList = edgeList;
    }

}
