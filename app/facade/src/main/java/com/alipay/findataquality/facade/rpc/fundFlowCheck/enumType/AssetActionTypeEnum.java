package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName AssetOperateType
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:32
 * @Version V1.0
 **/
public enum AssetActionTypeEnum {
    INCREASE("INCREASE","增"),
    DECREASE("DECREASE","减"),
    FREEZE("FREEZE","冻结"),
    UNFREEZE("UNFREEZE","解冻");

    private String code;
    private String desc;
    AssetActionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
