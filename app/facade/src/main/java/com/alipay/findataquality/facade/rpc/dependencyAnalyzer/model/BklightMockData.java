package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model;

/**
 * BKlight自动化mock后的结果
 */
public class BklightMockData {
    /**
     * 重点字段：规则ID
     */
    public String rule_id;
    /**
     * Case类型
     */
    public String case_type;
    /**
     * 重点字段：Case ID
     */
    public String case_id;
    /**
     * 重点字段：基准实例ID
     */
    public String standard_instance_id;
    /**
     * 上游依赖节点ID
     */
    public String upper_node_id;
    /**
     * 上游依赖节点所属应用
     */
    public String upper_node_app;
    /**
     * 上游依赖节点名称
     */
    public String upper_node_name;
    /**
     * 重点字段：下游异常注入类型
     */
    public String down_node_inject_type;
    /**
     * 联调分组
     */
    public String group_id;
    /**
     * 上游依赖节点监控规则
     */
    public String upper_node_monitor_rule;
    /**
     * 重点字段：下游节点ID
     */
    public String down_node_id;
    /**
     * 重点字段：下游节点名称
     */
    public String down_node_service_name;
    /**
     * 重点字段：下游节点应用名称
     */
    public String down_node_app_name;
    /**
     * 重点字段：下游异常注入规则
     */
    public String down_inject_rule;
    /**
     * 重点字段：下游异常注入类型v2
     */
    public String down_inject_type_v2;
    /**
     * 重点字段：下游异常规则状态
     */
    public String down_error_rule_status;
    /**
     * 下游规则命中实例ID
     */
    public String down_rule_hit_instance_id;
    /**
     * 依赖节点初始入参
     */
    public String dependency_node_init_param;
    /**
     * 依赖节点规则注入入参
     */
    public String dependency_node_rule_inject_param;
    /**
     * 依赖节点初始返回值
     */
    public String dependency_node_init_response;
    /**
     * 依赖节点规则注入返回值
     */
    public String dependency_node_rule_inject_response;
    /**
     * 依赖节点初始异常信息
     */
    public String dependency_node_init_error_info;
    /**
     * 依赖节点规则注入异常信息
     */
    public String dependency_node_rule_inject_error_info;
    /**
     * 实例执行上下文信息
     */
    public String instance_exec_context;
    /**
     * 重点字段：命中实例节点返回值
     */
    public String hit_instance_node_response;

    public String getRule_id() {
        return rule_id;
    }

    public void setRule_id(String rule_id) {
        this.rule_id = rule_id;
    }

    public String getCase_type() {
        return case_type;
    }

    public void setCase_type(String case_type) {
        this.case_type = case_type;
    }

    public String getCase_id() {
        return case_id;
    }

    public void setCase_id(String case_id) {
        this.case_id = case_id;
    }

    public String getStandard_instance_id() {
        return standard_instance_id;
    }

    public void setStandard_instance_id(String standard_instance_id) {
        this.standard_instance_id = standard_instance_id;
    }

    public String getUpper_node_id() {
        return upper_node_id;
    }

    public void setUpper_node_id(String upper_node_id) {
        this.upper_node_id = upper_node_id;
    }

    public String getUpper_node_app() {
        return upper_node_app;
    }

    public void setUpper_node_app(String upper_node_app) {
        this.upper_node_app = upper_node_app;
    }

    public String getUpper_node_name() {
        return upper_node_name;
    }

    public void setUpper_node_name(String upper_node_name) {
        this.upper_node_name = upper_node_name;
    }

    public String getDown_node_inject_type() {
        return down_node_inject_type;
    }

    public void setDown_node_inject_type(String down_node_inject_type) {
        this.down_node_inject_type = down_node_inject_type;
    }

    public String getGroup_id() {
        return group_id;
    }

    public void setGroup_id(String group_id) {
        this.group_id = group_id;
    }

    public String getUpper_node_monitor_rule() {
        return upper_node_monitor_rule;
    }

    public void setUpper_node_monitor_rule(String upper_node_monitor_rule) {
        this.upper_node_monitor_rule = upper_node_monitor_rule;
    }

    public String getDown_node_id() {
        return down_node_id;
    }

    public void setDown_node_id(String down_node_id) {
        this.down_node_id = down_node_id;
    }

    public String getDown_node_service_name() {
        return down_node_service_name;
    }

    public void setDown_node_service_name(String down_node_service_name) {
        this.down_node_service_name = down_node_service_name;
    }

    public String getDown_node_app_name() {
        return down_node_app_name;
    }

    public void setDown_node_app_name(String down_node_app_name) {
        this.down_node_app_name = down_node_app_name;
    }

    public String getDown_inject_rule() {
        return down_inject_rule;
    }

    public void setDown_inject_rule(String down_inject_rule) {
        this.down_inject_rule = down_inject_rule;
    }

    public String getDown_inject_type_v2() {
        return down_inject_type_v2;
    }

    public void setDown_inject_type_v2(String down_inject_type_v2) {
        this.down_inject_type_v2 = down_inject_type_v2;
    }

    public String getDown_error_rule_status() {
        return down_error_rule_status;
    }

    public void setDown_error_rule_status(String down_error_rule_status) {
        this.down_error_rule_status = down_error_rule_status;
    }

    public String getDown_rule_hit_instance_id() {
        return down_rule_hit_instance_id;
    }

    public void setDown_rule_hit_instance_id(String down_rule_hit_instance_id) {
        this.down_rule_hit_instance_id = down_rule_hit_instance_id;
    }

    public String getDependency_node_init_param() {
        return dependency_node_init_param;
    }

    public void setDependency_node_init_param(String dependency_node_init_param) {
        this.dependency_node_init_param = dependency_node_init_param;
    }

    public String getDependency_node_rule_inject_param() {
        return dependency_node_rule_inject_param;
    }

    public void setDependency_node_rule_inject_param(String dependency_node_rule_inject_param) {
        this.dependency_node_rule_inject_param = dependency_node_rule_inject_param;
    }

    public String getDependency_node_init_response() {
        return dependency_node_init_response;
    }

    public void setDependency_node_init_response(String dependency_node_init_response) {
        this.dependency_node_init_response = dependency_node_init_response;
    }

    public String getDependency_node_rule_inject_response() {
        return dependency_node_rule_inject_response;
    }

    public void setDependency_node_rule_inject_response(String dependency_node_rule_inject_response) {
        this.dependency_node_rule_inject_response = dependency_node_rule_inject_response;
    }

    public String getDependency_node_init_error_info() {
        return dependency_node_init_error_info;
    }

    public void setDependency_node_init_error_info(String dependency_node_init_error_info) {
        this.dependency_node_init_error_info = dependency_node_init_error_info;
    }

    public String getDependency_node_rule_inject_error_info() {
        return dependency_node_rule_inject_error_info;
    }

    public void setDependency_node_rule_inject_error_info(String dependency_node_rule_inject_error_info) {
        this.dependency_node_rule_inject_error_info = dependency_node_rule_inject_error_info;
    }

    public String getInstance_exec_context() {
        return instance_exec_context;
    }

    public void setInstance_exec_context(String instance_exec_context) {
        this.instance_exec_context = instance_exec_context;
    }

    public String getHit_instance_node_response() {
        return hit_instance_node_response;
    }

    public void setHit_instance_node_response(String hit_instance_node_response) {
        this.hit_instance_node_response = hit_instance_node_response;
    }
}
