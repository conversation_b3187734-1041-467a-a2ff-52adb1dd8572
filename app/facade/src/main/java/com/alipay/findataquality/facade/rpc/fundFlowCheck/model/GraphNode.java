package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.ColorTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;

/**
 * @ClassName GraphNode
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/5 13:09
 * @Version V1.0
 **/
public class GraphNode {
    private String id;
    private String label;
    private String extInfo;

    public GraphNode(String id, String label,String extInfo){
        this.id = id;
        this.label = label;
        this.extInfo = extInfo;
    }

    public void setColorByOp(AssetOperation op){
        if(op==null){
            return;
        }
        if(AssetPayToolEnum.UNKNOWN.equals(op.getPayToolType())){
            this.setColor(ColorTypeEnum.RED);
        }
//
//        //mock下余额宝节点颜色
//        if(AssetPayToolEnum.YEB.equals(op.getPayToolType())){
//            this.setColor(ColorTypeEnum.RED);
//        }
    }

    public void setColorForNode(AssetOperation op, CheckStatusEnum checkStatusEnum){
        if(op==null){
            return;
        }
        switch (checkStatusEnum){
            case IGNORE:
                this.setColor(ColorTypeEnum.BLACK);
                break;
            case SUCCESS:
                this.setColor(ColorTypeEnum.GREEN);
                break;
            case FAIL:
                this.setColor(ColorTypeEnum.RED);
                break;
            default:
                this.setColor(ColorTypeEnum.BLACK);
                break;
        }
        if(AssetPayToolEnum.UNKNOWN.equals(op.getPayToolType())){
            this.setColor(ColorTypeEnum.RED);
        }
    }

    public void setColor(ColorTypeEnum color){
        putExtInfo("nodeColor", color.getColorCode());
    }

    public void putExtInfo(String key, String value){
        extInfo = StrUtil.putJsonValue(extInfo, key, value);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}
