package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model;

import com.alipay.findataquality.facade.result.CommonResult;

import java.util.*;

/**
 * @ClassName QueryTaskResult
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 16:22
 * @Version V1.0
 **/
public class QueryTaskResult extends CommonResult {
    private List<TableData> tableData = new ArrayList<>();
    private List<TableData> keyData = new ArrayList<>();
    private String traceIds;
    private String extInfo;
    private String keyDataSummary;

    /** 记录的主键id */
    private String id;
    /** 记录的shareCode */
    private String shareCode;

    /**
     * 获取总共执行的SQL数
     * @return
     */
    public int getTotalSqlCnt(){
        return tableData.size();
    }
    /**
     * 获取总共查询到的数据行数
     * @return
     */
    public int getTotalRowCnt(){
        int total = 0;
        for (TableData data : tableData) {
            total += data.getColumnData().size();
        }
        return total;
    }

    public String getTotalRowSumStr(){
        StringBuilder sb = new StringBuilder();
        Map<String,Integer> sortMap = new TreeMap<>();
        for (TableData data : tableData) {
            String fullPath = data.getDbName()+"."+data.getTableName();
            if(sortMap.containsKey(fullPath)){
                sortMap.put(fullPath,sortMap.get(fullPath)+data.getColumnData().size());
            }else{
                sortMap.put(fullPath,data.getColumnData().size());
            }
        }
        if(sortMap.isEmpty()){
            return null;
        }
        Set<String>keys = sortMap.keySet();
        for (String key:keys){
            sb.append(key+"={"+sortMap.get(key)+"}\n");
        }
        return sb.toString();
    }

    public List<TableData> getTableData() {
        return tableData;
    }

    public void setTableData(List<TableData> tableData) {
        this.tableData = tableData;
    }

    public List<TableData> getKeyData() {
        return keyData;
    }

    public void setKeyData(List<TableData> keyData) {
        this.keyData = keyData;
    }

    public String getKeyDataSummary() {
        return keyDataSummary;
    }

    public void setKeyDataSummary(String keyDataSummary) {
        this.keyDataSummary = keyDataSummary;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public String getTraceIds() {
        return traceIds;
    }

    public void setTraceIds(String traceIds) {
        this.traceIds = traceIds;
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }
}
