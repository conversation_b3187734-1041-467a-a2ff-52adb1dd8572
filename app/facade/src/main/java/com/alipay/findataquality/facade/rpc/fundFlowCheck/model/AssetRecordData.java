package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.ExpressionEvaluatorUtil;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;

import java.util.*;

/**
 * @ClassName AssetBaseData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:16
 * @Version V1.0
 **/
public abstract class AssetRecordData implements Cloneable{
    /**
     * 交易单，对于同一笔数据，可能同时包含多笔不同表的交易数据，如fc可同时增、减、冻结、解冻
     */
    private List<TableData> dataList;

    /**
     * 当用户使用findTable查找表时，记录历史查找过的表名
     */
    private List<String> findTableHistory = new ArrayList<>() ;

    private AssetOperationData assetOperationData = new AssetOperationData();

    public abstract String[] dataTable();

    public List<TableData> getDataList() {
        return dataList;
    }

    public void setDataList(List<TableData> dataList) {
        this.dataList = dataList;
    }

    public abstract AssetOperation analyzeAssetOperation(TableData tableData, int tableIndex, int dataIndex);

    /**
     * 返回表名和获取到的数据行数映射关系
     * @param includeFindHistory 是否包含历史查找记录
     * @return
     */
    public Map<String,Integer> findUniqueTableCntMap(boolean includeFindHistory){
        Map<String,Integer> map = new HashMap<>();
        //List<String> uniqueTables = new ArrayList<>();
        Set<String> uniqueTablesSet = new HashSet<>();
        if(dataList!=null&&!dataList.isEmpty()){
            for (TableData tableData:dataList) {
                if(map.containsKey(tableData.getTableFullPathOrig())){
                    continue;
                }
                map.put(tableData.getTableFullPathOrig(),tableData.getColumnData().size());
                uniqueTablesSet.add(tableData.getTableFullPath());
            }
        }

        if(includeFindHistory){
            for (String history:findTableHistory) {
                if(!uniqueTablesSet.contains(history)){
                    map.put(history,0);
                }
            }
        }
        return map;
    }

    /**
     * 数据克隆
     *
     * @return
     */
    public AssetRecordData clone(){
        try {
            AssetRecordData data = (AssetRecordData) super.clone();
            //深拷贝
            List<TableData> newDataList = new ArrayList<>();
            if(dataList!=null){
                for(TableData tableData:dataList){
                    TableData tableDataClone = tableData.clone();
                    newDataList.add(tableDataClone);
                }
            }
            List<String> findTableHistoryNew = new ArrayList<>(findTableHistory);
            data.setFindTableHistory(findTableHistoryNew);
            data.setDataList(newDataList);
            data.setAssetOperationData(this.assetOperationData.clone());
            return data;
        } catch (CloneNotSupportedException e) {
            //e.printStackTrace();
            return null;
        }
    }

    public void initFromDataContext(TableDataContext dataContext){
        List<TableData> tableDataList = new ArrayList<>();
        String[] tableList = this.dataTable();
        for (int i=0;i<tableList.length;i++) {
            String table = tableList[i];
            List<TableData> tableDataListInMap = dataContext.getTableDataMap().get(table);
            if(tableDataListInMap==null||tableDataListInMap.isEmpty()){
                continue;
            }
            for (TableData tableData :tableDataListInMap) {
                //可放入null值
                tableDataList.add(tableData);
                if(tableData!=null&&!tableData.getColumnData().isEmpty()){
                    //分析资金操作序列
                    for(int j=0;j<tableData.getColumnData().size();j++){
                        AssetOperation operation = analyzeAssetOperation(tableData,i,j);
                        if(operation!=null){
                            this.getAssetOperationData().getOperationList().add(operation);
                        }
                    }
                }
            }
        }
        this.dataList = tableDataList;
    }

    public AssetOperationData getAssetOperationData() {
        return assetOperationData;
    }

    public void setAssetOperationData(AssetOperationData assetOperationData) {
        this.assetOperationData = assetOperationData;
    }


    /**
     * 数据校验常用操作方法集合
     */

    /**
     * 根据库名+表名查找表数据
     * 如，传入yebapp.yeb_asset_increase_order，返回该表的数据
     * @param tableNames
     * @return
     */
    public AssetRecordData findTable(String ...tableNames){
        AssetRecordData recordClone = this.clone();
        //记录查找历史
        List<String> history = Optional.ofNullable(recordClone.getFindTableHistory())
                .orElseGet(ArrayList::new); // 如果为null则创建新集合
        if (tableNames != null) {
            Collections.addAll(history, tableNames);
        }
        recordClone.setFindTableHistory(history); // 确保后续使用非空
//        for (String tableName: tableNames) {
//            recordClone.getFindTableHistory().add(tableName);
//        }
        for (int i = 0; i < recordClone.getDataList().size(); i++) {
            boolean exist = false;
            for (String tableName: tableNames) {
                if(recordClone.getDataList().get(i).isCurrentTable(tableName)){
                    exist = true;
                    break;
                }
            }
            if(!exist){
                recordClone.getDataList().remove(i);
                i--;
            }
        }
        return recordClone;
    }

    /**
     * 查找指定条件的数据
     * @param condition
     * @return
     */
    public AssetRecordData findData(String condition){
        AssetRecordData recordClone = this.clone();
        if(!recordClone.getDataList().isEmpty()){
            for(int i=0;i<recordClone.getDataList().size();i++){
                TableData data = recordClone.dataList.get(i);
                for(int j=0;j<data.getColumnData().size();j++){
                    List<String> currentRow = data.getColumnData().get(j);
                    //判断当前数据是否满足条件
                    Map<String,Object> valueMap = new HashMap<>();
                    for(int k=0;k<currentRow.size();k++){
                        //放入变量名、变量值
                        valueMap.put(data.getColumnName().get(k),currentRow.get(k));
                    }
                    //从表字段中进行表达式处理，表字段本身不会缺失，故此处设为false
                    Boolean isMatch = ExpressionEvaluatorUtil.evaluateBooleanExpression(valueMap, condition,false,null);
                    if(!Boolean.TRUE.equals(isMatch)){
                        //不满足条件，从list中删除数据
                        data.getColumnData().remove(j);
                        data.getRowIndexList().remove(j);
                        j--;
                    }
                }
                //若过滤后，整体数据都为空，则将TableData也删除
                if(data.getColumnData().isEmpty()){
                    recordClone.getDataList().remove(i);
                    i--;
                }
            }
        }
        return recordClone;
    }

    /**
     * 对json格式的数据进行过滤
     * @param var
     * @param condition
     * @return
     */
    public AssetRecordData findJsonData(String var, String condition){
        AssetRecordData recordClone = this.clone();
        //从扩展字段中设置变量时，扩展字段格式不同，可能存在部分字段缺失，故此处对缺失的需自动补null，让表达式执行过去。该map用于记录缺失的字段，提升后续执行效率
        Map<String,String> valueSupplyMap = new HashMap<>();
        if(!recordClone.getDataList().isEmpty()){
            for(int i=0;i<recordClone.getDataList().size();i++){
                TableData data = recordClone.dataList.get(i);
                for(int j=0;j<data.getColumnData().size();j++){
                    List<String> currentRow = data.getColumnData().get(j);
                    Integer index = data.getColumnIndex(var);
                    if(index!=null){
                        String json = currentRow.get(index);
                        //判断当前数据是否满足条件
                        Map<String,Object> valueMap = StrUtil.getJsonKvMap(json);
                        //从扩展字段中设置变量时，扩展字段格式不同，可能存在部分字段缺失，故此处对缺失的需自动补null，让表达式执行过去，因而此处设为true
                        Boolean isMatch = ExpressionEvaluatorUtil.evaluateBooleanExpression(valueMap, condition,true, valueSupplyMap);
                        if(!Boolean.TRUE.equals(isMatch)){
                            //不满足条件，从list中删除数据
                            data.getColumnData().remove(j);
                            data.getRowIndexList().remove(j);
                            j--;
                        }
                    }
                }
                //若过滤后，整体数据都为空，则将TableData也删除
                if(data.getColumnData().isEmpty()){
                    recordClone.getDataList().remove(i);
                    i--;
                }
            }
        }
        return recordClone;
    }


    /**
     * 查找指定条件的变量
     * @param vars
     * @return
     */
    public AssetRecordData findVars(String ...vars){
        AssetRecordData recordClone = this.clone();
        if(!recordClone.getDataList().isEmpty()){
            for(int i=0;i<recordClone.getDataList().size();i++){
                TableData data = recordClone.dataList.get(i);
                boolean containsAll = true;
                List<String>varsList = new ArrayList<>();
                for(String var:vars){
                    if(!data.containsColumn(var)){
                        containsAll = false;
                        break;
                    }
                    varsList.add(var);
                }
                //若全部字段都能找到，则保留tableData，并设置字段列表，否则将表移除
                if(containsAll){
                    data.setColumnName(varsList);
                }else{
                    recordClone.getDataList().remove(i);
                    i--;
                }
            }
        }
        return recordClone;
    }

    /**
     * 获取校验数据坐标
     * @return
     */
    public List<DataCoordinate> getAllDataCoordinate(){
        List<DataCoordinate> dataCoordinateList = new ArrayList<>();
        if(this.dataList==null){
            return dataCoordinateList;
        }
        for (int i = 0; i < this.dataList.size(); i++) {
            TableData tableData = this.dataList.get(i);
            dataCoordinateList.addAll(tableData.getDataCoordinateList());
        }
        return dataCoordinateList;
    }

    /**
     * 带factor的sum，返回结果为sum*factor
     * @param factor
     * @return
     */
    public Integer sum(int factor){
        Integer result = this.sum();
        if(result==null){
            return null;
        }
        return result*factor;
    }

    /**
     * 数据加和，只支持int类型
     * @return
     */
    public Integer sum(){
        Integer sum = null;
        for (int i = 0; i < this.dataList.size(); i++) {
            TableData tableData = this.dataList.get(i);
            for (int j = 0; j < tableData.getColumnData().size(); j++) {
                List<String> row = tableData.getColumnData().get(j);
                for (String var: tableData.getColumnName()) {
                    Integer index = tableData.getColumnIndex(var);
                    if(index==null){
                        return null;
                    }
                    try {
                        Integer value = Integer.valueOf(row.get(index));
                        if(sum==null){
                            sum = value;
                        }else{
                            sum+=value;
                        }
                    }catch (Exception e){
                        return null;
                    }
                }
            }
        }
        return sum;
    }

    public AssetRecordData sort(String...vars){
        return this;
    }

    public AssetRecordData sortDsc(String...vars){
        return this;
    }

    /**
     * 数据加和，只支持int类型
     * @param var
     * @return
     */
//    public Integer sum(String var){
//        Integer sum = null;
//        for (int i = 0; i < this.dataList.size(); i++) {
//            TableData tableData = this.dataList.get(i);
//            for (int j = 0; j < tableData.getColumnData().size(); j++) {
//                List<String> row = tableData.getColumnData().get(j);
//                Integer index = tableData.getColumnIndex(var);
//                if(index==null){
//                    return null;
//                }
//                try {
//                    Integer value = Integer.valueOf(row.get(index));
//                    if(sum==null){
//                        sum = value;
//                    }else{
//                        sum+=value;
//                    }
//                }catch (Exception e){
//                    return null;
//                }
//            }
//        }
//        return sum;
//    }

    /**
     * 返回当前的数据条数
     * @return
     */
    public int count(){
        if(this.dataList==null){
            return 0;
        }
        return this.dataList.size();
    }

    /**
     * 根据传入的变量名，拼接成字符串，不同变量,分割，多行数据;分割
     * 如，传入inst_id,user_id，返回结果样例如下：
     * SHUMIJJ,2088xx;MYBANK,2088x
     * @param vars
     * @return
     */
    public List<String> joinVars(String ...vars){
        List<String> varsList = new ArrayList<>();
        for (int i = 0; i < this.dataList.size(); i++) {
            TableData tableData = this.dataList.get(i);
            for (int j = 0; j < tableData.getColumnData().size(); j++) {
                List<String> row = tableData.getColumnData().get(j);
                StringBuilder sb = new StringBuilder();
                for (int k=0;k<vars.length;k++) {
                    Integer index = tableData.getColumnIndex(vars[k]);
                    if(index!=null){
                        String value = row.get(index);
                        sb.append(value);
                        if(k!=vars.length-1){
                            sb.append("-");
                        }
                    }
                    else{
                        return null;
                    }
                }
                varsList.add(sb.toString());
            }
        }
        return varsList;
    }

    public boolean isEmpty(){
        if(this.dataList==null){
            return true;
        }
        int cnt= 0;
        for (int i = 0; i < this.dataList.size(); i++) {
            TableData tableData = this.dataList.get(i);
            if(tableData==null||tableData.getColumnData()==null){
                continue;
            }
            cnt+=tableData.getColumnData().size();
        }
        return cnt==0;
    }

    public List<String> getFindTableHistory() {
        return findTableHistory;
    }

    public void setFindTableHistory(List<String> findTableHistory) {
        this.findTableHistory = findTableHistory;
    }
}
