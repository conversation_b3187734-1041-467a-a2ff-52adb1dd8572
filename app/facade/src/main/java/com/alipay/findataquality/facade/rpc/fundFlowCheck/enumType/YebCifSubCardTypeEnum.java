package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName cif
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/29 14:13
 * @Version V1.0
 **/
public enum YebCifSubCardTypeEnum {

    /** 本系统理财子卡与CIF理财子卡的对应*/
    FINANCE_ACCOUNT("invest"),

    /** 本系统基金子卡与CIF理财子卡的对应*/
    FUND_ACCOUNT("fundpay"),

    /** 本系统余额宝子卡与CIF理财子卡的对应*/
    YEB_ACCOUNT("fundpay_share"),

    /** 本系统余额宝分销子卡与CIF理财子卡的对应*/
    DISTRIBUTION_ACCOUNT("distribution"),

    /** 存钱罐子卡与cif对应*/
    CQG_FUND("CQG_FUND"),

    /** 存钱罐分销子卡 */
    CQG_DISTRIBUTION("XQD_DISTRIBUTION_ACCT"),

    /** 攒钱卡子卡与cif对应*/
    ZQK_FUND("ZQK_FUND"),

    /** 攒钱卡分销子卡 */
    ZQK_DISTRIBUTION("ZQK_DISTRIBUTION"),

    /**  亲情宝 */
    QQB_TO_SUBCARD_TYPE("emotion_fund"),

    /** 天猫宝 */
    TMB_TO_SUBCARD_TYPE("tmall_fund"),

    /**  家庭宝对应子卡类型  子卡类型有CIF定义  */
    JTB_TO_SUBCARD_TYPE("family_fund"),

    /**  理财小宝对应子卡类型  子卡类型有CIF定义 */
    LCXB_TO_SUBCARD_TYPE("lcxb_fund"),

    /** 喵街对应子卡类型  子卡类型有CIF定义 */
    MERCHANT_TO_SUBCARD_TYPE("merchant_fund"),

    /** 猫超子卡(与cif对应)*/
    MCK_FUND("MCK_FUND"),

    /** 猫超分销子卡(与cif对应) */
    MCK_DISTRIBUTION("MCK_DISTRIBUTION"),

    /** 余额宝过桥卡子卡 */
    YEB_CROSS_FUND("YEB_CROSS_ACCT"),

    /** 余额宝过桥卡分销子卡 */
    YEB_CROSS_DISTRIBUTION("YEB_CROSS_DIS_ACCT"),

    /** 合花子卡(与cif对应)*/
    HH_FUND("HH_FUND"),

    /** 合花分销子卡(与cif对应) */
    HH_DISTRIBUTION("HH_DISTRIBUTION"),

    /** 存款卡虚拟子卡(与cif对应) */
    VIRTUAL_CKK_FUND("VIRTUAL_CKK_FUND_ACCT"),

    /** 存款卡虚拟分销子卡(与cif对应) */
    VIRTUAL_CKK_DISTRIBUTION("VIRTUAL_CKK_DIS_ACCT"),

    /** 存款卡子卡(与cif对应)*/
    CKK_FUND("CKK_FUND_ACCT"),

    /** 存款卡分销子卡(与cif对应) */
    CKK_DISTRIBUTION("CKK_DIS_ACCT"),

    ;

    private String code;

    YebCifSubCardTypeEnum(String code) {
        this.code = code;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return
     */
    public static YebCifSubCardTypeEnum getByCode(String code) {
        for (YebCifSubCardTypeEnum contextKey : values()) {
            if (contextKey.code.equals(code)) {
                return contextKey;
            }
        }
        return null;
    }
}
