package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.iwallet.biz.common.util.money.Money;

import java.util.List;

/**
 * 交易阶段场景所组合得到的组合场景
 */
public class TransactionComposedScene {

    String composedSceneDesc;

    List<TransactionStageScene> transactionStageSceneList;

    Money sumAmount;

    public String getComposedSceneDesc() {
        return composedSceneDesc;
    }

    public void setComposedSceneDesc(String composedSceneDesc) {
        this.composedSceneDesc = composedSceneDesc;
    }

    public List<TransactionStageScene> getTransactionStageSceneList() {
        return transactionStageSceneList;
    }

    public void setTransactionStageSceneList(List<TransactionStageScene> transactionStageSceneList) {
        this.transactionStageSceneList = transactionStageSceneList;
    }

    public Money getSumAmount() {
        return sumAmount;
    }

    public void setSumAmount(Money sumAmount) {
        this.sumAmount = sumAmount;
    }
}
