package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model;

/**
 * @ClassName QueryScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/31 13:45
 * @Version V1.0
 **/
public class QueryScene {
    //场景名称
    private String sceneName;
    //场景码
    private String sceneCode;
    //所属域
    private String domainName;
    //所属域code
    private String domainCode;
    //输入参数名
    private String inputVar;
    //输入参数提示
    private String inputTips;
    //是否支持资金分析，T/F
    private String supportFundFlow;

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getInputVar() {
        return inputVar;
    }

    public void setInputVar(String inputVar) {
        this.inputVar = inputVar;
    }

    public String getInputTips() {
        return inputTips;
    }

    public void setInputTips(String inputTips) {
        this.inputTips = inputTips;
    }

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getDomainCode() {
        return domainCode;
    }

    public void setDomainCode(String domainCode) {
        this.domainCode = domainCode;
    }

    public String getSupportFundFlow() {
        return supportFundFlow;
    }

    public void setSupportFundFlow(String supportFundFlow) {
        this.supportFundFlow = supportFundFlow;
    }
}
