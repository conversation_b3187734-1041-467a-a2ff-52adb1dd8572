package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

import com.alipay.sofa.common.utils.StringUtil;

import java.util.*;

/**
 * @ClassName AssetOperateType
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:32
 * @Version V1.0
 **/
public enum AssetPayToolEnum {
    /**
     * 余额宝支付工具
     */
    YEB("YEB",true,"0100",YebCifSubCardTypeEnum.YEB_ACCOUNT,"余额宝"),
    YEB_HH("YEB_HH",true,"1002",YebCifSubCardTypeEnum.HH_FUND,"余额宝合花子卡"),
    YEB_CQG("YEB_CQG",true,"0700",YebCifSubCardTypeEnum.CQG_FUND,"余额宝0700子卡"),
    YEB_ZQK("YEB_ZQK",true,"0800",YebCifSubCardTypeEnum.ZQK_FUND,"余额宝悄悄攒子卡"),
    YEB_CKK_VIRTUAL("YEB_CKK_VIRTUAL",true,"1003",YebCifSubCardTypeEnum.VIRTUAL_CKK_FUND,"余额宝存款卡"),
    //存款卡资金进出以虚拟主卡记录为资金总账，子卡此处设为false
    YEB_CKK("YEB_CKK",false,"1004",YebCifSubCardTypeEnum.CKK_FUND,"余额宝存款卡子卡"),
    YEB_CROSS("YEB_CROSS",true,"0900",YebCifSubCardTypeEnum.YEB_CROSS_FUND,"余额宝大额T0过桥"),
    YEB_MCK("YEB_MCK",true,"1001",YebCifSubCardTypeEnum.MCK_FUND,"余额宝猫超卡"),
    YEB_JTB("YEB_JTB",true,"0400",YebCifSubCardTypeEnum.JTB_TO_SUBCARD_TYPE,"余额宝家庭宝子卡"),
    YEB_MERCHANT("YEB_MERCHANT",true,"0600",YebCifSubCardTypeEnum.MERCHANT_TO_SUBCARD_TYPE,"余额宝喵街卡"),

    YUE("YUE",true,"ALIPAYACCOUNT",null,"余额"),
    YUE_HONEYPAYWALLET("YUE_HONEYPAYWALLET",true,"HONEYPAYWALLET",null,"余额托管子户(零花钱)"),
    //合花相关场景使用
    YUE_WE_DEP_ACT("YUE_WE_DEP_ACT",true,"WE_DEP_ACT",null,"余额托管子户"),
    YUE_WE_MYBANK_DEP_ACT("YUE_WE_MYBANK_DEP_ACT",true,"WE_MYBANK_DEP_ACT",null,"网商托管子户"),
    //余额非主卡类型，兜底使用
    YUE_COMMON_DEP_ACT("YUE_COMMON_DEP_ACT",true,"YUE_COMMON_DEP_ACT",null,"余额托管子户"),
    YUE_FUND_B("YUE_FUND_B",true,"ALIPAYACCOUNT",null,"基金泛金融余额户"),

    //通用泛金融余额户，在余额宝消费场景中使用，资金流余额宝->泛金融余额户，特点是281开头
    YUE_COMMON_B("YUE_COMMON_B",true,"ALIPAYACCOUNT",null,"泛金融余额户"),

    /**
     * 差错户
     */
    YUE_FUND_SLIP_INNER_ACCOUNT("YUE_FUND_SLIP_INNER_ACCOUNT",true,"ALIPAYACCOUNT",null,"基金专用差错户"),
    YUE_COMMON_SLIP_INNER_ACCOUNT("YUE_COMMON_SLIP_INNER_ACCOUNT",true,"ALIPAYACCOUNT",null,"支付宝通用差错户"),

    /**
     * 银联外部机构
     * 在合花子卡场景中使用，合花子卡消费，资金流为：合花子卡->泛金融余额户->银联外部机构
     */
    OUTSIDE_INST_CUP("OUTSIDE_INST_CUP",true,"OUTSIDE_INST_CUP",null,"外部银联机构"),

    CARD("CARD",true,"CARD",null,"银行卡"),
    YEB_COUPON_B("YEB_COUPON_B",true,"YEB_COUPON_B",null,"红包B类户"),
    YEB_COUPON_B_YUE("YEB_COUPON_B_YUE",true,"ALIPAY_FINANCING_TRANS_VOUCHER",null,"红包泛金融余额户"),
    FUND_DUMMY("FUND_DUMMY",true,"FUND_DUMMY",null,"基金"),
    PRODTRANS("PRODTRANS",false,"PRODTRANS",null,"产品账"),

    //存在未知节点时，使用该枚举
    UNKNOWN("UNKNOWN",false,"UNKNOWN",null,"未知");

    private String code;
    private boolean majorAccount;
    private String productCode;
    private YebCifSubCardTypeEnum subCardTypeEnum;
    private String desc;
    AssetPayToolEnum(String code, boolean majorAccount, String productCode, YebCifSubCardTypeEnum subCardTypeEnum, String desc) {
        this.code = code;
        this.majorAccount = majorAccount;
        this.productCode = productCode;
        this.subCardTypeEnum = subCardTypeEnum;
        this.desc = desc;
    }

    /**
     * 根据子类型获取枚举
     * @param subCardCode
     * @return
     */
    public static AssetPayToolEnum getBySubCardType(String subCardCode){
        if(StringUtil.isBlank(subCardCode)){
            return null;
        }
        YebCifSubCardTypeEnum subCardType = YebCifSubCardTypeEnum.getByCode(subCardCode);
        if(subCardType==null){
            return null;
        }
        for (AssetPayToolEnum productEnum:values()
        ) {
            if(subCardType.equals(productEnum.getSubCardTypeEnum())){
                return productEnum;
            }
        }
        return null;
    }

    public boolean isMajorAccount() {
        return majorAccount;
    }

    /**
     * 根据productCode获取枚举
     * @param productCode
     * @return
     */
    public static AssetPayToolEnum getByProductCode(String productCode){
        if(StringUtil.isBlank(productCode)){
            return null;
        }
        for (AssetPayToolEnum productEnum:values()
             ) {
            if(productEnum.getProductCode().equals(productCode.toUpperCase(Locale.ROOT))){
                return productEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getProductCode() {
        return productCode;
    }

    public String getDesc() {
        return desc;
    }

    public YebCifSubCardTypeEnum getSubCardTypeEnum() {
        return subCardTypeEnum;
    }
}
