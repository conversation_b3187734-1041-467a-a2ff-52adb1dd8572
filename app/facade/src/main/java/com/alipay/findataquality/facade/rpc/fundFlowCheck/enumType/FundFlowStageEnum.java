package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * 最小可串联资金流
 */
public enum FundFlowStageEnum {
    /*************买入余额宝**************/
    BANK_TO_YEB("CARD","YEB","CARD-YEB","银行卡申购余额宝"),
    COUPON_TO_YEB("YEB_COUPON_B_YUE","YEB","YEB_COUPON_B_YUE-YEB_COUPON_B-YEB","红包奖励发放到余额宝"),
    YEB_CKK_TO_YEB("YEB_CKK_VIRTUAL","YEB","YEB_CKK_VIRTUAL-YEB","余额宝存款卡申购余额宝"),
    /*************余额宝卖出**************/
    YEB_TO_FUND_DUMMY("YEB","FUND_DUMMY","YEB-FUND_DUMMY","余额宝申购基金"),
    YEB_TO_FUND_DUMMY_HQ("YEB","FUND_DUMMY","YEB-FUND_DUMMY","余额宝申购基金(高保非基金支付链路)"),
    YEB_YUE_TO_FUND_DUMMY_HQ("YEB","FUND_DUMMY","YEB-YUE-FUND_DUMMY","余额宝申购基金(高保基金支付链路)"),
    YEB_TO_COUPON("YEB","YEB_COUPON_B_YUE","YEB-YEB_COUPON_B-YEB_COUPON_B_YUE","余额宝红包奖励发放退回"),
    YEB_TO_BANK("YEB","CARD","YEB-CARD","余额宝退款到银行卡"),
    /*************基金退回余额宝**************/
    FUND_DUMMY_TO_YEB_CANCEL("FUND_DUMMY","YEB","FUND_DUMMY-YEB","基金撤单后资金退回余额宝"),//特殊

    FUND_DUMMY_TO_YEB_TOTALREFUND_03T1("FUND_DUMMY","YEB","FUND_DUMMY-YEB","基金全部退款后资金退回余额宝(03T1)"),

    FUND_DUMMY_TO_YEB_TOTALREFUND_03T2("FUND_DUMMY","YEB","FUND_DUMMY-YEB","基金全部退款后资金退回余额宝(03T2)"),

    FUND_DUMMY_TO_YEB_PARTREFUND_03T1("FUND_DUMMY","YEB","FUND_DUMMY-YEB","基金部分退款后资金退回余额宝(03T1)"),
    FUND_DUMMY_TO_YEB_PARTREFUND_03T2("FUND_DUMMY","YEB","FUND_DUMMY-YEB","基金部分退款后资金退回余额宝(03T2)");


    private String from;
    private String to;
    private String flow;
    private String desc;


    FundFlowStageEnum(String from, String to, String flow, String desc) {
        this.from=from;
        this.to=to;
        this.flow=flow;
        this.desc=desc;
    }

    public String getFrom() {
        return from;
    }

    public String getTo() {
        return to;
    }

    public String getFlow() {
        return flow;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "{"
                + "\"from\":\""
                + from + '\"'
                + ",\"to\":\""
                + to + '\"'
                + ",\"flow\":\""
                + flow + '\"'
                + ",\"desc\":\""
                + desc + '\"'
                + "},\"super-FundFlowStageEnum\":" + super.toString() + "}";

    }
}
