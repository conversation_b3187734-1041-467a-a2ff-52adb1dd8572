package com.alipay.findataquality.facade.rpc.fundFlowCheck.result;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionComposedScene;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionGroup;

import java.util.List;

/**
 * @ClassName FullTraceDataCheckResult
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 16:14
 * @Version V1.0
 **/
public class FundFlowAnalyzeResult extends BaseResult{

    //资金流
    private List<TransactionGroup> transactionGroupList;

    //场景描述：一句话描述推测场景
    private String scenePrediction;

    //交易场景集合
    private List<TransactionComposedScene> transactionComposedSceneList;


    public List<TransactionGroup> getTransactionGroupList() {
        return transactionGroupList;
    }

    public void setTransactionGroupList(List<TransactionGroup> transactionGroupList) {
        this.transactionGroupList = transactionGroupList;
    }

    public String getScenePrediction() {
        return scenePrediction;
    }

    public void setScenePrediction(String scenePrediction) {
        this.scenePrediction = scenePrediction;
    }

    public List<TransactionComposedScene> getTransactionComposedSceneList() {
        return transactionComposedSceneList;
    }

    public void setTransactionComposedSceneList(List<TransactionComposedScene> transactionComposedSceneList) {
        this.transactionComposedSceneList = transactionComposedSceneList;
    }
}
