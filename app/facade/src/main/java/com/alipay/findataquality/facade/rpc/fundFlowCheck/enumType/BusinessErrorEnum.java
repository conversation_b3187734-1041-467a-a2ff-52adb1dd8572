package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName BusinessErrorEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 15:29
 * @Version V1.0
 **/
public enum BusinessErrorEnum {
    //业务异常
    SYSTEM_ERROR("001","系统错误"),
    //参数异常
    ILLEGAL_PARAMETER_ERROR("002","入参非法"),
    //数据库异常
    DATABASE_OPERATION_ERROR("003","数据库操作异常"),
    //未知异常
    UNKNOW_ERROR("004",  "未知异常");

    private String code;

    private String desc;

    BusinessErrorEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}