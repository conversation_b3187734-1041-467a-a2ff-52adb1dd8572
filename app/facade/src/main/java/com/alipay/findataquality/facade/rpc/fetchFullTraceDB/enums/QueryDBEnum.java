package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums;

/**
 * @ClassName QueryDBEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 15:19
 * @Version V1.0
 **/
public enum QueryDBEnum {

    /** 余额宝 */
    YEBCORE("yebcore", "余额宝主库",JdbcTypeEnum.MYSQL),

    /** 余额宝 */
    FINANCINGCORE("yebapp", "余额宝资产库",JdbcTypeEnum.MYSQL),

    /** 微账 */
    MFTRANS("mftrans", "微账库",JdbcTypeEnum.MYSQL),

    /** 分账 */
    AFTRANS("aftrans", "分账库",JdbcTypeEnum.MYSQL),

    /** 产品账 */
    PRODTRANS("prodtrans", "产品账库",JdbcTypeEnum.MYSQL),

    /** ֤会员 */
    OBCIF("obcif", "֤会员库",JdbcTypeEnum.MYSQL),

    /** ֤CIF代销协议库 */
    CUSTASSET("custasset", "cif代销协议库",JdbcTypeEnum.MYSQL),

    /** ֤finmember协议库 */
    FINMEMBER("fincif", "财富finmember库",JdbcTypeEnum.MYSQL),

    /** 基金签约库 */
    FINFUNDPROTOCOL("finfundprotocol", "基金签约库",JdbcTypeEnum.OB),
    FINFUNDPROTOCOL_Z90("finfundprotocol", "基金签约库",JdbcTypeEnum.OB),

    /**
     * 基金交易库
     */
    FINFUNDTRADE("finfundtrade","基金交易库",JdbcTypeEnum.MYSQL),

    /**
     * 基金交易fundtranscore库
     */
    FUNDTRANSCORE("fundtranscore","基金交易库",JdbcTypeEnum.MYSQL),

    /**
     * 交易账单表
     */
    FINTRADERECORD("fintraderecord","基金账单库",JdbcTypeEnum.MYSQL),

    /**
     * 基金策略库
     */
    FINSTRATEGY("finstrategy","基金策略库",JdbcTypeEnum.MYSQL),
    
    /**
     * 清算任务库
     */
    FINFUNDTASKMGR("finfundtaskmgr","清算任务库",JdbcTypeEnum.MYSQL),

    ORAPAY("orapay","orapay支付单",JdbcTypeEnum.OB),

    OB_CLOUDPAY("obcloudpay","obcloudpay支付单",JdbcTypeEnum.OB),

    ACCTRANS_LOG("acclog","acclog余额日志",JdbcTypeEnum.MYSQL),

    //红包B类户时，泛金融账户余额操作日志捞取，当前账号固定为20881021613091590156
    ACCTRANS_LOG_EP("acclog","acclog余额日志",JdbcTypeEnum.MYSQL),

    //基金赎回到差错，资金方向泛金融账户→用户余额，泛金融账户余额操作日志捞取，基金泛金融账户当前固定为29100000000015000156
    ACCTRANS_LOG_FUND_EP("acclog","acclog余额日志",JdbcTypeEnum.MYSQL),

    FINANCE_ASSET("financeasset","financeasset支付单",JdbcTypeEnum.MYSQL),

    //FA ins部署单元
    FINANCE_ASSET_INS("insfinanceasset","insfinanceasset支付单",JdbcTypeEnum.MYSQL),

    //FA ant部署单元
    FINANCE_ASSET_ANT("antfinanceasset","antfinanceasset支付单",JdbcTypeEnum.MYSQL),

    MY_INSTPAY("instpay","instpay单据",JdbcTypeEnum.MYSQL),

    /**
     * 资产账
     */
    FAASSET("faasset","资产账",JdbcTypeEnum.MYSQL),
    FAASSET_FINCOME("fincome","资产账",JdbcTypeEnum.MYSQL),
    FAASSET_FAINS("fains","资产账",JdbcTypeEnum.MYSQL);

    private String code;
    private String desc;
    private JdbcTypeEnum jdbc;

    private QueryDBEnum(String code, String desc, JdbcTypeEnum jdbc) {
        this.code = code;
        this.desc = desc;
        this.jdbc = jdbc;
    }

    public static QueryDBEnum getByCode(String code){
        for (QueryDBEnum queryDBEnum : values()) {
            if (queryDBEnum.getCode().equalsIgnoreCase(code)) {
                return queryDBEnum;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public JdbcTypeEnum getJdbc() {
        return jdbc;
    }

    public void setJdbc(JdbcTypeEnum jdbc) {
        this.jdbc = jdbc;
    }
}
