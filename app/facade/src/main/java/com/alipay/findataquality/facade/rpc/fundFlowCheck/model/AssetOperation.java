package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetPayToolEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.InstIdEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.CheckUtil;
import com.iwallet.biz.common.util.money.Money;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName AssetOperatorData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 17:42
 * @Version V1.0
 **/
public class AssetOperation implements Cloneable, Serializable {
    private static final long serialVersionUID = 2L;

    /**
     * 资金操作主体
     */
    private String userId;

    /**
     * 资金操作对手方主体
     */
    private String opUserId;

    /**
     * 资产操作账号
     */
    private String accountNo;

    /**
     * 资产操作品类
     */
    private AssetPayToolEnum payToolType;

    /**
     * 资产操作动作
     */
    private AssetActionTypeEnum actionType;
    /**
     * 资产操作金额
     */
    private Money amount;
    /**
     * 资产操作主体
     */
    private InstIdEnum instId;
    /**
     * 是否展示余额宝流入流出渠道标签
     */
    private boolean showInstIdLabel=false;

    /**
     * 余额宝当前主体
     */
    private InstIdEnum currentMasterInstId;

    //是否展示余额宝当前主体标签

    private boolean showCurrentInstIdLabel=false;

    /**
     * 操作对应的数据
     */
    private TableData tableData;

    /**
     * 操作对应的traceId
     */
    private String traceId;

    /**
     * 余额宝fc三级业务标
     */
    private String yebBizType;
    private String bizProd;
    private String bizActionType;
    private String fundSource;

    /**
     * 操作对应tableData的第几行数据
     * 从0开始
     */
    private int dataIndex;

    /**
     * 操作创建时间
     */
    private Date gmtCreate;
    /**
     * 操作修改时间
     */
    private Date gmtModify;
    /**
     * 操作业务时间
     */
    private Date bizDate;

    /**
     * 账务时间
     */
    private Date transDt;

    public AssetOperation clone() {
        try {
            return (AssetOperation) super.clone();
        } catch (CloneNotSupportedException e) {
            //e.printStackTrace();
        }
        return null;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public static AssetOperation createBaseOperation(TableData tableData, int dataIndex, String userId, String accountNo, String amount){
        if(tableData==null||tableData.getColumnData().isEmpty()){
            return null;
        }
        AssetOperation operation = new AssetOperation();
        operation.setTableData(tableData);
        operation.setDataIndex(dataIndex);
        operation.setUserId(tableData.getColumnIndexValue(userId,dataIndex));
        operation.setAccountNo(tableData.getColumnIndexValue(accountNo,dataIndex));
        operation.setAmount(CheckUtil.createYuanByCent(tableData.getColumnIndexValue(amount,dataIndex)));
        operation.setGmtCreate(CheckUtil.parseDateTime(tableData.getColumnIndexValue("gmt_create",dataIndex)));
        operation.setGmtModify(CheckUtil.parseDateTime(tableData.getColumnIndexValue("gmt_modified",dataIndex)));
        return operation;
    }

    public InstIdEnum getCurrentMasterInstId() {
        return currentMasterInstId;
    }

    public void setCurrentMasterInstId(InstIdEnum currentMasterInstId) {
        this.currentMasterInstId = currentMasterInstId;
    }

    public String getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(String opUserId) {
        this.opUserId = opUserId;
    }

    public static AssetOperation createUnknown(){
        AssetOperation operation = new AssetOperation();
        operation.setPayToolType(AssetPayToolEnum.UNKNOWN);
        //operation.set
        return operation;
    }

    public String getGraphUniqueId() {
        if(this.getPayToolType()!=AssetPayToolEnum.UNKNOWN){
            return this.getPayToolType().getCode()+"_"+this.getUserId()+"_"+this.getAccountNo();
        }
        //对于未知节点，每个未知节点均认为不同节点
        String instanceId = Integer.toHexString(System.identityHashCode(this));
        return "UNKNOWN_"+instanceId;
    }

    /**
     * 返回指定列的值，此处自动识别当前属于tableData的第几行数据
     * @param columnName
     * @return
     */
    public String getColumnValue(String columnName){
        if(!columnName.contains("#")){
            return tableData.getColumnIndexValue(columnName,dataIndex);
        }else{
            String[] array = columnName.split("#");
            String jsonData = tableData.getColumnIndexValue(array[0],dataIndex);;
            return CheckUtil.getJsonValue(jsonData,array[1],false);
        }
    }

    public Date getTransDt() {
        return transDt;
    }

    public void setTransDt(Date transDt) {
        this.transDt = transDt;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public int getDataIndex() {
        return dataIndex;
    }

    public void setDataIndex(int dataIndex) {
        this.dataIndex = dataIndex;
    }

    public AssetPayToolEnum getPayToolType() {
        return payToolType;
    }

    public void setPayToolType(AssetPayToolEnum payToolType) {
        this.payToolType = payToolType;
    }

    public AssetActionTypeEnum getActionType() {
        return actionType;
    }

    public void setActionType(AssetActionTypeEnum actionType) {
        this.actionType = actionType;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public InstIdEnum getInstId() {
        return instId;
    }

    public void setInstId(InstIdEnum instId) {
        this.instId = instId;
    }

    public TableData getTableData() {
        return tableData;
    }

    public void setTableData(TableData tableData) {
        this.tableData = tableData;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModify() {
        return gmtModify;
    }

    public void setGmtModify(Date gmtModify) {
        this.gmtModify = gmtModify;
    }

    public Date getBizDate() {
        return bizDate;
    }

    public void setBizDate(Date bizDate) {
        this.bizDate = bizDate;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getYebBizType() {
        return yebBizType;
    }

    public void setYebBizType(String yebBizType) {
        this.yebBizType = yebBizType;
    }

    public String getBizProd() {
        return bizProd;
    }

    public void setBizProd(String bizProd) {
        this.bizProd = bizProd;
    }

    public String getBizActionType() {
        return bizActionType;
    }

    public void setBizActionType(String bizActionType) {
        this.bizActionType = bizActionType;
    }

    public String getFundSource() {
        return fundSource;
    }

    public void setFundSource(String fundSource) {
        this.fundSource = fundSource;
    }

    public boolean isShowCurrentInstIdLabel() {
        return showCurrentInstIdLabel;
    }

    public void setShowCurrentInstIdLabel(boolean showCurrentInstIdLabel) {
        this.showCurrentInstIdLabel = showCurrentInstIdLabel;
    }

    public boolean isShowInstIdLabel() {
        return showInstIdLabel;
    }

    public void setShowInstIdLabel(boolean showInstIdLabel) {
        this.showInstIdLabel = showInstIdLabel;
    }
}
