package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.iwallet.biz.common.util.money.Money;

import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * 资金模型：单节点对象，包含流入流出和自身操作
 */
public class AssetModelNode implements Serializable {
    private static final long serialVersionUID = 1L;
    // 资金流入相关
    private Money inAmount = new Money(0);
    private List<AssetOperation> inFlows = new LinkedList<>();

    // 资金流出相关
    private Money outAmount = new Money(0);
    private List<AssetOperation> outFlows = new LinkedList<>();

    // 冻结操作相关
    private Money freezeAmount = new Money(0);
    private List<AssetOperation> freezeSingleOps = new LinkedList<>();

    // 解冻操作相关
    private Money unfreezeAmount = new Money(0);
    private List<AssetOperation> unfreezeSingleOps = new LinkedList<>();

    // 节点自身属性
    private AssetOperation selfAssetOperation;


    public Money getInAmount() {
        return inAmount;
    }

    public void setInAmount(Money inAmount) {
        this.inAmount = inAmount;
    }

    public List<AssetOperation> getInFlows() {
        return inFlows;
    }

    public void setInFlows(List<AssetOperation> inFlows) {
        this.inFlows = inFlows;
    }

    public Money getOutAmount() {
        return outAmount;
    }

    public void setOutAmount(Money outAmount) {
        this.outAmount = outAmount;
    }

    public List<AssetOperation> getOutFlows() {
        return outFlows;
    }

    public void setOutFlows(List<AssetOperation> outFlows) {
        this.outFlows = outFlows;
    }

    public Money getFreezeAmount() {
        return freezeAmount;
    }

    public void setFreezeAmount(Money freezeAmount) {
        this.freezeAmount = freezeAmount;
    }

    public List<AssetOperation> getFreezeSingleOps() {
        return freezeSingleOps;
    }

    public void setFreezeSingleOps(List<AssetOperation> freezeSingleOps) {
        this.freezeSingleOps = freezeSingleOps;
    }

    public Money getUnfreezeAmount() {
        return unfreezeAmount;
    }

    public void setUnfreezeAmount(Money unfreezeAmount) {
        this.unfreezeAmount = unfreezeAmount;
    }

    public List<AssetOperation> getUnfreezeSingleOps() {
        return unfreezeSingleOps;
    }

    public void setUnfreezeSingleOps(List<AssetOperation> unfreezeSingleOps) {
        this.unfreezeSingleOps = unfreezeSingleOps;
    }

    public AssetOperation getSelfAssetOperation() {
        return selfAssetOperation;
    }

    public void setSelfAssetOperation(AssetOperation selfAssetOperation) {
        this.selfAssetOperation = selfAssetOperation;
    }

    @Override
    public String toString() {
        return selfAssetOperation.getGraphUniqueId()+"#"+ selfAssetOperation.getActionType()+"#"+selfAssetOperation.getAmount();
    }
}
