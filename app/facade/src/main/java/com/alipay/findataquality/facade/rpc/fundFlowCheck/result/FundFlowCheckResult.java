package com.alipay.findataquality.facade.rpc.fundFlowCheck.result;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedList;
import java.util.List;

/**
 * @ClassName FullTraceDataCheckResult
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 16:14
 * @Version V1.0
 **/
public class FundFlowCheckResult extends BaseResult{
    protected static final Logger LOGGER = LoggerFactory.getLogger(FundFlowCheckResult.class);

    //校验规则列表
    private List<RuleCheckResult> checkResultList = new LinkedList<>();
    //资金模型校验列表
    private List<ModelCheckResult> modelCheckResultList = new LinkedList<>();
    //扩展信息
    private String extInfo;

    public List<RuleCheckResult> getCheckResultList() {
        return checkResultList;
    }

    public void setCheckResultList(List<RuleCheckResult> checkResultList) {
        this.checkResultList = checkResultList;
    }

    public List<ModelCheckResult> getModelCheckResultList() {
        return modelCheckResultList;
    }

    public void setModelCheckResultList(List<ModelCheckResult> modelCheckResultList) {
        this.modelCheckResultList = modelCheckResultList;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        if (!CollectionUtils.isEmpty(this.getCheckResultList())){
            sb.append("当前【资金规则校验】执行结果共包含"+this.getCheckResultList().size()+"条规则\n");
            for (int i = 0; i < this.getCheckResultList().size(); i++) {
                sb.append("========================\n");
                RuleCheckResult curr = this.getCheckResultList().get(i);
                if(curr!=null){
                    sb.append("第"+(i+1)+"条规则执行详情如下:\n");
                    sb.append(this.getCheckResultList().get(i).getPrintStr());
                }else{
                    sb.append("第"+(i+1)+"条规则执行结果为null\n");
                }
            }
        }

        if (!CollectionUtils.isEmpty(this.getModelCheckResultList())) {
            sb.append("当前【资金模型校验】执行结果共包含" + this.getModelCheckResultList().size() + "条规则\n");
            for (int i = 0; i < this.getModelCheckResultList().size(); i++) {
                sb.append("========================\n");
                ModelCheckResult curr = this.getModelCheckResultList().get(i);
                if (curr != null) {
                    sb.append("第" + (i + 1) + "条规则执行详情如下:\n");
                    sb.append(this.getModelCheckResultList().get(i).getPrintStr());
                } else {
                    sb.append("第" + (i + 1) + "条规则执行结果为null\n");
                }
            }
        }
        sb.append("========================\n执行统计信息如下：\n"+this.extInfo);
        return sb.toString();
    }

    public void calculateStatistic(){
        String extInfo = calculateStatisticForRuleCheck("");
        extInfo = calculateStatisticForModelCheck(extInfo);
        this.setExtInfo(extInfo);

    }

    public String calculateStatisticForRuleCheck(String extInfo){
        if(checkResultList==null||checkResultList.isEmpty()){
            return extInfo;
        }

        /**
         * totalCnt,总数
         * successCnt,通过数
         * failCnt,失败数
         * exceptionCnt,异常数
         * ignoreCnt,忽略数
         * successRate,通过率
         */
        int totalCnt=checkResultList.size();
        int successCnt=0;
        int failCnt=0;
        int exceptionCnt=0;
        int ignoreCnt=0;

        //通过比率，百分比形式，保留两位小数
        String successRate;
        for (int i = 0; i < checkResultList.size(); i++) {
            RuleCheckResult result = checkResultList.get(i);
            switch (CheckStatusEnum.getByCode(result.getCheckStatus())){
                case SUCCESS:
                    successCnt++;
                    break;
                case FAIL:
                    failCnt++;
                    break;
                case EXCEPTION:
                    exceptionCnt++;
                    break;
                case IGNORE:
                    ignoreCnt++;
                    break;
                default:
                    break;
            }
        }
        //处理规则校验数据统计
        extInfo = StrUtil.putJsonValue(extInfo,"totalCnt",""+totalCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"successCnt",""+successCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"failCnt",""+failCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"exceptionCnt",""+exceptionCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"ignoreCnt",""+ignoreCnt);
        successRate = String.format("%.2f%%",successCnt/((successCnt+failCnt+exceptionCnt)*0.01));
        extInfo = StrUtil.putJsonValue(extInfo,"successRate",""+successRate);

        return extInfo;

    }
    /**
     * 资金模型检测的统计结果
     * @param extInfo
     * @return
     */
    public String calculateStatisticForModelCheck(String extInfo){
        /**
         * totalCnt,总数
         * successCnt,通过数
         * failCnt,失败数
         * exceptionCnt,异常数
         * ignoreCnt,忽略数
         * successRate,通过率
         */
        if(modelCheckResultList==null||modelCheckResultList.isEmpty()){
            return extInfo;
        }
        int modelTotalCnt=modelCheckResultList.size();
        int modelSuccessCnt=0;
        int modelFailCnt=0;
        int modelExceptionCnt=0;
        int modelIgnoreCnt=0;
        //通过比率，百分比形式，保留两位小数
        String modelSuccessRate;
        for (int i = 0; i < modelCheckResultList.size(); i++) {
            RuleCheckResult result = modelCheckResultList.get(i);
            switch (CheckStatusEnum.getByCode(result.getCheckStatus())){
                case SUCCESS:
                    modelSuccessCnt++;
                    break;
                case FAIL:
                    modelFailCnt++;
                    break;
                case EXCEPTION:
                    modelExceptionCnt++;
                    break;
                case IGNORE:
                    modelIgnoreCnt++;
                    break;
                default:
                    break;
            }
        }

        //处理模型校验数据统计
        extInfo = StrUtil.putJsonValue(extInfo,"modelTotalCnt",""+modelTotalCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"modelSuccessCnt",""+modelSuccessCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"modelFailCnt",""+modelFailCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"modelExceptionCnt",""+modelExceptionCnt);
        extInfo = StrUtil.putJsonValue(extInfo,"modelIgnoreCnt",""+modelIgnoreCnt);
        modelSuccessRate = String.format("%.2f%%",modelSuccessCnt/((modelSuccessCnt+modelFailCnt+modelExceptionCnt)*0.01));
        extInfo = StrUtil.putJsonValue(extInfo,"modelSuccessRate",""+modelSuccessRate);

        return extInfo;
    }



    /**
     * 将result合并入当前result结果中
     * @param result
     */
    public void merge(FundFlowCheckResult result){
        if(result==null||(result.getCheckResultList()==null &&result.getModelCheckResultList()==null)){
            return;
        }
        this.checkResultList.addAll(result.getCheckResultList());
        this.modelCheckResultList.addAll(result.getModelCheckResultList());

        this.calculateStatistic();
    }

    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }


}
