package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model;

import com.alipay.findataquality.facade.result.CommonResult;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName QueryTaskResult
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 16:22
 * @Version V1.0
 **/
public class QuerySceneResult extends CommonResult {
    private List<QueryScene> scenes = new ArrayList<>();

    public List<QueryScene> getScenes() {
        return scenes;
    }

    public void setScenes(List<QueryScene> scenes) {
        this.scenes = scenes;
    }
}
