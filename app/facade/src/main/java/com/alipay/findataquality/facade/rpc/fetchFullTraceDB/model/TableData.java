package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.model;

import com.alipay.sofa.common.utils.StringUtil;

import java.util.*;

/**
 * @ClassName DBTable
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/21 16:17
 * @Version V1.0
 **/
public class TableData {
    //DB名
    private String dbName;
    //表名
    private String tableName;
    //表字段
    private List<String> columnName=new ArrayList<>();

    //表字段索引
    private Map<String,Integer> columnIndex = new HashMap<>();

    //表值，可包含多行，每行按顺序放入list
    private List<List<String>> columnData=new ArrayList<>();

    /**
     * 返回结果中第一条非空的数据
     * @param columnName
     * @return
     */
    public String getFirstValue(String columnName){
        if(columnIndex.containsKey(columnName)&&columnData.size()>0){
            int index = columnIndex.get(columnName);
            for (int i = 0; i < columnData.size(); i++) {
                //返回第一个非空的数值
                String data = columnData.get(i).get(index);
                if(StringUtil.isNotBlank(data)){
                    return data;
                }
            }
        }
        return null;
    }

    /**
     * 返回字段拼接形式，方便在SQL中通过in语句来使用，如：'a','b','c'
     * @param columnName
     * @return
     */
    public String getArrayValue(String columnName){
        String[] list = getArrayValueList(columnName);
        if(list!=null){
            StringBuilder sb  = new StringBuilder();
            int cnt = 0;
            for (String str: list) {
                if(StringUtil.isBlank(str)){
                    continue;
                }
                if(cnt++==0){
                    sb.append("'").append(str).append("'");
                }else{
                    sb.append(",'").append(str).append("'");
                }
            }
            return sb.toString();
        }
        return null;
    }

    public String[] getArrayValueList(String columnName){
        List<String>list = new ArrayList<>();
        boolean containsKey = columnIndex.containsKey(columnName)
                ||columnIndex.containsKey(columnName.toUpperCase(Locale.ROOT))
                ||columnIndex.containsKey(columnName.toLowerCase(Locale.ROOT));
        if(containsKey&&columnData.size()>0){
            //忽略大小写
            Integer index = columnIndex.get(columnName);
            if(index==null){
                index = columnIndex.get(columnName.toUpperCase(Locale.ROOT));
            }
            if(index==null){
                index = columnIndex.get(columnName.toLowerCase(Locale.ROOT));
            }
            for(int i=0;i<columnData.size();i++){
                //取第i行，第index列数据
                String value = columnData.get(i).get(index);
                list.add(value);
            }
        }
        if(list.isEmpty()){
            return null;
        }
        return list.toArray(new String[0]);
    }

    public Integer getColumnIndex(String columnName){
        return this.columnIndex.get(columnName);
    }

    public void addColumnName(String columnName){
        this.columnIndex.put(columnName,this.columnName.size());
        this.columnName.add(columnName);
    }
    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getColumnName() {
        return columnName;
    }

    public void setColumnName(List<String> columnName) {
        this.columnName = columnName;
    }

    public List<List<String>> getColumnData() {
        return columnData;
    }

    public void setColumnData(List<List<String>> columnData) {
        this.columnData = columnData;
    }
}
