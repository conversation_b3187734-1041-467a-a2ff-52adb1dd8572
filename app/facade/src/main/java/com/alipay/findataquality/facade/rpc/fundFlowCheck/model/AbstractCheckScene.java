package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.ExpressionEvaluatorUtil;
import com.alipay.sofa.common.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName BaseCheckScene
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/21 14:50
 * @Version V1.0
 **/
public abstract class AbstractCheckScene <T,A> {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractCheckScene.class);
    protected TableDataContext dataContext;

    public AbstractCheckScene(TableDataContext dataContext){
        this.dataContext = dataContext;
    }

    /**
     * 校验场景名称
     * @return
     */
    public abstract String checkSceneName();

    /**
     * 对于计算表达式，本次可支持：
     * 1.计算逻辑中，左右两边结果均为集合，使用集合形式判断相等结果
     * 2.左边两边支持=、->判断，其中=表述两边集合元素需完全相等，->表示左边集合元素去重后是右边的子集
     * 3.在右侧，使用|分割后面描述规则检查的内容及失败提示，即格式为 规则内容|规则描述|失败提示，其中规则描述和失败提示可为空
     * 规则示例：
     * （1）$fc.status={S}|fc包含一条status结果为S的数据|xx规则失败
     * （2）$fc.status->{S}|fc中status值全部为S
     * 3.拓展函数sum、count、distinct、concat在后续迭代版本中支持
     * （1）count(fc)->2,
     * （2）sum(fc.real_amount)->100,
     * （3）concat($fc.status,-,$fc.inst_id)={S-shumijj}
     *
     * 对于传入的变量，形式包括
     * 1.表字段$table.column,如$fc.status
     * 2.表字段json形式拓展值，$table.column#key，如$fc.ext_info#status
     * 3.固定值集合，如{1,2,3}表示值集合为{1,2,3}
     * 4.特殊变量@any任意值，@null空，@notNull非空
     *
     * 对于传入的变量，形式包括
     * 1.表字段$table.column,如$fc.status
     * 2.表字段json形式拓展值，$table.column#key，如$fc.ext_info#currentMasterInstId
     * 3.固定值集合，如{1,2,3}表示值集合为{1,2,3}
     *
     * 对于复杂计算，可以使用setVar函数来加入前置计算好的变量结果，前置set之后，可在后续直接使用
     * 注意，set若和现有表变量重叠，会覆盖表变量
     */
    protected List<AbstractRuleCheckTask> getAllCheckTask(){
        Method[] methods = this.getClass().getDeclaredMethods();
        List<AbstractRuleCheckTask> checkFuncTasks = new ArrayList<>();
        for (Method method: methods) {
            String contextKey = getFuncContextKey(method);

            AutoCheck checkAnnotation = method.getAnnotation(AutoCheck.class);
            if(checkAnnotation==null){
                LOGGER.info("方法{}不包含自动校验注解，跳过处理",contextKey);
                continue;
            }
            if(method.getParameterTypes().length==0){
                LOGGER.info("方法{}未找到数据输入参数，跳过处理",contextKey);
                continue;
            }
            if(method.getReturnType().equals(RuleCheckResult.class)){
                LOGGER.info("检测到校验方法{}，校验数据{}",contextKey,method.getParameterTypes());
                AbstractRuleCheckTask task = new AbstractRuleCheckTask(method,this,dataContext);
                checkFuncTasks.add(task);
                task.process();
            }else{
                LOGGER.warn("校验方法{}返回结果非RuleCheckResult类型，跳过处理",contextKey);
            }
        }
        return checkFuncTasks;
    }

    /**
     * 执行校验任务
     * @return
     */
    public FundFlowCheckResult executeTask(){
        LOGGER.info("{}执行校验任务开始执行",this.getClass().getSimpleName());
        long start = System.currentTimeMillis();
        FundFlowCheckResult result = new FundFlowCheckResult();

        //获取线程池
        ExecutorService executor = this.getDataContext().getExecutorService();

        List<AbstractRuleCheckTask> taskList =  getAllCheckTask();
        List<Future<RuleCheckResult>> futures = new ArrayList<>();
        List<RuleCheckResult> ruleCheckResultList = new ArrayList<>();

        for (AbstractRuleCheckTask task: taskList) {
            LOGGER.info("添加校验任务{}",task.getCheckSceneName());
            futures.add(executor.submit(task));
        }
        int cnt=0;
        // 收集并打印所有查询的结果
        for (Future<RuleCheckResult> future : futures) {
            try {
                //get()会阻塞直到有结果，此处设定每个任务最长等待5s
                long timeout = 5L;
                TimeUnit unit = TimeUnit.SECONDS;
                ruleCheckResultList.add(future.get(timeout,unit));
            } catch (Exception e) {
                //e.printStackTrace();
                LOGGER.error("校验任务{}执行异常,e={}",taskList.get(cnt).getCheckSceneName(),e);
            }
            cnt++;
        }
        result.setCheckResultList(ruleCheckResultList);
        result.calculateStatistic();
        LOGGER.info("{}校验任务执行完成,耗时{}s",this.getClass().getSimpleName(),(System.currentTimeMillis()-start)/1000.0);
        return result;
    }

    /**
     * 设置运行中校验变量上下文
     * 使用之后
     * @param key
     * @param value
     */
    public void putVar(String key,String value){
        if(StringUtil.isBlank(key)||StringUtil.isBlank(value)){
            return;
        }
        this.dataContext.getCheckContext().putVar(getCurrentContextKey(),key,value);
    }

    /**
     * 获取当前校验方法名，格式为class#method
     * @return
     */
    protected String getCurrentContextKey(){
        StackTraceElement element = getCurrentCheckFunc();
        String[] classPath = element.getClassName().split("\\.");
        //返回格式为class#method
        return classPath[classPath.length-1]+"#"+element.getMethodName();
    }

    protected StackTraceElement getCurrentCheckFunc(){
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        return stackTrace[4];
    }

    public RuleCheckResult autoCheckEqual(AssetRecordData data1, String value){
        return autoCheckEqual(data1,new String[]{value});
    }

    /**
     * 根据表达式做校验
     * @param data1
     * @param expression
     * @return
     */
    public RuleCheckResult autoCheckExpression(AssetRecordData data1, String expression){
        if(data1==null||data1.isEmpty()){
            return new RuleCheckResult(CheckStatusEnum.IGNORE,"左侧数据为空，跳过",null,data1);
        }
        int cnt = 0;
        int diffCnt = 0;
        List<DataCoordinate> dataCoordinateList = new ArrayList<>();
        for(int i=0;i<data1.getDataList().size();i++){
            TableData data = data1.getDataList().get(i);
            for(int j=0;j<data.getColumnData().size();j++){
                cnt++;
                //判断当前数据是否满足条件
                Map<String,Object> valueMap = new HashMap<>();
                for(int k=0;k<data.getColumnName().size();k++){
                    //放入变量名、变量值
                    String columnName = data.getColumnName().get(k);
                    valueMap.put(columnName,data.getColumnIndexValue(columnName,j));
                }
                //从表字段中进行表达式处理，表字段本身不会缺失，故此处设为false
                Boolean isMatch = ExpressionEvaluatorUtil.evaluateBooleanExpression(valueMap, expression,false,null);
                if(!Boolean.TRUE.equals(isMatch)){
                    //不满足条件，将坐标中加入list
                    dataCoordinateList.addAll(data.getDataCoordinateList(j));
                    diffCnt++;
                }
            }
        }
        if(diffCnt>0){
            String checkDetail = "共检查"+cnt+"条数据，其中"+diffCnt+"条不通过";
            return new RuleCheckResult(CheckStatusEnum.FAIL,checkDetail.toString(),dataCoordinateList,data1);
        }else{
            String checkDetail = "共检查"+cnt+"条数据，全部通过";
            return new RuleCheckResult(CheckStatusEnum.SUCCESS,checkDetail,null,data1);
        }
    }

    public RuleCheckResult autoCheckEqual(AssetRecordData data1, String[]data2){
        try{
            if(data1==null||data1.isEmpty()){
                return new RuleCheckResult(CheckStatusEnum.IGNORE,"左侧数据为空，跳过",null,data1);
            }
            //StringBuilder sb = new StringBuilder();
            int cnt = 0;
            int diffCnt = 0;
            List<DataCoordinate> dataCoordinateList = new ArrayList<>();
            for (int i = 0; i < data1.getDataList().size(); i++) {
                TableData tableData = data1.getDataList().get(i);
                for (int j = 0; j < tableData.getColumnName().size(); j++) {
                    String columnName = tableData.getColumnName().get(j);
                    int index = tableData.getColumnIndex(columnName);
                    for (int k = 0; k < tableData.getColumnData().size(); k++) {
                        List<String>row = tableData.getColumnData().get(k);
                        String rowValue = row.get(index);
                        cnt++;
                        boolean isExist = false;
                        for (String valueEqual:data2) {
                            if(valueEqual.equals(rowValue)){
                                isExist = true;
                                break;
                            }
                        }
                        if(!isExist){
                            diffCnt++;
                            //sb.append(tableData.getTableFullPathOrig()+"."+columnName+"["+k+"]="+rowValue+"\n");
                            //记录异常数据坐标
                            dataCoordinateList.addAll(tableData.getDataCoordinateList(columnName,k));
                        }
                    }
                }
            }
            if(diffCnt>0){
                String checkDetail = "共检查"+cnt+"条数据，其中"+diffCnt+"条不通过";
                return new RuleCheckResult(CheckStatusEnum.FAIL,checkDetail.toString(),dataCoordinateList,data1);
            }else{
                String checkDetail = "共检查"+cnt+"条数据，全部通过";
                return new RuleCheckResult(CheckStatusEnum.SUCCESS,checkDetail,null,data1);
            }
        }catch (Exception e){
            LOGGER.error("autoCheckEqual校验任务执行异常,e={}",e);
            return new RuleCheckResult(CheckStatusEnum.EXCEPTION,"校验执行异常",null,data1);
        }
    }

    public RuleCheckResult autoCheckSumEqual(AssetRecordData data1, AssetRecordData data2, double factor){
        try{
            if(data1.isEmpty()&&data2.isEmpty()){
                return new RuleCheckResult(CheckStatusEnum.IGNORE,"左右数据全部为空，跳过",null,data1,data2);
            }
            Integer data1Sum = data1.sum();
            Integer data2Sum = data2.sum();
            if(data1Sum==null){
                return new RuleCheckResult(CheckStatusEnum.FAIL,"左侧数据包含非数字，校验失败",data1.getAllDataCoordinate(),data1,data2);
            }
            if(data2Sum==null){
                return new RuleCheckResult(CheckStatusEnum.FAIL,"右侧数据包含非数字，校验失败",data2.getAllDataCoordinate(),data1,data2);
            }
            Double newValue = data1Sum*factor;
            if(newValue.equals(Double.valueOf(data2Sum))){
                return new RuleCheckResult(CheckStatusEnum.SUCCESS,"左边汇总值="+newValue+"，右边汇总值="+data2Sum+"，左右汇总值相等",null,data1,data2);
            }else{
                List<DataCoordinate> coordinateList = new ArrayList<>();
                coordinateList.addAll(data1.getAllDataCoordinate());
                coordinateList.addAll(data2.getAllDataCoordinate());
                return new RuleCheckResult(CheckStatusEnum.FAIL,"左边汇总值="+newValue+"，右边汇总值="+data2Sum+"，左右汇总值不等",coordinateList,data1,data2);
            }
        }catch (Exception e){
            LOGGER.error("autoCheckEqual校验任务执行异常,e={}",e);
            return new RuleCheckResult(CheckStatusEnum.EXCEPTION,"校验执行异常",null,data1,data2);
        }
    }

    /**
     * 增加一个枚举类型，left_equal, right_equal, all_equal, set_equal
     * @param data1
     * @param data2
     * @return
     */
    public RuleCheckResult autoCheckCombineEqual(AssetRecordData data1, AssetRecordData data2){
        List<TableDataCombinator> data1Combinators = getCombineCombinatorList(data1);
        List<TableDataCombinator> data2Combinators = getCombineCombinatorList(data2);
        //左侧为空，直接返回
        if(data1Combinators.isEmpty()&&data2Combinators.isEmpty()){
            return new RuleCheckResult(CheckStatusEnum.IGNORE,"左右数据全部为空，跳过",null,data1,data2);
        }
        //两边数据量相等，进行循环比对
        for (int i = 0; i < data1Combinators.size(); i++) {
            TableDataCombinator left = data1Combinators.get(i);
            if(left.isUsed()){
                continue;
            }
            for (int j = 0; j < data2Combinators.size(); j++) {
                if(left.isUsed()){
                    break;
                }
                TableDataCombinator right = data2Combinators.get(j);
                if(right.isUsed()){
                    continue;
                }
                if(left.getMd5().equals(right.getMd5())){
                    left.setUsed(true);
                    right.setUsed(true);
                }
            }
        }
        //当全部使用过时，证明左右两边完全匹配
        if(data1Combinators.stream().filter(data->!data.isUsed()).collect(Collectors.toList()).isEmpty()){
            return new RuleCheckResult(CheckStatusEnum.SUCCESS,"左边数据条数="+data1Combinators.size()+"，右边数据条数="+data2Combinators.size()+"，且左右数据内容完全匹配",null,data1,data2);
        }else{
            List<TableDataCombinator> leftRemain = data1Combinators.stream().filter(data->!data.isUsed()).collect(Collectors.toList());
            List<TableDataCombinator> rightRemain = data2Combinators.stream().filter(data->!data.isUsed()).collect(Collectors.toList());
            List<DataCoordinate> coordinateList = new ArrayList<>();
            coordinateList.addAll(getCoordinateFromCombinator(leftRemain));
            coordinateList.addAll(getCoordinateFromCombinator(rightRemain));
            if(data1Combinators.size()==data2Combinators.size()) {
                return new RuleCheckResult(CheckStatusEnum.FAIL, "左边数据条数=" + data1Combinators.size() + "，右边数据条数=" + data2Combinators.size() + "，左右数据条数相等，但数据内容存在差异", coordinateList,data1,data2);
            }else{
                return new RuleCheckResult(CheckStatusEnum.FAIL, "左边数据条数=" + data1Combinators.size() + "，右边数据条数=" + data2Combinators.size() + "，左右数据条数不同", coordinateList,data1,data2);
            }
        }
    }

    /**
     * 从AssetRecordData中获取成对组合值
     * @param data
     * @return
     */
    protected List<TableDataCombinator> getCombineCombinatorList(AssetRecordData data){
        List<TableDataCombinator> tableCompareList = new ArrayList<>();
        if(data==null||data.getDataList()==null||data.getDataList().isEmpty()){
            return tableCompareList;
        }
        //获取dataTable循环
        for (int i = 0; i < data.getDataList().size(); i++) {
            TableData tableData = data.getDataList().get(i);
            //获取数据行
            for (int k = 0; k < tableData.getColumnData().size(); k++) {
                //获取数据字段
                TableDataCombinator dataCompare = new TableDataCombinator(tableData);
                for (int j = 0; j < tableData.getColumnName().size(); j++) {
                    String columnName = tableData.getColumnName().get(j);
                    int index = tableData.getColumnIndex(columnName);
                    List<String> row = tableData.getColumnData().get(k);
                    dataCompare.addColumnValue(columnName,row.get(index),k);
                }
                dataCompare.calculateMd5();
                tableCompareList.add(dataCompare);
            }
        }
        return tableCompareList;
    }

    /**
     * 从组合中获取数据字段坐标
     * @param combinators
     * @return
     */
    public List<DataCoordinate> getCoordinateFromCombinator(List<TableDataCombinator> combinators){
        List<DataCoordinate> coordinateList = new ArrayList<>();
        for (TableDataCombinator combinator:combinators) {
            for (String var:combinator.getColumnName()) {
                List<DataCoordinate> coordinates = combinator.getTableData().getDataCoordinateList(var,combinator.getRowIndex());
                coordinateList.addAll(coordinates);
            }
        }
        return coordinateList;
    }

    protected String getFuncContextKey(Method method){
        return this.getClass().getSimpleName()+"#"+method.getName();
    }

    public TableDataContext getDataContext() {
        return dataContext;
    }

    public void setDataContext(TableDataContext dataContext) {
        this.dataContext = dataContext;
    }

    public String getVar(String varName,String condition){
        return null;
    }

    public AssetRecordData getDataTable(String shortName){
        StackTraceElement element = getCurrentCheckFunc();
        return null;
    }
    public String getVarCombination(String varList,String separator, String condition){
        return null;
    }
}
