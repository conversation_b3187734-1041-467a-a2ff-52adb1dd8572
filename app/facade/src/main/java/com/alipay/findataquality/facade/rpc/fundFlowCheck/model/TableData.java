package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.CheckUtil;
import com.alipay.sofa.common.utils.StringUtil;

import java.io.Serializable;
import java.util.*;

/**
 * @ClassName TableData
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 16:58
 * @Version V1.0
 **/
public class TableData implements Serializable,Cloneable {

    private static final long serialVersionUID = -7874603209653591211L;

    //DB名
    private String dbName;
    //表名
    private String tableName;
    //表字段
    private List<String> columnName=new ArrayList<>();

    //表字段索引
    private Map<String,Integer> columnIndex = new HashMap<>();

    //表值，可包含多行，每行按顺序放入list
    private List<List<String>> columnData=new ArrayList<>();

    /**
     * 第几行行号，从0开始。在数据过滤场景中，如果行被过滤后，该列表需同样被过滤
     * e.g., 过滤钱rowIndexList={0,1,2,3}，假如过滤掉的为第2行，那么过滤后rowIndexList={0,1,3}
     */
    private List<Integer> rowIndexList = new ArrayList<>();

    public TableData clone() {
        try {
            TableData tableDataNew = (TableData) super.clone();
            //copy字段名
            tableDataNew.setColumnName(new ArrayList<>(this.columnName));
            Map<String,Integer> columnIndexNew = new HashMap<>();
            //copy表字段索引
            columnIndexNew.putAll(this.columnIndex);
            //copy多行数据值
            List<List<String>> columnDataNew = new ArrayList<>();
            for (List<String>data: columnData) {
                columnDataNew.add(new ArrayList<>(data));
            }
            tableDataNew.setColumnData(columnDataNew);
            //copy行号
            tableDataNew.setRowIndexList(new ArrayList<>(this.rowIndexList));
            return tableDataNew;
        } catch (CloneNotSupportedException e) {
            return null;
        }
    }

    public String getTableFullPath(){
        return dbName+"."+ CheckUtil.removeTrailingNumbers(tableName);
    }

    public String getTableFullPathOrig(){
        return dbName+"."+tableName;
    }

    /**
     * 获取指定字段的校验坐标
     * @param columnName
     * @param rowIndex
     * @return
     */
    public List<DataCoordinate> getDataCoordinateList(String columnName, int rowIndex) {
        List<DataCoordinate> dataCoordinateList = new ArrayList<>();
        Integer columnIndex = this.getColumnIndex(columnName);
        if (columnIndex == null || this.getColumnData().size() <= rowIndex) {
            return dataCoordinateList;
        }
        List<String> row = this.getColumnData().get(rowIndex);
        DataCoordinate dataCoordinate = new DataCoordinate();
        dataCoordinate.setDbName(this.getDbName());
        dataCoordinate.setTableName(this.getTableName());
        dataCoordinate.setColumnName(columnName);
        //对前端透出的字段坐标上，从1开始
        dataCoordinate.setValueIndex(this.getRowIndexList().get(rowIndex)+1);
        dataCoordinate.setColumnValue(this.getColumnIndexValue(columnName, rowIndex));
        dataCoordinateList.add(dataCoordinate);
        return dataCoordinateList;
    }

    /**
     * 获取指定数据行的字段校验坐标
     * @param rowIndex
     * @return
     */
    public List<DataCoordinate> getDataCoordinateList(int rowIndex) {
        List<DataCoordinate> dataCoordinateList = new ArrayList<>();
        if (this.getColumnData().size() <= rowIndex) {
            return dataCoordinateList;
        }
        for (int i = 0; i < this.getColumnName().size(); i++) {
            String columnName = this.getColumnName().get(i);
            DataCoordinate dataCoordinate = new DataCoordinate();
            dataCoordinate.setDbName(this.getDbName());
            dataCoordinate.setTableName(this.getTableName());
            dataCoordinate.setColumnName(columnName);
            //对前端透出的字段坐标上，从1开始
            dataCoordinate.setValueIndex(this.getRowIndexList().get(rowIndex)+1);
            dataCoordinate.setColumnValue(this.getColumnIndexValue(columnName, rowIndex));
            dataCoordinateList.add(dataCoordinate);
        }
        return dataCoordinateList;
    }

    /**
     * 获取当前表的所有数据坐标
     * @return
     */
    public List<DataCoordinate> getDataCoordinateList() {
        List<DataCoordinate> dataCoordinateList = new ArrayList<>();
        for (int j = 0; j < this.getColumnData().size(); j++) {
            List<String> row = this.getColumnData().get(j);
            for(String var:this.getColumnName()){
                dataCoordinateList.addAll(this.getDataCoordinateList(var,j));
            }
        }
        return dataCoordinateList;
    }

    /**
     * 判断输入的表名是否和当前表一致
     * @param tableName
     * @return
     */
    public boolean isCurrentTable(String tableName){
        String inputName = CheckUtil.removeTrailingNumbers(tableName);
        if(StringUtil.isBlank(inputName)){
            return false;
        }
        String currentName = getTableFullPath();
        return inputName.equals(currentName);
    }

    /**
     * 获取指定行指定字段的值
     * @param columnName
     * @param index
     * @return
     */
    public String getColumnIndexValue(String columnName, int index){
        String[] values= getArrayValueList(columnName);
        if(values!=null&&values.length>index){
            return values[index];
        }
        return null;
    }

    /**
     * 返回结果中第一条非空的数据
     * @param columnName
     * @return
     */
    public String getFirstValue(String columnName){
        if(columnIndex.containsKey(columnName)&&columnData.size()>0){
            int index = columnIndex.get(columnName);
            for (int i = 0; i < columnData.size(); i++) {
                //返回第一个非空的数值
                String data = columnData.get(i).get(index);
                if(StringUtil.isBlank(data)){
                    return data;
                }
            }
        }
        return null;
    }

    /**
     * 返回字段拼接形式，方便在SQL中通过in语句来使用，如：'a','b','c'
     * @param columnName
     * @return
     */
    public String getArrayValue(String columnName){
        String[] list = getArrayValueList(columnName);
        if(list!=null){
            StringBuilder sb  = new StringBuilder();
            int cnt = 0;
            for (String str: list) {
                if(StringUtil.isBlank(str)){
                    continue;
                }
                if(cnt++==0){
                    sb.append("'").append(str).append("'");
                }else{
                    sb.append(",'").append(str).append("'");
                }
            }
            return sb.toString();
        }
        return null;
    }

    public String[] getArrayValueList(String columnName){
        List<String>list = new ArrayList<>();
        boolean containsKey = columnIndex.containsKey(columnName)
                ||columnIndex.containsKey(columnName.toUpperCase(Locale.ROOT))
                ||columnIndex.containsKey(columnName.toLowerCase(Locale.ROOT));
        if(containsKey&&columnData.size()>0){
            //忽略大小写
            Integer index = columnIndex.get(columnName);
            if(index==null){
                index = columnIndex.get(columnName.toUpperCase(Locale.ROOT));
            }
            if(index==null){
                index = columnIndex.get(columnName.toLowerCase(Locale.ROOT));
            }
            for(int i=0;i<columnData.size();i++){
                //取第i行，第index列数据
                String value = columnData.get(i).get(index);
                list.add(value);
            }
        }
        if(list.isEmpty()){
            return null;
        }
        return list.toArray(new String[0]);
    }

    /**
     * 判断是否包含指定字段
     * @param columnName
     * @return
     */
    public boolean containsColumn(String columnName){
        return this.columnIndex.containsKey(columnName);
    }

    public Integer getColumnIndex(String columnName){
        return this.columnIndex.get(columnName);
    }

    public void addColumnName(String columnName){
        this.columnIndex.put(columnName,this.columnName.size());
        this.columnName.add(columnName);
    }
    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public List<String> getColumnName() {
        return columnName;
    }

    public void setColumnName(List<String> columnName) {
        this.columnName = columnName;
    }

    public List<List<String>> getColumnData() {
        return columnData;
    }

    public void setColumnData(List<List<String>> columnData) {
        this.columnData = columnData;
    }

    public List<Integer> getRowIndexList() {
        return rowIndexList;
    }

    public void setRowIndexList(List<Integer> rowIndexList) {
        this.rowIndexList = rowIndexList;
    }
}
