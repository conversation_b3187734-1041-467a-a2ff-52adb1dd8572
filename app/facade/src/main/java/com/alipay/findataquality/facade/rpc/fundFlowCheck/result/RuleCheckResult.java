package com.alipay.findataquality.facade.rpc.fundFlowCheck.result;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.AssetRecordData;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.DataCoordinate;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionGroup;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.StrUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: RuleCheckResult.java, v 0.1 2020-03-24 下午4:56 yucheng.hyc Exp $$
 */
public class RuleCheckResult{
    //校验场景
    private String checkScene;
    //校验场景名
    private String checkSceneName;
    //校验方法
    private String checkMethod;
    //规则描述
    private String ruleDesc;
    //规则执行结果
    //CheckStatusEnum
    private String checkStatus;
    //校验类型
    private String checkType;
    //规则执行详情
    private String checkDetail;
    //扩展信息
    private String extInfo;
    //被校验数据坐标
    private List<DataCoordinate> diffCoordinates;
    //资金模型检查结果，每个包含一段资金流
    private TransactionGroup transactionGroup;

    /**
     * 初始化
     * @param checkStatus
     * @param checkDetail
     * @param diffCoordinates
     */
    public RuleCheckResult(CheckStatusEnum checkStatus, String checkDetail, List<DataCoordinate> diffCoordinates, AssetRecordData...dataList) {
        this.checkStatus=checkStatus.getCode();
        this.checkDetail = checkDetail;
        this.diffCoordinates = diffCoordinates;
        //放入关联表扩展信息，左表、右表、异常数据坐标等
        if (dataList!=null){
            if (dataList.length==1){
                this.putRelatedTableExt("leftTable",dataList[0]);
            }
            if (dataList.length>1){
                this.putRelatedTableExt("leftTable",dataList[0]);
                this.putRelatedTableExt("rightTable",dataList[1]);
            }
        }
        //将diffCoordinates放入扩展信息中
        String coordinates = JSONObject.toJSONString(diffCoordinates);
        extInfo = StrUtil.putJsonValue(extInfo,"diffCoordinates",coordinates);
    }

    /**
     * 将结果以String类型输出
     * @return
     */
    public String getPrintStr(){
        StringBuilder sb = new StringBuilder();
        sb.append("checkScene:"+checkScene+"\n"+
                "checkSceneName:"+checkSceneName+"\n"+
                "checkMethod:"+checkMethod+"\n"+
                "ruleDesc:"+ruleDesc+"\n"+
                "checkStatus:"+checkStatus+"\n"+
                "checkDetail:"+checkDetail+"\n"+
                "extInfo:"+extInfo+"\n"
                //"diffCoordinates:"+ JSONObject.toJSONString(diffCoordinates)+"\n"
        );
        return sb.toString();
    }

    /**
     * 放入关联表扩展信息
     * @param key
     * @param data
     */
    public void putRelatedTableExt(String key, AssetRecordData data){
        if (data == null) {
            return;
        }
        Map<String,Integer> tableCntMap = data.findUniqueTableCntMap(true);
        // 解析extInfo字符串为JSONObject
        JSONObject jsonObject = JSON.parseObject(this.getExtInfo());
        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }
        // 获取已有的relatedTables类，如果不存在则创建一个新的
        JSONObject relatedTablesObj = jsonObject.getJSONObject(key);
        if (relatedTablesObj == null) {
            relatedTablesObj = new JSONObject();
        }
        relatedTablesObj.putAll(tableCntMap);

        // 更新JSONObject中的relatedTables
        jsonObject.put(key, relatedTablesObj);
        this.setExtInfo(jsonObject.toJSONString());
    }
    public String getExtInfo() {
        return extInfo;
    }

    public void setExtInfo(String extInfo) {
        this.extInfo = extInfo;
    }

    public String getCheckMethod() {
        return checkMethod;
    }

    public void setCheckMethod(String checkMethod) {
        this.checkMethod = checkMethod;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCheckType() {
        return checkType;
    }

    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }

    public void setCheckType(CheckTypeEnum checkType) {
        this.checkType = checkType.getCode();
    }

    public String getCheckDetail() {
        return checkDetail;
    }

    public void setCheckDetail(String checkDetail) {
        this.checkDetail = checkDetail;
    }

    public List<DataCoordinate> getDiffCoordinates() {
        return diffCoordinates;
    }

    public void setDiffCoordinates(List<DataCoordinate> diffCoordinates) {
        this.diffCoordinates = diffCoordinates;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getCheckScene() {
        return checkScene;
    }

    public void setCheckScene(String checkScene) {
        this.checkScene = checkScene;
    }

    public String getCheckSceneName() {
        return checkSceneName;
    }

    public void setCheckSceneName(String checkSceneName) {
        this.checkSceneName = checkSceneName;
    }

    public TransactionGroup getTransactionGroup() {
        return transactionGroup;
    }

    public void setTransactionGroup(TransactionGroup transactionGroup) {
        this.transactionGroup = transactionGroup;
    }
}
