package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alibaba.fastjson.JSON;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetActionTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.CheckStatusEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.ModelCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.utils.ExpressionEvaluatorUtil;
import com.iwallet.biz.common.util.money.Money;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetModelCheckTypeEnum.*;

/**
 * @ClassName AbstractFundFlowCheckScene
 * @Description 资金模型检测
 * <AUTHOR>
 * @Date 2025/5/29 20:15
 * @Version V1.0
 **/
public abstract class AbstractModelCheckScene extends AbstractCheckScene {

    public AbstractModelCheckScene(TableDataContext dataContext) {
        super(dataContext);
    }

    @Override
    protected List<AbstractModelCheckTask> getAllCheckTask(){
        Method[] methods = this.getClass().getDeclaredMethods();
        List<AbstractModelCheckTask> checkFuncTasks = new ArrayList<>();
        for (Method method: methods) {
            String contextKey = getFuncContextKey(method);

            AutoCheck checkAnnotation = method.getAnnotation(AutoCheck.class);
            if(checkAnnotation==null){
                LOGGER.info("方法{}不包含自动校验注解，跳过处理",contextKey);
                continue;
            }
            if(method.getParameterTypes().length==0){
                LOGGER.info("方法{}未找到数据输入参数，跳过处理",contextKey);
                continue;
            }
            if(method.getReturnType().equals(ModelCheckResult.class)){
                LOGGER.info("检测到校验方法{}，校验数据{}",contextKey,method.getParameterTypes());
                /**
                 * 需要识别三种.按照以上三种逻辑，添加task
                 * 1.单节点（IN、OUT、OP操作）
                 * 2.分图（TransactionGroup）
                 * 3.总图（组合图TransactionGroup list）
                 */
                Class<?>[] parameterTypes = method.getParameterTypes();
                Type[] genericParameterTypes = method.getGenericParameterTypes();
                // 识别单节点检测: 唯一参数是 AssetModelNode.class
                if (parameterTypes.length == 1 && parameterTypes[0] == AssetModelNode.class) {
                    AbstractModelCheckTask task = new AbstractModelCheckTask(method, this, dataContext,SINGLE_NODE);
                    checkFuncTasks.add(task);
                    task.process();
                }
                // 识别分图检测: 唯一参数是 TransactionGroupclass
                if (parameterTypes.length == 1
                        && parameterTypes[0] == TransactionGroup.class) {
                    AbstractModelCheckTask task = new AbstractModelCheckTask(method, this, dataContext,SUB_GRAPH);
                    checkFuncTasks.add(task);
                    task.process();
                }
                // 识别总图检测: 唯一参数是 List<TransactionGroup>
                if (parameterTypes.length == 1
                        && parameterTypes[0] == List.class) {
                    try {
                        // 检查第一个参数的类型参数是否为 TransactionGroup
                        ParameterizedType firstParamType = (ParameterizedType) genericParameterTypes[0];
                        Type[] firstTypeArgs = firstParamType.getActualTypeArguments();
                        if (firstTypeArgs.length == 1 && firstTypeArgs[0] == TransactionGroup.class) {
                            method.getDeclaringClass().getSimpleName();
                            AbstractModelCheckTask task = new AbstractModelCheckTask(method, this, dataContext,MAIN_GRAPH);
                            checkFuncTasks.add(task);
                            task.process();
                        }
                    } catch (ClassCastException e) {
                        // 如果类型不是 ParameterizedType (比如原始类型 List)
                        LOGGER.warn("校验方法{}入参不满足总图检测的入参类型，跳过处理",contextKey);
                    }
                }
            }else{
                LOGGER.warn("校验方法{}返回结果非RuleCheckResult类型，跳过处理",contextKey);
            }
        }
        return checkFuncTasks;
    }

    @Override
    public FundFlowCheckResult executeTask(){
        LOGGER.info("{}执行校验任务开始执行",this.getClass().getSimpleName());
        long start = System.currentTimeMillis();
        FundFlowCheckResult result = new FundFlowCheckResult();

        //获取线程池
        ExecutorService executor = this.getDataContext().getExecutorService();

        List<AbstractModelCheckTask> taskList =  getAllCheckTask();
        List<Future<List<ModelCheckResult>>> futures = new ArrayList<>();
        List<ModelCheckResult> modelCheckResultList = new ArrayList<>();

        for (AbstractModelCheckTask task: taskList) {
            LOGGER.info("添加校验任务{}",task.getCheckSceneName());
            futures.add(executor.submit(task));
        }
        int cnt=0;
        // 收集并打印所有查询的结果
        for (Future<List<ModelCheckResult>> future : futures) {
            try {
                //get()会阻塞直到有结果，此处设定每个任务最长等待5s
                long timeout = 5L;
                TimeUnit unit = TimeUnit.SECONDS;
                List<ModelCheckResult> modelCheckResults = future.get(timeout,unit);
                modelCheckResultList.addAll(modelCheckResults);
            } catch (Exception e) {
                result.setSuccess(false);
                LOGGER.error("校验任务{}执行异常,e={}",taskList.get(cnt).getCheckSceneName(),e);
            }
            cnt++;
        }

        result.setModelCheckResultList(modelCheckResultList);
        result.calculateStatistic();
        LOGGER.info("{}校验任务执行完成,耗时{}s",this.getClass().getSimpleName(),(System.currentTimeMillis()-start)/1000.0);
        return result;
    }


    public ModelCheckResult autoCheckGraphBalance(List<TransactionGroup> transactionGroupList){
        List<AssetModelNode> assetModelNodeList = parseTransactionGroups(transactionGroupList);
        Money totalInFlowAmount = new Money(0);
        Money totalOutFlowAmount = new Money(0);
        Money totalFreezeFlowAmount = new Money(0);
        Money totalUnFreezeFlowAmount = new Money(0);
        for (AssetModelNode assetModelNode:assetModelNodeList){
            totalInFlowAmount= totalInFlowAmount.add(assetModelNode.getInAmount());
            totalOutFlowAmount = totalOutFlowAmount.add(assetModelNode.getOutAmount());
            totalFreezeFlowAmount = totalFreezeFlowAmount.add(assetModelNode.getFreezeAmount());
            totalUnFreezeFlowAmount = totalUnFreezeFlowAmount.add(assetModelNode.getUnfreezeAmount());
       }
        Money leftAmount = totalInFlowAmount.add(totalFreezeFlowAmount);
        Money rightAmount = totalOutFlowAmount.add(totalUnFreezeFlowAmount);
        if (leftAmount.equals(rightAmount)){
            return new ModelCheckResult(CheckStatusEnum.SUCCESS, "所有账户流入金额（含冻结）总和=" + leftAmount + "元,所有账户流出金额（含解冻）总和=" + rightAmount + ",满足："+"流入总金额=流出总金额", transactionGroupList, null);
        }else{
            return new ModelCheckResult(CheckStatusEnum.FAIL, "所有账户流入金额（含冻结）总和=" + leftAmount + "元,所有账户流出金额（含解冻）总和=" + rightAmount + ",不满足："+"流入总金额=流出总金额", transactionGroupList, null);
        }

    }

    /**
     * 多渠道比较
     * 1.满足条件：先找渠道
     * 2.比较金额。
     * @param assetModelNode
     * @param actionTypeMap
     * @param comparator
     * @return
     */
    public ModelCheckResult autoCheckNodeBalance(AssetModelNode assetModelNode,Map<AssetActionTypeEnum,Integer> actionTypeMap,String comparator) {
        AssetActionTypeEnum leftAction = actionTypeMap.keySet().stream().findFirst().orElse(null);
        ModelCheckResult modelCheckResult = isMeetPreConditions(assetModelNode, leftAction, actionTypeMap.get(leftAction));

        if (modelCheckResult != null) {
            //左侧渠道不满足前置条件
            return modelCheckResult;
        }

        Money leftMoney=null,rightMoney=null;
        StringBuffer expression = new StringBuffer();
        //依次遍历比对
        List<Map.Entry<AssetActionTypeEnum, Integer>> entries =
                new LinkedList<>(actionTypeMap.entrySet());
        // 从第二个元素开始遍历
        for (int i = 1; i < entries.size(); i++) {
            Map.Entry<AssetActionTypeEnum, Integer> entry = entries.get(i);
            AssetActionTypeEnum rightAction = entry.getKey();

            leftMoney = getActionTotalMoney(assetModelNode, leftAction);
            if (leftMoney == null || leftAction == null) {
                return new ModelCheckResult(CheckStatusEnum.IGNORE, "节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,左侧金额数据为空，跳过", null, assetModelNode);
            }
            //右侧渠道不满足前置条件
            modelCheckResult = isMeetPreConditions(assetModelNode, rightAction, actionTypeMap.get(rightAction));
            if (modelCheckResult != null) {
                return modelCheckResult;
            }

            rightMoney = getActionTotalMoney(assetModelNode, rightAction);
            if (rightMoney == null || rightAction == null) {
                return new ModelCheckResult(CheckStatusEnum.IGNORE, "节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,右侧金额数据为空，跳过", null, assetModelNode);
            }

            boolean isMatch = executeMoneyBalanceExpression(leftAction, rightAction, comparator, leftMoney, rightMoney);
            expression.append(leftAction.getCode()).append(comparator).append(rightAction.getCode());
            if (!isMatch) {
                return new ModelCheckResult(CheckStatusEnum.FAIL, "节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,左边金额=" + leftMoney + "元,右边金额=" + rightMoney + ",不满足：" + expression, null, assetModelNode);
            }
            leftAction = rightAction;
        }
        return new ModelCheckResult(CheckStatusEnum.SUCCESS, "节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,左边金额=" + leftMoney + "元,右边金额=" + rightMoney + ",满足："+expression, null, assetModelNode);
    }

    /**
     * 判断单节点对应的渠道是否满足前置条件：渠道存在、渠道数量满足要求
     * @param assetModelNode
     * @param direction
     * @param channelNum
     * @return
     */
    public ModelCheckResult isMeetPreConditions(AssetModelNode assetModelNode, AssetActionTypeEnum direction,int channelNum){
        if (channelNum == -1){
            switch (direction){
                case INCREASE:
                    if (assetModelNode.getInFlows().size() == 0){
                        return new ModelCheckResult(CheckStatusEnum.IGNORE,"不满足条件:节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,存在流入渠道，且渠道只有"+assetModelNode.getInFlows().size()+"条",null,assetModelNode);
                    }
                case DECREASE:
                    if (assetModelNode.getOutFlows().size() == 0){
                        return new ModelCheckResult(CheckStatusEnum.IGNORE,"不满足条件:节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,存在流出渠道，且渠道只有"+assetModelNode.getOutFlows().size()+"条",null,assetModelNode);
                    }
                case FREEZE:
                    if (assetModelNode.getFreezeSingleOps().size() == 0){
                        return new ModelCheckResult(CheckStatusEnum.IGNORE,"不满足条件:节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,存在冻结场景，且冻结只有"+assetModelNode.getFreezeSingleOps().size()+"个",null,assetModelNode);
                    }
                case UNFREEZE:
                    if (assetModelNode.getUnfreezeSingleOps().size() == 0){
                        return new ModelCheckResult(CheckStatusEnum.IGNORE,"不满足条件:节点【"+assetModelNode.getSelfAssetOperation().getGraphUniqueId()+"】,存在解冻场景，且解冻只有"+assetModelNode.getUnfreezeSingleOps().size()+"个",null,assetModelNode);
                    }
                default:
                    ;
            }
            return null;
        }else {
            switch (direction) {
                case INCREASE:
                    if (assetModelNode.getInFlows().size() != channelNum) {
                        return new ModelCheckResult(CheckStatusEnum.IGNORE, "不满足条件:节点【" + assetModelNode.getSelfAssetOperation().getGraphUniqueId() + "】,存在流入渠道，且渠道只有" + assetModelNode.getInFlows().size() + "条", null, assetModelNode);
                    }
                case DECREASE:
                    if (assetModelNode.getOutFlows().size() != channelNum) {
                        return new ModelCheckResult(CheckStatusEnum.IGNORE, "不满足条件:节点【" + assetModelNode.getSelfAssetOperation().getGraphUniqueId() + "】,存在流出渠道，且渠道只有" + assetModelNode.getOutFlows().size() + "条", null, assetModelNode);
                    }
                case FREEZE:
                    if (assetModelNode.getFreezeSingleOps().size() != channelNum) {
                        return new ModelCheckResult(CheckStatusEnum.IGNORE, "不满足条件:节点【" + assetModelNode.getSelfAssetOperation().getGraphUniqueId() + "】,存在冻结场景，且冻结只有" + assetModelNode.getFreezeSingleOps().size() + "个", null, assetModelNode);
                    }
                case UNFREEZE:
                    if (assetModelNode.getUnfreezeSingleOps().size() != channelNum) {
                        return new ModelCheckResult(CheckStatusEnum.IGNORE, "不满足条件:节点【" + assetModelNode.getSelfAssetOperation().getGraphUniqueId() + "】,存在解冻场景，且解冻只有" + assetModelNode.getUnfreezeSingleOps().size() + "个", null, assetModelNode);
                    }
                default:
                    ;
            }
        }
        return null;
    }

    /**
     * 获取单节点对应渠道的总金额
     * @param assetModelNode
     * @param direction
     * @return
     */
    public Money getActionTotalMoney(AssetModelNode assetModelNode, AssetActionTypeEnum direction){
        switch (direction){
            case INCREASE:
                return assetModelNode.getInAmount();
            case DECREASE:
                return assetModelNode.getOutAmount();
            case FREEZE:
                return assetModelNode.getFreezeAmount();
            case UNFREEZE:
                return assetModelNode.getUnfreezeAmount();
            default:
                return null;
        }
    }

    /**
     * 执行表达式:金额比对
     * @param leftAction
     * @param rightAction
     * @param comparator
     * @param left
     * @param right
     * @return
     */
    public Boolean executeMoneyBalanceExpression(AssetActionTypeEnum leftAction,AssetActionTypeEnum rightAction,String comparator, Money left, Money right) {
        Map<String,Object> valueMap = new HashMap<>();
        valueMap.put(leftAction.getCode(),left.getAmount());
        valueMap.put(rightAction.getCode(),right.getAmount());
        StringBuffer expression = new StringBuffer();
        expression.append(leftAction.getCode()).append(comparator).append(rightAction.getCode());
        //从表字段中进行表达式处理，表字段本身不会缺失，故此处设为false
        return ExpressionEvaluatorUtil.evaluateBooleanExpression(valueMap, expression.toString(),false,null);
    }

    /**
     * 依据transactionGroups解析为assetModelNodeList
     * @param transactionGroups
     * @return
     */
    public List<AssetModelNode> parseTransactionGroups(List<TransactionGroup> transactionGroups) {
        Map<String, AssetModelNode> nodeMap = new HashMap<>();
        try {
            // 第一阶段：索引所有操作节点
            indexAllOperations(transactionGroups, nodeMap);

            // 第二阶段：处理交易关系
            processTransactionRelations(transactionGroups, nodeMap);
        } catch (Exception e) {
            LOGGER.error("初始化AssetModelNodeList失败，参数transactionGroups为:{}", JSON.toJSONString(transactionGroups));
            throw new RuntimeException(e);
        }
        return new ArrayList<>(nodeMap.values());
    }

    private void indexAllOperations(List<TransactionGroup> groups, Map<String, AssetModelNode> nodeMap) {
        for (TransactionGroup group : groups) {
            for (AssetTransaction t : group.getTransactionList()) {
                if (t.isSingleOpType()) {
                    registerSingleOperation(nodeMap, t.getSingleOperation());
                } else {
                    registerNormalOperation(nodeMap, t.getFrom());
                    registerNormalOperation(nodeMap, t.getTo());
                }
            }
        }
    }

    private void registerNormalOperation(Map<String, AssetModelNode> nodeMap, AssetOperation op) {
        if (op == null) return;
        nodeMap.computeIfAbsent(op.getGraphUniqueId(), k -> createNewNode(op));
    }

    private void registerSingleOperation(Map<String, AssetModelNode> nodeMap, AssetOperation op) {
        if (op == null) return;
        AssetModelNode node = nodeMap.computeIfAbsent(op.getGraphUniqueId(), k -> createNewNode(op));

        op.setTableData(null);//特意设置为null
        // 立即处理冻结/解冻分类
        if (op.getActionType() == AssetActionTypeEnum.FREEZE) {
            node.getFreezeSingleOps().add(op);
            node.setFreezeAmount(addMoney(node.getFreezeAmount(), op.getAmount()));
        } else if (op.getActionType() == AssetActionTypeEnum.UNFREEZE) {
            node.getUnfreezeSingleOps().add(op);
            node.setUnfreezeAmount(addMoney(node.getUnfreezeAmount(), op.getAmount()));
        }
    }

    private AssetModelNode createNewNode(AssetOperation op) {
        AssetModelNode node = new AssetModelNode();
        op.setTableData(null);//特意设置为null
        node.setSelfAssetOperation(op);
        return node;
    }

    private void processTransactionRelations(List<TransactionGroup> groups, Map<String, AssetModelNode> nodeMap) {
        for (TransactionGroup group : groups) {
            for (AssetTransaction t : group.getTransactionList()) {
                if (!t.isSingleOpType()) {
                    processNormalTransaction(t, nodeMap);
                }
            }
        }
    }

    private void processNormalTransaction(AssetTransaction t, Map<String, AssetModelNode> nodeMap) {
        AssetOperation from = t.getFrom();
        AssetOperation to = t.getTo();
        Money amount = t.getAmount();

        // 处理流出关系
        if (from != null) {
            AssetModelNode fromNode = nodeMap.get(from.getGraphUniqueId());
            if (fromNode != null) {
                to.setTableData(null);//特意设置为null
                fromNode.getOutFlows().add(to);
                fromNode.setOutAmount(addMoney(fromNode.getOutAmount(), amount));
            }
        }

        // 处理流入关系
        if (to != null) {
            AssetModelNode toNode = nodeMap.get(to.getGraphUniqueId());
            if (toNode != null) {
                from.setTableData(null);//特意设置为null
                toNode.getInFlows().add(from);
                toNode.setInAmount(addMoney(toNode.getInAmount(), amount));
            }
        }
    }

    private Money addMoney(Money original, Money toAdd) {
        // 假设Money类实现安全加法，这里简化为分相加逻辑
        return new Money(original.getCent() + (toAdd != null ? toAdd.getCent() : 0),
                original.getCurrency());
    }

}
