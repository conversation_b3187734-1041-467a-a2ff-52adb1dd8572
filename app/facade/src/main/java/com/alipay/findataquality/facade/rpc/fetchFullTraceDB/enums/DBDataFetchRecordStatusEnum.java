package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums;

/**
 * DB全链路取数记录状态枚举
 */
public enum DBDataFetchRecordStatusEnum {
    //未归档
    UNMARKED("UNMARKED","未归档"),
    //已归档
    MARKED("MARKED","已归档")
    ;

    private String code;
    private String desc;

    DBDataFetchRecordStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DBDataFetchRecordStatusEnum getByCode(String code){
        for (DBDataFetchRecordStatusEnum queryScene : values()) {
            if (queryScene.getCode().equalsIgnoreCase(code)) {
                return queryScene;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}
