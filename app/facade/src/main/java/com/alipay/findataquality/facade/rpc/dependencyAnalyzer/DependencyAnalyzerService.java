package com.alipay.findataquality.facade.rpc.dependencyAnalyzer;

import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model.BklightMockData;
import com.alipay.findataquality.facade.rpc.dependencyAnalyzer.result.DependencyAnalyzerResult;
import java.util.List;

/**
 * 强弱依赖分析接口
 */
public interface DependencyAnalyzerService {

    public DependencyAnalyzerResult analysisDependency(List<BklightMockData> bklightMockDataList);


}
