package com.alipay.findataquality.facade.rpc.fundFlowCheck;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowAnalyzeRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowCheckRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.request.FundFlowDetectRequest;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowAnalyzeResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowCheckResult;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.FundFlowDetectResult;
import com.alipay.zoneclient.zoneapi.annotation.ZonePublish;

/**
 * @ClassName FullTraceDataCheck
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/8 16:00
 * @Version V1.0
 **/
@ZonePublish(zoneType={"GZ"})
public interface FundFlowCheckService {
    /**
     * 资金模型分析
     * @param request
     * @return
     */
    public FundFlowAnalyzeResult analyzeFundFlowGraph(FundFlowAnalyzeRequest request);

    /**
     * 场景识别
     * @param fundFlowDetectRequest，fundFlowAnalyzeRequest
     * @return
     */
    public FundFlowDetectResult detectFundFlow(FundFlowDetectRequest fundFlowDetectRequest,FundFlowAnalyzeRequest fundFlowAnalyzeRequest);

    /**
     * 资金规则检查
     * @param fundFlowCheckRequest
     * @return
     */
    public FundFlowCheckResult checkFundFlow(FundFlowCheckRequest fundFlowCheckRequest);

    /**
     * 资金模型检查
     * @param fundFlowCheckRequest
     * @return
     */
    public FundFlowCheckResult checkFundFlowModel(FundFlowCheckRequest fundFlowCheckRequest);


}
