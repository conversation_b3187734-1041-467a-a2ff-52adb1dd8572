package com.alipay.findataquality.facade.rpc.fundFlowCheck.request;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionGroup;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.BaseResult;

import java.util.List;

/**
 * 依据资金流图的场景识别入参
 */
public class FundFlowDetectRequest extends BaseResult {
    private List<TransactionGroup> transactionGroupList;

    public List<TransactionGroup> getTransactionGroupList() {
        return transactionGroupList;
    }

    public void setTransactionGroupList(List<TransactionGroup> transactionGroupList) {
        this.transactionGroupList = transactionGroupList;
    }
}
