package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.RuleCheckResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.concurrent.Callable;

/**
 * @ClassName AbstractCheckTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/23 05:41
 * @Version V1.0
 **/
public class AbstractRuleCheckTask implements Callable<RuleCheckResult> {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractRuleCheckTask.class);
    /**
     * 当前需要处理的规则方法
     */
    private Method currentFunc;

    private AbstractCheckScene checkScene;
    /**
     * 规则方法的上下文可以，格式为class#method
     */
    private String funcContextKey;
    /**
     * 数据容器
     */
    private TableDataContext dataContext;

    AbstractRuleCheckTask(Method currentFunc, AbstractCheckScene checkScene, TableDataContext dataContext){
        this.currentFunc = currentFunc;
        this.dataContext = dataContext;
        this.checkScene = checkScene;
    }

    @Override
    public RuleCheckResult call(){
        LOGGER.info("开始执行校验任务{}",this.getCheckSceneName());
        long start = System.currentTimeMillis();
        boolean re = preProcess();
        if(!re){
            return null;
        }
        RuleCheckResult result = process();
        AutoCheck checkAnnotation = this.currentFunc.getAnnotation(AutoCheck.class);
        if(result!=null){
            result.setRuleDesc(checkAnnotation.ruleDesc());
            result.setCheckMethod(this.currentFunc.getName());
            result.setCheckScene(this.checkScene.getClass().getSimpleName());
            result.setCheckSceneName(this.checkScene.checkSceneName());
        }
        long end = System.currentTimeMillis();
        postProcess();
        LOGGER.info("校验任务{}执行完成，耗时{}s",this.getCheckSceneName(),(end-start)/1000.0);
        return result;
    }

    private boolean preProcess(){
        return true;
    }

    private void postProcess(){

    }

    protected RuleCheckResult process(){
        try {
            //获取方法参数类型
            Class[] classTypeArray = this.currentFunc.getParameterTypes();
            Object[] params = new Object[classTypeArray.length];
            //从map中获取方法实例
            for (int i = 0; i < classTypeArray.length; i++) {
                params[i]=this.dataContext.getTableDataMapByClass().get(classTypeArray[i]);
            }
            //执行方法
            RuleCheckResult checkResult = (RuleCheckResult) this.currentFunc.invoke(this.checkScene,params);
            return checkResult;
        } catch (InvocationTargetException | IllegalAccessException e) {
            //e.printStackTrace();
            LOGGER.error("RuleCheckTask执行异常,e={}",e);
        }
        return null;
    }

    /**
     * 获取校验方法名
     * @return
     */
    public String getCheckSceneName(){
        return this.checkScene.getClass().getSimpleName()+"#"+this.currentFunc.getName();
    }
}
