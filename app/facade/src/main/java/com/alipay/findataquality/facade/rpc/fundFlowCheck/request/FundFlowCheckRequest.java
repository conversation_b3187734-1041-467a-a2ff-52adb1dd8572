package com.alipay.findataquality.facade.rpc.fundFlowCheck.request;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;

import java.util.List;

/**
 * @ClassName FundFlowCheckRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 16:25
 * @Version V1.0
 **/
public class FundFlowCheckRequest extends BaseRequest{
    private String dataJson;
    private String shareCode;
    List<TransactionStageScene> transactionStageScenes;

    public String getDataJson() {
        return dataJson;
    }

    public void setDataJson(String dataJson) {
        this.dataJson = dataJson;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public List<TransactionStageScene> getTransactionStageScenes() {
        return transactionStageScenes;
    }

    public void setTransactionStageScenes(List<TransactionStageScene> transactionStageScenes) {
        this.transactionStageScenes = transactionStageScenes;
    }

    @Override
    public String toString() {
        return "{"
                + "\"dataJson\":\""
                + dataJson + '\"'

                + ",\"shareCode\":\""
                + shareCode + '\"'

                + ",\"transactionStageScenes\":"
                + transactionStageScenes

                + "},\"super-FundFlowCheckRequest\":" + super.toString() + "}";

    }
}
