package com.alipay.findataquality.facade.rpc.fundFlowCheck.request;

/**
 * @ClassName FundFlowCheckRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/3 16:25
 * @Version V1.0
 **/
public class FundFlowAnalyzeRequest extends BaseRequest{
    private String dataJson;
    private String shareCode;
    private boolean recognizeScene = false;

    public String getDataJson() {
        return dataJson;
    }

    public void setDataJson(String dataJson) {
        this.dataJson = dataJson;
    }

    public String getShareCode() {
        return shareCode;
    }

    public void setShareCode(String shareCode) {
        this.shareCode = shareCode;
    }

    public boolean isRecognizeScene() {
        return recognizeScene;
    }

    public void setRecognizeScene(boolean recognizeScene) {
        this.recognizeScene = recognizeScene;
    }
}
