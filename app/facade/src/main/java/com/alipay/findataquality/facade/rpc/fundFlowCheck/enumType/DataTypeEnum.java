package com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType;

/**
 * @ClassName InstTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/18 18:47
 * @Version V1.0
 **/
public enum DataTypeEnum {
    /**
     * 数据类型
     */
    TRADE("TRADE","交易"),
    LOG("LOG","日志"),
    ACCOUNT("ACCOUNT","账户"),
    BALANCE("BALANCE","余额");

    private String code;
    private String desc;

    DataTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
