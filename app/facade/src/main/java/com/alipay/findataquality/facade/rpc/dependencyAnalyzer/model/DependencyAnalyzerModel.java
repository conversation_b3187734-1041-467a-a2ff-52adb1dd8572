package com.alipay.findataquality.facade.rpc.dependencyAnalyzer.model;

/**
 * 强弱依赖分析模型
 */
public class DependencyAnalyzerModel {

    /*
     * 用例id
     */
    String caseId;
    /*
     * 场景id
     */
    String sceneId;

    /**
     * 场景大类
     */
    String sceneClassType;
    /**
     * 场景名称
     */
    String sceneName;

    String abnormalDetail;
    /*
     * 用例名称
     */
    String caseName;

    /**
     * bakery展示链接
     */
    String bakeryUrl;

    /**
     * bakery静态图链接
     */
    String bakeryStaticPicUrl;

    //强依赖、弱依赖 的d
    String dependencyType;

    //强弱依赖判断依据
    String judgmentInfo;

    //强弱依赖判断是否有效（正确）:T-有效，F-无效
    boolean valid;

    //是否是对照组：即是否是标准非MOCK数据
    boolean controlGroup;

    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }

    public String getSceneId() {
        return sceneId;
    }

    public void setSceneId(String sceneId) {
        this.sceneId = sceneId;
    }

    public String getSceneClassType() {
        return sceneClassType;
    }

    public void setSceneClassType(String sceneClassType) {
        this.sceneClassType = sceneClassType;
    }

    public String getSceneName() {
        return sceneName;
    }

    public void setSceneName(String sceneName) {
        this.sceneName = sceneName;
    }

    public String getAbnormalDetail() {
        return abnormalDetail;
    }

    public void setAbnormalDetail(String abnormalDetail) {
        this.abnormalDetail = abnormalDetail;
    }

    public String getCaseName() {
        return caseName;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }

    public String getBakeryUrl() {
        return bakeryUrl;
    }

    public void setBakeryUrl(String bakeryUrl) {
        this.bakeryUrl = bakeryUrl;
    }

    public String getBakeryStaticPicUrl() {
        return bakeryStaticPicUrl;
    }

    public void setBakeryStaticPicUrl(String bakeryStaticPicUrl) {
        this.bakeryStaticPicUrl = bakeryStaticPicUrl;
    }

    public String getDependencyType() {
        return dependencyType;
    }

    public void setDependencyType(String dependencyType) {
        this.dependencyType = dependencyType;
    }

    public String getJudgmentInfo() {
        return judgmentInfo;
    }

    public void setJudgmentInfo(String judgmentInfo) {
        this.judgmentInfo = judgmentInfo;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public boolean isControlGroup() {
        return controlGroup;
    }

    public void setControlGroup(boolean controlGroup) {
        this.controlGroup = controlGroup;
    }
}
