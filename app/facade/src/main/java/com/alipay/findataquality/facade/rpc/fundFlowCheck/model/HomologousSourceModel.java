/**
 * Alipay.com Inc.
 * Copyright (c) 2004-2025 All Rights Reserved.
 */
package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @version : FundFlowHomoCheckResult.java, v 0.1 2025年03月17日 10:20 zhaolinling Exp $
 */
public class HomologousSourceModel {
    private String dbName;
    private String tableName;
    private Long baselineId;
    private String columnIndex;//列下标
    private JSONObject columnData;
    private JSONObject checkResult;
    private List<String> valueIndexList;//异常字段的下标

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public Long getBaselineId() {
        return baselineId;
    }

    public void setBaselineId(Long baselineId) {
        this.baselineId = baselineId;
    }

    public String getColumnIndex() {
        return columnIndex;
    }

    public void setColumnIndex(String columnIndex) {
        this.columnIndex = columnIndex;
    }

    public JSONObject getColumnData() {
        return columnData;
    }

    public void setColumnData(JSONObject columnData) {
        this.columnData = columnData;
    }

    public JSONObject getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(JSONObject checkResult) {
        this.checkResult = checkResult;
    }

    public List<String> getValueIndexList() {
        return valueIndexList;
    }

    public void setValueIndexList(List<String> valueIndexList) {
        this.valueIndexList = valueIndexList;
    }
}
