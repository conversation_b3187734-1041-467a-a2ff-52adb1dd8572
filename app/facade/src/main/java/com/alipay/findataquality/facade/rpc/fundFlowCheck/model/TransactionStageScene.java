package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.iwallet.biz.common.util.money.Money;

import java.io.Serializable;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum.*;

/**
 * 交易阶段所命名的场景
 */
public class TransactionStageScene implements Serializable {

    private static final long serialVersionUID = -7874603209653591210L;

    FundFlowStageEnum fundFlowStageEnum;

    Money amount;

    int nextStartIndex;

    public FundFlowStageEnum getFundFlowStageEnum() {
        return fundFlowStageEnum;
    }

    public void setFundFlowStageEnum(FundFlowStageEnum fundFlowStageEnum) {
        this.fundFlowStageEnum = fundFlowStageEnum;
    }

    public Money getAmount() {
        return amount;
    }

    public void setAmount(Money amount) {
        this.amount = amount;
    }

    public int getNextStartIndex() {
        return nextStartIndex;
    }

    public void setNextStartIndex(int nextStartIndex) {
        this.nextStartIndex = nextStartIndex;
    }

    @Override
    public String toString() {
        return "{"
                + "\"fundFlowStageEnum\":"
                + fundFlowStageEnum

                + ",\"amount\":"
                + amount

                + "}";

    }
}
