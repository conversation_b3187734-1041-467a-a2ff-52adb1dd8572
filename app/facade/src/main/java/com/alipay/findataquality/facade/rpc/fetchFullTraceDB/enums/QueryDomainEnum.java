package com.alipay.findataquality.facade.rpc.fetchFullTraceDB.enums;

/**
 * @ClassName QueryDBEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/17 15:19
 * @Version V1.0
 **/
public enum QueryDomainEnum {

    /** 余额宝 */
    YEB("YEB", "余额宝"),

    /** 基金 */
    FUND("FUND", "基金"),

    /** ֤证券 */
    STOCK("STOCK", "֤证券"),

    /** 运营和开放平台 */
    PROMO("PROMO", "运营和开放平台"),

    /** 场景理财 */
    CJLC("CJLC", "场景理财"),

    /** 行情 */
    QUOTATION("QUOTATION", "行情"),

    /** 定期 */
    DINGQI("DINGQI", "定期"),

    /**资产 */
    ASSET("ASSET", "资产"),

    /**
     * 资金平台
     */
    FINANCE_ASSET("FINANCE_ASSET", "资金平台"),

    /**
     * 计收费
     */
    CHARGE("CHARGE", "计收费"),

    /**
     * 资产账
     */
    FAASSET("FAASSET", "资产账");

    private String code;
    private String desc;

    private QueryDomainEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
