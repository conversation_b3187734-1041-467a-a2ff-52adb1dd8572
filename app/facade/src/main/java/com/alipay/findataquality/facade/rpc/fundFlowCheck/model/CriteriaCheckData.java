package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

public class CriteriaCheckData {

    /**
     * 字段名，对应oriTableData.columnName中的某个值
     */
    String dataName;
    /**
     * 字段名对应的值，对应oriTableData.columnData中的指定dataName的值
     */
    String dataValue;

    int tableNameIndex;//dataValue所在的表在tableList上的具体坐标,举例：yeb_asset_decrease_order在tableList中的索引是0，则此值为0
    /**
     * 字段名对应的值的index，对应dataValue所在字段在oriTableData.columnData中横纵坐标，举例：real_amount在columnData中横纵坐标为0，7（与dataName在columnName中的索引匹配），，则此值为[0,7]
     */
    int[] dataLocatedIndex;
    /**
     * 字段名所检索的tableData
     */
    TableData oriTableData;

    //数据库
    private String dbName;
    //数据表
    private String tableName;
    //字段名
    private String columnName;
    //字段值所属行，从0开始
    private int rowIndex;
    //字段值
    private String columnValue;

    public String getDataName() {
        return dataName;
    }

    public void setDataName(String dataName) {
        this.dataName = dataName;
    }

    public String getDataValue() {
        return dataValue;
    }

    public void setDataValue(String dataValue) {
        this.dataValue = dataValue;
    }

    public int getTableNameIndex() {
        return tableNameIndex;
    }

    public void setTableNameIndex(int tableNameIndex) {
        this.tableNameIndex = tableNameIndex;
    }

    public int[] getDataLocatedIndex() {
        return dataLocatedIndex;
    }

    public void setDataLocatedIndex(int[] dataLocatedIndex) {
        this.dataLocatedIndex = dataLocatedIndex;
    }

    public TableData getOriTableData() {
        return oriTableData;
    }

    public void setOriTableData(TableData oriTableData) {
        this.oriTableData = oriTableData;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public int getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(int rowIndex) {
        this.rowIndex = rowIndex;
    }

    public String getColumnValue() {
        return columnValue;
    }

    public void setColumnValue(String columnValue) {
        this.columnValue = columnValue;
    }
}
