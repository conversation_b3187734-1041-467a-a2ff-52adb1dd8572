package com.alipay.findataquality.facade.rpc.fundFlowCheck.utils;

import com.alibaba.fastjson.JSONObject;
import com.alipay.sofa.common.utils.StringUtil;
import com.iwallet.biz.common.util.money.Money;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName StringUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/27 17:18
 * @Version V1.0
 **/
public class CheckUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckUtil.class.getSimpleName());

    /**
     * 比较两个整数是否相等，如果有一个为空，则返回false
     * @param a
     * @param b
     * @return
     */
    public static boolean checkIntEqual(Integer a,Integer b){
        if(a==null||b==null){
            return false;
        }
        return a==b;
    }

    public static <T> boolean checkListEqual(List<T> list1, List<T> list2) {
        if(list1==null||list2==null||list1.isEmpty()||list2.isEmpty()){
            return false;
        }
        // 如果两个列表的长度不一致，直接返回false
        if (list1.size() != list2.size()) {
            return false;
        }

        // 使用HashMap来记录每个元素的出现次数
        Map<T, Integer> countMap1 = new HashMap<>();
        Map<T, Integer> countMap2 = new HashMap<>();

        // 记录list1中每个元素的出现次数
        for (T element : list1) {
            countMap1.put(element, countMap1.getOrDefault(element, 0) + 1);
        }

        // 记录list2中每个元素的出现次数
        for (T element : list2) {
            countMap2.put(element, countMap2.getOrDefault(element, 0) + 1);
        }

        // 比较两个HashMap是否相等
        return countMap1.equals(countMap2);
    }


    /**
     * 去除字符串末尾的以_结尾的数字，例如table_02_03,则返回table
     * @param str
     * @return
     */
    public static String removeTrailingNumbers(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        int index = str.lastIndexOf('_');
        while (index != -1 && index < str.length() - 1) {
            String trailing = str.substring(index + 1);
            if (trailing.matches("\\d+")) {
                str = str.substring(0, index);
                index = str.lastIndexOf('_');
            } else {
                break;
            }
        }
        return str;
    }

    public static Date parseDateTime(String dateTime) {
        //精确到毫秒
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        try {
            return dateFormat.parse(dateTime);
        } catch (ParseException e) {
            LOGGER.error("parseDateTime字符串转换成日期格式错误,string={},e={}",dateTime,e);
        }
        return null;
    }

    public static Money createYuanByCent(String amount) {
        //精确到毫秒
        try {
            return new Money(Double.valueOf(amount)/100.00);
        } catch (NumberFormatException e) {
            LOGGER.error("字符串转换成money格式错误,string={},e={}",amount,e);
        }
        return null;
    }

    /**
     * 从json数据中获取指定key的值
     * @param jsonData
     * @param key
     * @param CaseSensitive
     * @return
     */
    public static String getJsonValue(String jsonData, String key, boolean CaseSensitive){
        if(StringUtil.isBlank(jsonData)||StringUtil.isBlank(key)){
            return null;
        }
        try{
            //将String数据转换为Json格式
            JSONObject obj = (JSONObject)JSONObject.parse(jsonData);
            if (!CaseSensitive) {
                Set<String> keys = obj.keySet();
                for (String objKey:keys) {
                    if(key.equalsIgnoreCase(objKey)){
                        return obj.get(objKey).toString();
                    }
                }
                return null;
            }else{
                Object valueNode = obj.get(key);
                return valueNode == null ? null : valueNode.toString();
            }
        }catch (Exception e){
            return null;
        }
    }
}
