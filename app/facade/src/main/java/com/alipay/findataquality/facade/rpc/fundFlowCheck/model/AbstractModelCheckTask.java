package com.alipay.findataquality.facade.rpc.fundFlowCheck.model;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AssetModelCheckTypeEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.AutoCheck;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.result.ModelCheckResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.Callable;

/**
 * @ClassName AbstractCheckTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/23 05:41
 * @Version V1.0
 **/
public class AbstractModelCheckTask implements Callable<List<ModelCheckResult>> {
    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractModelCheckTask.class);
    /**
     * 当前需要处理的规则方法
     */
    private Method currentFunc;

    private AbstractModelCheckScene checkScene;
    /**
     * 规则方法的上下文可以，格式为class#method
     */
    private String funcContextKey;

    /**
     * 资金模型检测类型
     */
    private AssetModelCheckTypeEnum assetModelCheckTypeEnum;

    /**
     * 数据容器
     */
    private TableDataContext dataContext;

    AbstractModelCheckTask(Method currentFunc, AbstractModelCheckScene checkScene, TableDataContext dataContext,AssetModelCheckTypeEnum assetModelCheckTypeEnum){
        this.currentFunc = currentFunc;
        this.dataContext = dataContext;
        this.checkScene = checkScene;
        this.assetModelCheckTypeEnum=assetModelCheckTypeEnum;
    }

    @Override
    public List<ModelCheckResult> call(){
        LOGGER.info("开始执行校验任务{}",this.getCheckSceneName());
        long start = System.currentTimeMillis();
        boolean re = preProcess();
        if(!re){
            return null;
        }
        List<ModelCheckResult> modelCheckResultList = process();
        AutoCheck checkAnnotation = this.currentFunc.getAnnotation(AutoCheck.class);
        for (ModelCheckResult result:modelCheckResultList){
            if(result!=null){
                result.setRuleDesc(checkAnnotation.ruleDesc());
                result.setCheckMethod(this.currentFunc.getName());
                result.setCheckScene(this.checkScene.getClass().getSimpleName());
                result.setCheckSceneName(this.checkScene.checkSceneName());
            }
        }
        long end = System.currentTimeMillis();
        postProcess();
        LOGGER.info("校验任务{}执行完成，耗时{}s",this.getCheckSceneName(),(end-start)/1000.0);
        return modelCheckResultList;
    }

    private boolean preProcess(){
        return true;
    }

    private void postProcess(){

    }

    protected List<ModelCheckResult> process(){
        try {
            //获取方法参数类型
            Class[] classTypeArray = this.currentFunc.getParameterTypes();
            Object[] params = new Object[classTypeArray.length];
            //资金模型检查处理结果，类型为List
            List<ModelCheckResult> modelCheckResultList = new LinkedList<>();
            List<TransactionGroup> graphTransactionGroupList = this.dataContext.getTransactionGroupList();
            List<AssetModelNode> graphAssetModelNodeList = this.checkScene.parseTransactionGroups(graphTransactionGroupList);//解析单节点列表

            switch (this.assetModelCheckTypeEnum){
                case MAIN_GRAPH: //处理总图
                    if (graphTransactionGroupList!=null && graphTransactionGroupList.size()>0){
                        params[0] = graphTransactionGroupList;//总图资金模型检测的入参的第1个参数为:总图mainGraphtransactionGroupList
                        //执行方法
                        ModelCheckResult mainGraphCheckResult =  (ModelCheckResult) this.currentFunc.invoke(this.checkScene,params);
                        mainGraphCheckResult.setAssetModelCheckType(this.assetModelCheckTypeEnum.getCode());
                        modelCheckResultList.add(mainGraphCheckResult);
                    }else{
                        LOGGER.error("主图执行异常,transactionGroupList为空");
                    }
                    break;
                case SUB_GRAPH://处理分图：依次检测分图
                    if (graphTransactionGroupList!=null && graphTransactionGroupList.size()>0){
                        for (TransactionGroup transactionGroup : graphTransactionGroupList){
                            params[0] = transactionGroup;//分图资金模型检测的入参的第1个参数为:subGraphTransactionGroup
                            //执行方法
                            ModelCheckResult subGraphCheckResult =  (ModelCheckResult) this.currentFunc.invoke(this.checkScene,params);
                            subGraphCheckResult.setAssetModelCheckType(this.assetModelCheckTypeEnum.getCode());
                            modelCheckResultList.add(subGraphCheckResult);
                        }
                    }else{
                        LOGGER.error("分图执行异常,transactionGroupList为空");
                    }
                    break;
                case SINGLE_NODE:
                    if (graphAssetModelNodeList!=null && graphAssetModelNodeList.size()>0){
                        //处理总图单节点
                        LOGGER.info("总图，单节点共：{}个",graphAssetModelNodeList.size());
                        for (AssetModelNode assetModelNode : graphAssetModelNodeList){
                            LOGGER.info("总图，assetModelNode：{}",assetModelNode);
                            params[0] = assetModelNode;//单节点资金模型检测的入参只有1个:assetModelNode
                            //执行方法
                            ModelCheckResult nodeCheckResult =  (ModelCheckResult) this.currentFunc.invoke(this.checkScene,params);
                            nodeCheckResult.setAssetModelCheckType(this.assetModelCheckTypeEnum.getCode());
                            modelCheckResultList.add(nodeCheckResult);
                        }
//                        //处理分图单节点
//                        int i = 1;
//                        for (TransactionGroup transactionGroup : graphTransactionGroupList){
//                            List<TransactionGroup> subGraphTransactionGroupList = new ArrayList<>();
//                            subGraphTransactionGroupList.add(transactionGroup);
////                            params[0] = subGraphTransactionGroupList;//分图资金模型检测的入参的第1个参数为:subGraphTransactionGroup
//                            List<AssetModelNode> subGraphAssetModelNodeList = this.checkScene.parseTransactionGroups(subGraphTransactionGroupList);//解析分图单节点列表
//                            LOGGER.info("分图{},单节点共：{}个",i,subGraphAssetModelNodeList.size());
//                            for (AssetModelNode subGraphAssetModelNode : subGraphAssetModelNodeList){
//                                LOGGER.info("分图，subGraphAssetModelNode：{}",subGraphAssetModelNode);
//                                params[0] = subGraphAssetModelNode;//单节点资金模型检测的入参只有1个:assetModelNode
//                                //执行方法
//                                ModelCheckResult nodeCheckResult =  (ModelCheckResult) this.currentFunc.invoke(this.checkScene,params);
////                                nodeCheckResult.setAssetModelCheckType(this.assetModelCheckTypeEnum.getCode()+"#分图"+i);
//                                nodeCheckResult.setAssetModelCheckType(this.assetModelCheckTypeEnum.getCode());
//                                modelCheckResultList.add(nodeCheckResult);
//                            }
//                            i++;
//                        }
                    }else{
                        LOGGER.error("单节点执行异常,assetModelNodeList为空");
                    }
                    break;
                default:
                    break;
            }
            return modelCheckResultList;
        } catch (InvocationTargetException | IllegalAccessException e) {
            //e.printStackTrace();
            LOGGER.error("RuleCheckTask执行异常,e={}",e);
        }
        return null;
    }

    /**
     * 获取校验方法名
     * @return
     */
    public String getCheckSceneName(){
        return this.checkScene.getClass().getSimpleName()+"#"+this.currentFunc.getName();
    }

}
