package com.alipay.findataquality.utils.common;

import com.alipay.findataquality.facade.rpc.fundFlowCheck.enumType.FundFlowStageEnum;
import com.alipay.findataquality.facade.rpc.fundFlowCheck.model.TransactionStageScene;
import com.iwallet.biz.common.util.money.Money;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public class AmountUtil {

    public static void main(String[] args) {
//        List<TransactionStageScene> transactionStageSceneList = new LinkedList<>();
//        TransactionStageScene transactionStageScene1 = new TransactionStageScene();
//        transactionStageScene1.setFundFlowStageEnum(FundFlowStageEnum.BANK_TO_YEB);
//        transactionStageScene1.setAmount(new Money(100));
//        transactionStageSceneList.add(transactionStageScene1);
//
//        TransactionStageScene transactionStageScene2 = new TransactionStageScene();
//        transactionStageScene2.setFundFlowStageEnum(FundFlowStageEnum.COUPON_TO_YEB);
//        transactionStageScene2.setAmount(new Money(200));
//        transactionStageSceneList.add(transactionStageScene2);
//        test(transactionStageSceneList);
//
//        for (TransactionStageScene transactionStageScene:transactionStageSceneList){
//            System.out.println(transactionStageScene.toString());
//        }

    }

    /**
     * String类型的分转换成Money类型
     * @param centStr
     * @return
     */
    public static Money transferAmountFromCentT0Yuan(String centStr){
        BigDecimal centsValue = new BigDecimal(centStr);
        BigDecimal yuanValue = centsValue.divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
        return new Money(yuanValue);
    }

    public static BigDecimal addBigDecimalValue(String value1,String value2){
        BigDecimal bigDecimalValue1 = new BigDecimal(value1);
        BigDecimal bigDecimalValue2 = new BigDecimal(value2);
        return bigDecimalValue1.add(bigDecimalValue2);
    }

    public static void test(List<TransactionStageScene> transactionStageSceneList ){
        for (TransactionStageScene transactionStageScene:transactionStageSceneList){
            if (transactionStageScene.getFundFlowStageEnum().getFlow().equals(FundFlowStageEnum.BANK_TO_YEB.getFlow())){
                transactionStageScene.setFundFlowStageEnum(FundFlowStageEnum.FUND_DUMMY_TO_YEB_CANCEL);
            }
        }

    }
}
