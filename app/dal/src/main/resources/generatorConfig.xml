<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!-- context:逆向工程主要配置信息
            id:名称
            defaultModelType:生成Model类型
                conditional:类似hierarchical
                flag:单表单Model
                hierarchical:主键生成XxxKey,Blod等单独生成,其他简单属性一个对象
            targetRuntime:设置生成的文件适用mybatis具体版本
                MyBatis3(default)
                MyBatis3Simple:无Example内容
    -->
    <context id="default" defaultModelType="flat" targetRuntime="MyBatis3Simple">
        <!-- 自动识别数据库关键字,默认false -->
        <!--        <property name="autoDelimitKeywords" value="true"/>-->
        <!-- 格式化java代码 -->
        <!--        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>-->
        <!-- 格式化XML代码 -->
        <!--        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>-->

        <!-- 生成的Java文件的编码 -->
        <property name="javaFileEncoding" value="UTF-8"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>

<!--        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin" />-->
<!--        &lt;!&ndash; 配置内置对象序列号接口 &ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>-->
<!--        &lt;!&ndash; 配置内置对象toString方法生成&ndash;&gt;-->
<!--        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>-->

        <commentGenerator >
            <!-- 是否去除自动生成日期的注释 -->
            <property name="suppressDate" value="true"/>
            <!-- 是否去除所有自动生成的注释 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!-- jdbc connect setting -->
        <!-- 这里用的测试库的配置，禁止外泄，实际使用时请替换成您的数据库的链接、用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************"
                        userId="ant_dev_g:ant_fp_1490:finecorisk"
                        password="vv66BuWz" />
        <!-- 类型处理器（可选） -->
        <javaTypeResolver>
            <!-- 是否强制Decimal和Numeric类型转换为BigDecimal,默认值false
                精度 > 0 || length > 18 -> java.math.BigDecimal
                精度 = 0 && 10 <= length <= 18 -> java.lang.Long
                精度 = 0 && 5 <= length <= 10 -> java.lang.Integer
                精度 = 0 && length < 5 -> java.lang.Short
             -->
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- Model生成
                targetPackage:生成实体类所在包
                targetProject:生成实体类所在硬盘位置
         -->
        <javaModelGenerator targetPackage="com.alipay.findataquality.dal.mybatis.model"
                            targetProject="src/main/java">
            <!-- 是否允许子包 -->
            <property name="enableSubPackages" value="true"/>
            <!-- 是否使用构造方法入参,默认false -->
            <!--            <property name="constructorBased" value="true"/>-->
            <!-- 是否清理从数据库中查询出的字符串左右两边的空白字符,默认false -->
            <!--            <property name="trimStrings" value="false"/>-->
            <!-- 建立modal对象属性是否不可改变 即生成的modal对象不会有setter方法，只有构造方法 -->
            <!--            <property name="immutable" value="false"/>-->
        </javaModelGenerator>
        <!-- SqlMap生成，如果使用 mapper + xml 需要添加该配置 -->

        <!-- <sqlMapGenerator targetPackage="sql" -->
        <!--                  targetProject="src/main/resources"> -->
        <!--    <property name="enableSubPackages" value="true"/> -->
        <!-- </sqlMapGenerator> -->

        <!-- Mappers生成
            type:预定义Mapper生成器
                Mybatis3/Mybatis3Simple:
                    ANNOTATEDMAPPER:基于注解Mapper接口，无XML
                    MIXEDMAPPER：XML与注解混合式形式
                    XMLMAPPER：XML形式
         -->
        <javaClientGenerator type="ANNOTATEDMAPPER" targetPackage="com.alipay.findataquality.dal.mybatis.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!-- 表映射
                tableName:数据库对应表名,生成全部表则使用%,支持SQL通配符匹配多个表
                domainObjectName:生成Java实体类类名
         -->
        <table tableName="full_trace_same_origin_validate_baseline" domainObjectName="single.FullTraceSameOriginValidateBaselineDO">
            <property name="runtimeTableName" value="full_trace_same_origin_validate_baseline" />
            <!-- 启用生成扩展Mapper接口 -->
            <property name="rootInterface" value="com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceSameOriginValidateBaselineDOExtendMapper" />
        </table>

        <table tableName="full_trace_validate_data_fetch_record" domainObjectName="single.FullTraceValidateDataFetchRecordDO">
            <property name="runtimeTableName" value="full_trace_validate_data_fetch_record" />
            <!-- 启用生成扩展Mapper接口 -->
            <property name="rootInterface" value="com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceValidateDataFetchRecordDOExtendMapper" />
        </table>

        <table tableName="full_trace_same_origin_not_core_config" domainObjectName="single.FullTraceSameOriginNotCoreDO">
            <property name="runtimeTableName" value="full_trace_same_origin_not_core_config" />
            <!-- 启用生成扩展Mapper接口 -->
            <property name="rootInterface" value="com.alipay.findataquality.dal.mybatis.mapper.single.FullTraceSameOriginNotCoreDOExtendMapper" />
        </table>

        <!-- 单表 示例 ,id用于在插入对象时返回主键 id -->
        <!--
        <table tableName="student" domainObjectName="single.StudentDO">
            <generatedKey column="id" sqlStatement="SELECT LAST_INSERT_ID() as id" type="post" identity="true" />
        </table>
        -->

        <!-- 分表(分表键和主键合一的场景 id为分表键) 示例-->
        <!-- <table tableName="sharding_student_00" domainObjectName="sharding.ShardingStudentDO">
            <property name="runtimeTableName" value="sharding_student" />
        </table>
        -->
        <!-- 分表(分表键和主键分开的场景 number为分表键) 示例-->
        <!-- 由于主键和分表键分离 自动生成的 SQL 不适用需要自定义 -->
        <!--
        <table tableName="sharding_teacher_00"
               domainObjectName="sharding.ShardingTeacherDO"
               enableDeleteByPrimaryKey="false"
               enableSelectByPrimaryKey="false"
               enableUpdateByPrimaryKey="false"
        >
            <property name="runtimeTableName" value="sharding_teacher" />
        </table>
        -->
    </context>
</generatorConfiguration>