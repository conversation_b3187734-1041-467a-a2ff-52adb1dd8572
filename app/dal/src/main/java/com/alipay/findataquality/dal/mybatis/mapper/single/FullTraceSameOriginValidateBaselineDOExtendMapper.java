package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginValidateBaselineDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * 扩展 MyBatis Generator 自动生成的 Mapper
 */
public interface FullTraceSameOriginValidateBaselineDOExtendMapper extends FullTraceSameOriginValidateBaselineDOMapper{
    
    /**
     * 根据 business_region、table_name 和 business_code 查询记录
     * 
     * @param businessRegion 业务区域
     * @param tableName 表名
     * @param businessCode 业务代码
     * @return 包含所有字段的记录列表
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, data_source, business_region, db_name, table_name, ",
        "business_code, ignore_columns, operator, operator_id, unique_md5, standard_check_source",
        "from full_trace_same_origin_validate_baseline",
        "where business_region = #{businessRegion,jdbcType=VARCHAR}",
        "and table_name = #{tableName,jdbcType=VARCHAR}",
        "and business_code = #{businessCode,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="data_source", property="dataSource", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_code", property="businessCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="ignore_columns", property="ignoreColumns", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="unique_md5", property="uniqueMd5", jdbcType=JdbcType.VARCHAR),
        @Result(column="standard_check_source", property="standardCheckSource", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<FullTraceSameOriginValidateBaselineDO> selectByBusinessRegionAndTableNameAndBusinessCode(
        @Param("businessRegion") String businessRegion,
        @Param("tableName") String tableName,
        @Param("businessCode") String businessCode
    );
}
