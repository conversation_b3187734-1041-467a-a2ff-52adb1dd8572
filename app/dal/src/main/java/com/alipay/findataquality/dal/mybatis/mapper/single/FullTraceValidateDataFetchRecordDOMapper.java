package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceValidateDataFetchRecordDO;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

public interface FullTraceValidateDataFetchRecordDOMapper {
    @Delete({
        "delete from full_trace_validate_data_fetch_record",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into full_trace_validate_data_fetch_record (",
        "scene_code, ",
        "operator, fetch_time, ",
        "status, entrance_code, ",
        "share_code, env, ",
        "ext_info, operator_id, ",
        "content, mark_content, ",
        "key_data, key_data_summary)",
        "values (#{sceneCode,jdbcType=VARCHAR}, ",
        "#{operator,jdbcType=VARCHAR}, #{fetchTime,jdbcType=TIMESTAMP}, ",
        "#{status,jdbcType=VARCHAR}, #{entranceCode,jdbcType=VARCHAR}, ",
        "#{shareCode,jdbcType=VARCHAR}, #{env,jdbcType=VARCHAR}, ",
        "#{extInfo,jdbcType=VARCHAR}, #{operatorId,jdbcType=VARCHAR}, ",
        "#{content,jdbcType=LONGVARCHAR}, #{markContent,jdbcType=LONGVARCHAR}, ",
        "#{keyData,jdbcType=LONGVARCHAR}, #{keyDataSummary,jdbcType=LONGVARCHAR})"
    })
    Long insert(FullTraceValidateDataFetchRecordDO record);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, scene_code, operator, fetch_time, status, entrance_code, ",
        "share_code, env, ext_info, operator_id, content, mark_content, key_data, key_data_summary",
        "from full_trace_validate_data_fetch_record",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="scene_code", property="sceneCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="fetch_time", property="fetchTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="status", property="status", jdbcType=JdbcType.VARCHAR),
        @Result(column="entrance_code", property="entranceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="share_code", property="shareCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="env", property="env", jdbcType=JdbcType.VARCHAR),
        @Result(column="ext_info", property="extInfo", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="content", property="content", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="mark_content", property="markContent", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data", property="keyData", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data_summary", property="keyDataSummary", jdbcType=JdbcType.LONGVARCHAR)
    })
    FullTraceValidateDataFetchRecordDO selectByPrimaryKey(Long id);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, scene_code, operator, fetch_time, status, entrance_code, ",
        "share_code, env, ext_info, operator_id, content, mark_content, key_data, key_data_summary",
        "from full_trace_validate_data_fetch_record"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="scene_code", property="sceneCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="fetch_time", property="fetchTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="status", property="status", jdbcType=JdbcType.VARCHAR),
        @Result(column="entrance_code", property="entranceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="share_code", property="shareCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="env", property="env", jdbcType=JdbcType.VARCHAR),
        @Result(column="ext_info", property="extInfo", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="content", property="content", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="mark_content", property="markContent", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data", property="keyData", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data_summary", property="keyDataSummary", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<FullTraceValidateDataFetchRecordDO> selectAll();

    @Update({
        "update full_trace_validate_data_fetch_record",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "scene_code = #{sceneCode,jdbcType=VARCHAR},",
          "operator = #{operator,jdbcType=VARCHAR},",
          "fetch_time = #{fetchTime,jdbcType=TIMESTAMP},",
          "status = #{status,jdbcType=VARCHAR},",
          "entrance_code = #{entranceCode,jdbcType=VARCHAR},",
          "share_code = #{shareCode,jdbcType=VARCHAR},",
          "env = #{env,jdbcType=VARCHAR},",
          "ext_info = #{extInfo,jdbcType=VARCHAR},",
          "operator_id = #{operatorId,jdbcType=VARCHAR},",
          "content = #{content,jdbcType=LONGVARCHAR},",
          "mark_content = #{markContent,jdbcType=LONGVARCHAR},",
          "key_data = #{keyData,jdbcType=LONGVARCHAR},",
          "key_data_summary = #{keyDataSummary,jdbcType=LONGVARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(FullTraceValidateDataFetchRecordDO record);
}