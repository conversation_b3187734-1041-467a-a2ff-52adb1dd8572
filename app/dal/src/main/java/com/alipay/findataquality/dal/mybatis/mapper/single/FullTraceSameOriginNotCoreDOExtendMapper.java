package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginNotCoreDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

public interface FullTraceSameOriginNotCoreDOExtendMapper extends FullTraceSameOriginNotCoreDOMapper{

    @Select({
        "select",
        "id, gmt_create, gmt_modified, business_region, db_name, table_name, not_core_columns",
        "from full_trace_same_origin_not_core_config",
        "where business_region = #{businessRegion,jdbcType=VARCHAR}",
        "and db_name = #{dbName,jdbcType=VARCHAR}",
        "and table_name = #{tableName,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="not_core_columns", property="notCoreColumns", jdbcType=JdbcType.VARCHAR)
    })
    FullTraceSameOriginNotCoreDO selectByBusinessRegionAndDbNameAndTableName(@Param("businessRegion") String businessRegion,
                                                                             @Param("dbName") String dbName,
                                                                             @Param("tableName") String tableName
    );

}