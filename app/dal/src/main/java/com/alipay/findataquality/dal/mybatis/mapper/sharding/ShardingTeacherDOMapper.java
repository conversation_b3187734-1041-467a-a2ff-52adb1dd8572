package com.alipay.findataquality.dal.mybatis.mapper.sharding;

import com.alipay.findataquality.dal.mybatis.model.sharding.ShardingTeacherDO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * MyBatis Generator 自动生成的 SQL
 */
public interface ShardingTeacherDOMapper {
    @Insert({
        "insert into sharding_teacher (id, number, ",
        "name, submission_date)",
        "values (#{id,jdbcType=INTEGER}, #{number,jdbcType=VARCHAR}, ",
        "#{name,jdbcType=VARCHAR}, #{submissionDate,jdbcType=DATE})"
    })
    int insert(ShardingTeacherDO record);

    @Select({
        "select",
        "id, number, name, submission_date",
        "from sharding_teacher"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="number", property="number", jdbcType=JdbcType.VARCHAR),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="submission_date", property="submissionDate", jdbcType=JdbcType.DATE)
    })
    List<ShardingTeacherDO> selectAll();
}
