package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginNotCoreDO;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

public interface FullTraceSameOriginNotCoreDOMapper {
    @Delete({
        "delete from full_trace_same_origin_not_core_config",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into full_trace_same_origin_not_core_config (id, gmt_create, ",
        "gmt_modified, business_region, ",
        "db_name, table_name, ",
        "not_core_columns)",
        "values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, ",
        "#{gmtModified,jdbcType=TIMESTAMP}, #{businessRegion,jdbcType=VARCHAR}, ",
        "#{dbName,jdbcType=VARCHAR}, #{tableName,jdbcType=VARCHAR}, ",
        "#{notCoreColumns,jdbcType=VARCHAR})"
    })
    int insert(FullTraceSameOriginNotCoreDO record);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, business_region, db_name, table_name, not_core_columns",
        "from full_trace_same_origin_not_core_config",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="not_core_columns", property="notCoreColumns", jdbcType=JdbcType.VARCHAR)
    })
    FullTraceSameOriginNotCoreDO selectByPrimaryKey(Long id);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, business_region, db_name, table_name, not_core_columns",
        "from full_trace_same_origin_not_core_config"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="not_core_columns", property="notCoreColumns", jdbcType=JdbcType.VARCHAR)
    })
    List<FullTraceSameOriginNotCoreDO> selectAll();

    @Update({
        "update full_trace_same_origin_not_core_config",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "business_region = #{businessRegion,jdbcType=VARCHAR},",
          "db_name = #{dbName,jdbcType=VARCHAR},",
          "table_name = #{tableName,jdbcType=VARCHAR},",
          "not_core_columns = #{notCoreColumns,jdbcType=VARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(FullTraceSameOriginNotCoreDO record);
}