package com.alipay.findataquality.dal.zdal;

import com.alipay.zdal.client.jdbc.ZdalDataSource;
import com.alipay.zdal.client.jdbc.builder.ZdalDataSourceBuilder;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionManager;

import javax.sql.DataSource;

/**
 * 演示创建数据源以及事务管理器的使用方式，使用前请先取消 @Configuration 注解的注释
 */
@Configuration
public class ZdalConfiguration {

    /**
     * 单库单表数据源
     */
    @Bean(initMethod = "init")
    public ZdalDataSource singleDataSource() {
        return ZdalDataSourceBuilder.create()
                //应用数据源,实际使用时换成应用自身的数据源
                .appDsName("finecoriskDataSource")
                //如果appName为当前应用,不需要声明该字段
                .appName("finecorisk")
                //应用数据源版本,实际使用时换成应用自身的数据源版本
                .version("EI62286018")
                //这里使用的示例数据源非dbMesh数据源
                .useDbMesh(false).build();
    }

    /**
     * 分库分表数据源
     */
//    @Bean(initMethod = "init")
//    public ZdalDataSource shardingDataSource() {
//        return ZdalDataSourceBuilder.create()
//                //应用数据源,实际使用时换成应用自身的数据源
//                .appDsName("zdaldemo_sharding_ds")
//                //如果appName为当前应用,不需要声明该字段
//                .appName("sofaappcenter")
//                //应用数据源版本,实际使用时换成应用自身的数据源版本
//                .version("sofabootdemo")
//                //这里使用的示例数据源非dbMesh数据源
//                .useDbMesh(false).build();
//    }

    /**
     * 单库单表事务管理器
     */
    @Bean
    public TransactionManager txManagerForSingle(@Qualifier(value = "singleDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 分库分表事务管理器
     */
//    @Bean
//    public TransactionManager txManagerForSharding(@Qualifier(value = "shardingDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }

}