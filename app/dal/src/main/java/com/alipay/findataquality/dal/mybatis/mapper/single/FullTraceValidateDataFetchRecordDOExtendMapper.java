package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceValidateDataFetchRecordDO;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * 扩展 MyBatis Generator 自动生成的 Mapper
 */
public interface FullTraceValidateDataFetchRecordDOExtendMapper extends FullTraceValidateDataFetchRecordDOMapper {
    
    /**
     * 根据share_code查询记录
     * 
     * @param shareCode 分享码
     * @return 包含所有字段的记录列表
     */
    @Select({
        "select",
        "id, gmt_create, gmt_modified, scene_code, operator, fetch_time, status, entrance_code, ",
        "share_code, env, ext_info, operator_id, content, mark_content, key_data, key_data_summary",
        "from full_trace_validate_data_fetch_record",
        "where share_code = #{shareCode,jdbcType=VARCHAR}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="scene_code", property="sceneCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="fetch_time", property="fetchTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="status", property="status", jdbcType=JdbcType.VARCHAR),
        @Result(column="entrance_code", property="entranceCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="share_code", property="shareCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="env", property="env", jdbcType=JdbcType.VARCHAR),
        @Result(column="ext_info", property="extInfo", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="content", property="content", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="mark_content", property="markContent", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data", property="keyData", jdbcType=JdbcType.LONGVARCHAR),
        @Result(column="key_data_summary", property="keyDataSummary", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<FullTraceValidateDataFetchRecordDO> selectByShareCode(String shareCode);
}
