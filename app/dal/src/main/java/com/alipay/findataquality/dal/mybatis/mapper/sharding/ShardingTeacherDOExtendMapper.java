package com.alipay.findataquality.dal.mybatis.mapper.sharding;

import com.alipay.findataquality.dal.mybatis.model.sharding.ShardingTeacherDO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

/**
 * 扩展 MyBatis Generator 自动生成的 Mapper
 */
public interface ShardingTeacherDOExtendMapper extends ShardingTeacherDOMapper {
    @Delete({
            "delete from sharding_teacher",
            "where number = #{number,jdbcType=VARCHAR}"
    })
    int deleteByNumber(@Param("number") String number);

    @Select({
            "select",
            "id, number, name, submission_date",
            "from sharding_teacher",
            "where number = #{number,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="number", property="number", jdbcType=JdbcType.VARCHAR),
            @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
            @Result(column="submission_date", property="submissionDate", jdbcType=JdbcType.DATE)
    })
    ShardingTeacherDO selectByNumber(@Param("number") String number);
}
