package com.alipay.findataquality.dal.mybatis.mapper.sharding;

import com.alipay.findataquality.dal.mybatis.model.sharding.ShardingStudentDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * MyBatis Generator 自动生成的 SQL
 */
public interface ShardingStudentDOMapper {
    @Delete({
        "delete from sharding_student",
        "where id = #{id,jdbcType=INTEGER}"
    })
    int deleteByPrimaryKey(Integer id);

    @Insert({
        "insert into sharding_student (id, number, ",
        "name, score, submission_date)",
        "values (#{id,jdbcType=INTEGER}, #{number,jdbcType=VARCHAR}, ",
        "#{name,jdbcType=VARCHAR}, #{score,jdbcType=VARCHAR}, #{submissionDate,jdbcType=DATE})"
    })
    int insert(ShardingStudentDO record);

    @Select({
        "select",
        "id, number, name, score, submission_date",
        "from sharding_student",
        "where id = #{id,jdbcType=INTEGER}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="number", property="number", jdbcType=JdbcType.VARCHAR),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="score", property="score", jdbcType=JdbcType.VARCHAR),
        @Result(column="submission_date", property="submissionDate", jdbcType=JdbcType.DATE)
    })
    ShardingStudentDO selectByPrimaryKey(Integer id);

    @Select({
        "select",
        "id, number, name, score, submission_date",
        "from sharding_student"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true),
        @Result(column="number", property="number", jdbcType=JdbcType.VARCHAR),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="score", property="score", jdbcType=JdbcType.VARCHAR),
        @Result(column="submission_date", property="submissionDate", jdbcType=JdbcType.DATE)
    })
    List<ShardingStudentDO> selectAll();

    @Update({
        "update sharding_student",
        "set number = #{number,jdbcType=VARCHAR},",
          "name = #{name,jdbcType=VARCHAR},",
          "score = #{score,jdbcType=VARCHAR},",
          "submission_date = #{submissionDate,jdbcType=DATE}",
        "where id = #{id,jdbcType=INTEGER}"
    })
    int updateByPrimaryKey(ShardingStudentDO record);
}