package com.alipay.findataquality.dal.mybatis;

import com.alipay.findataquality.dal.mybatis.mapper.sharding.ShardingStudentDOMapper;
import com.alipay.findataquality.dal.mybatis.mapper.sharding.ShardingTeacherDOExtendMapper;
import com.alipay.findataquality.dal.mybatis.mapper.single.StudentDOMapper;
import com.alipay.findataquality.dal.mybatis.model.sharding.ShardingStudentDO;
import com.alipay.findataquality.dal.mybatis.model.sharding.ShardingTeacherDO;
import com.alipay.findataquality.dal.mybatis.model.single.StudentDO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Mybatis 使用教程
 */
@Component
public class MybatisSample {
    private static final Logger log = LoggerFactory.getLogger(MybatisSample.class);
    private final String STUDENT_NUMBER = UUID.randomUUID().toString();
    private final String TEACHER_NUMBER = String.valueOf(new Random().nextInt(10000));

    @Autowired(required = false)
    private StudentDOMapper singleStudentMapper;

//    @Autowired(required = false)
//    private ShardingStudentDOMapper shardingStudentMapper;
//
//    @Autowired(required = false)
//    private ShardingTeacherDOExtendMapper shardingTeacherDOExtendMapper;

    @Transactional(transactionManager = "txManagerForSingle")
    public StudentDO singleDataSourceTransactionTest() throws Exception {
        log.info("------------- 单库单表数据源事务内操作演示 ---------------");

        StudentDO studentDO = new StudentDO();
        studentDO.setNumber(STUDENT_NUMBER);
        studentDO.setName("Rose");
        studentDO.setScore("99");
        singleStudentMapper.insert(studentDO);

        log.info("单库单表数据源事务内插入数据:{}", studentDO);

        int id = studentDO.getId();
        StudentDO currentThreadResult = singleStudentMapper.selectByPrimaryKey(id);

        Assert.notNull(currentThreadResult, "当前线程查询结果为空");

        log.info("单库单表数据源事务内当前线程查询:{}", currentThreadResult);

        AtomicReference<StudentDO> otherThreadReference = new AtomicReference<>();

        CountDownLatch latch = new CountDownLatch(1);

        new Thread(() -> {
            StudentDO otherThreadResult = singleStudentMapper.selectByPrimaryKey(id);
            latch.countDown();
            otherThreadReference.set(otherThreadResult);
        }).start();

        latch.await();
        Assert.isNull(otherThreadReference.get(), "并发线程查询结果不为空");

        log.info("单库单表数据源事务内并发线程查询为空");

        singleStudentMapper.deleteByPrimaryKey(id);

        log.info("单库单表数据源事务内清理测试数据");

        log.info("------------- 单库单表数据源事务内操作演示 ---------------");

        return currentThreadResult;
    }

    public StudentDO singleDataSourceTest() throws Exception {
        org.springframework.util.Assert.notNull(singleStudentMapper, "请确保 MybatisConfiguration 类中的 @Configuration 注释已打开");

        log.info("------------- 单库单表数据源不开事务操作演示 ---------------");

        StudentDO studentDO = new StudentDO();
        studentDO.setNumber(STUDENT_NUMBER);
        studentDO.setName("Rose");
        studentDO.setScore("99");
        singleStudentMapper.insert(studentDO);

        int id = studentDO.getId();
        log.info("单库单表数据源插入数据:{}", studentDO);

        StudentDO currentThreadResult = singleStudentMapper.selectByPrimaryKey(id);

        Assert.notNull(currentThreadResult, "当前线程查询结果为空");

        log.info("单库单表数据源当前线程查询:{}", currentThreadResult);

        AtomicReference<StudentDO> otherThreadReference = new AtomicReference<>();

        CountDownLatch latch = new CountDownLatch(1);

        new Thread(() -> {
            StudentDO otherThreadResult = singleStudentMapper.selectByPrimaryKey(id);
            latch.countDown();
            otherThreadReference.set(otherThreadResult);
        }).start();

        latch.await();

        Assert.notNull(otherThreadReference.get(), "并发线程查询结果为空");

        log.info("单库单表数据源并发线程查询:{}", currentThreadResult);

        singleStudentMapper.deleteByPrimaryKey(id);

        log.info("单库单表数据源不开事务清理测试数据");

        log.info("------------- 单库单表数据源不开事务操作演示 ---------------");

        return currentThreadResult;
    }


//    @Transactional(transactionManager = "txManagerForSharding")
//    public ShardingStudentDO shardingStudentTransactionTest() throws Exception {
//
//        log.info("------------- 分库分表数据源事务内操作演示 ---------------");
//
//        Integer id = Math.abs(new Random().nextInt());
//
//        shardingStudentMapper.deleteByPrimaryKey(id);
//
//        ShardingStudentDO studentDO = new ShardingStudentDO();
//        studentDO.setId(id);
//        studentDO.setNumber(STUDENT_NUMBER);
//        studentDO.setName("Rose");
//        studentDO.setScore("99");
//        shardingStudentMapper.insert(studentDO);
//
//        log.info("分库分表数据源事务内插入数据:{}", studentDO);
//
//        ShardingStudentDO currentThreadResult = shardingStudentMapper.selectByPrimaryKey(id);
//
//        Assert.notNull(currentThreadResult, "当前线程查询结果为空");
//
//        log.info("分库分表数据源事务内当前线程查询:{}", currentThreadResult);
//
//        AtomicReference<ShardingStudentDO> otherThreadReference = new AtomicReference<>();
//
//        CountDownLatch latch = new CountDownLatch(1);
//
//        new Thread(() -> {
//            ShardingStudentDO otherThreadResult = shardingStudentMapper.selectByPrimaryKey(id);
//            latch.countDown();
//            otherThreadReference.set(otherThreadResult);
//        }).start();
//
//        latch.await();
//        Assert.isNull(otherThreadReference.get(), "并发线程查询结果不为空");
//
//        log.info("分库分表数据源事务内并发线程查询为空");
//
//        shardingStudentMapper.deleteByPrimaryKey(id);
//
//        log.info("分库分表数据源事务内清理测试数据");
//
//        log.info("------------- 分库分表数据源事务内操作演示 ---------------");
//
//        return studentDO;
//    }
//
//    public ShardingStudentDO shardingStudentTest() throws Exception {
//        org.springframework.util.Assert.notNull(shardingStudentMapper, "请确保 MybatisConfiguration 类中的 @Configuration 注释已打开");
//
//        log.info("------------- 分库分表数据源不开事务操作演示 ---------------");
//
//        Integer id = Math.abs(new Random().nextInt());
//
//        shardingStudentMapper.deleteByPrimaryKey(id);
//
//        ShardingStudentDO studentDO = new ShardingStudentDO();
//        studentDO.setId(id);
//        studentDO.setNumber(STUDENT_NUMBER);
//        studentDO.setName("Rose");
//        studentDO.setScore("99");
//        shardingStudentMapper.insert(studentDO);
//
//        log.info("分库分表数据源插入数据:{}", studentDO);
//
//        ShardingStudentDO currentThreadResult = shardingStudentMapper.selectByPrimaryKey(id);
//
//        Assert.notNull(currentThreadResult, "当前线程查询结果为空");
//
//        log.info("分库分表数据源当前线程查询:{}", currentThreadResult);
//
//        AtomicReference<ShardingStudentDO> otherThreadReference = new AtomicReference<>();
//
//        CountDownLatch latch = new CountDownLatch(1);
//
//        new Thread(() -> {
//            ShardingStudentDO otherThreadResult = shardingStudentMapper.selectByPrimaryKey(id);
//            latch.countDown();
//            otherThreadReference.set(otherThreadResult);
//        }).start();
//
//        latch.await();
//
//        Assert.notNull(otherThreadReference.get(), "并发线程查询结果为空");
//
//        log.info("分库分表数据源并发线程查询:{}", currentThreadResult);
//
//        shardingStudentMapper.deleteByPrimaryKey(id);
//
//        log.info("分库分表数据源不开事务清理测试数据");
//
//        log.info("------------- 分库分表数据源不开事务操作演示 ---------------");
//
//        return studentDO;
//    }
//
//    @Transactional(transactionManager = "txManagerForSharding")
//    public ShardingTeacherDO shardingTeacherTransactionTest() throws Exception {
//
//        log.info("------------- 分库分表数据源事务内操作演示 ---------------");
//
//        Integer id = Math.abs(new Random().nextInt());
//
//        shardingTeacherDOExtendMapper.deleteByNumber(TEACHER_NUMBER);
//
//        ShardingTeacherDO teacherDO = new ShardingTeacherDO();
//        teacherDO.setId(id);
//        teacherDO.setNumber(TEACHER_NUMBER);
//        teacherDO.setName("Rose");
//        shardingTeacherDOExtendMapper.insert(teacherDO);
//
//        log.info("分库分表数据源事务内插入数据:{}", teacherDO);
//
//        ShardingTeacherDO currentThreadResult = shardingTeacherDOExtendMapper.selectByNumber(TEACHER_NUMBER);
//
//        Assert.notNull(currentThreadResult, "当前线程查询结果为空");
//
//        log.info("分库分表数据源事务内当前线程查询:{}", currentThreadResult);
//
//        AtomicReference<ShardingTeacherDO> otherThreadReference = new AtomicReference<>();
//
//        CountDownLatch latch = new CountDownLatch(1);
//
//        new Thread(() -> {
//            ShardingTeacherDO otherThreadResult = shardingTeacherDOExtendMapper.selectByNumber(TEACHER_NUMBER);
//            latch.countDown();
//            otherThreadReference.set(otherThreadResult);
//        }).start();
//
//        latch.await();
//        Assert.isNull(otherThreadReference.get(), "并发线程查询结果不为空");
//
//        log.info("分库分表数据源事务内并发线程查询为空");
//
//        shardingTeacherDOExtendMapper.deleteByNumber(TEACHER_NUMBER);
//
//        log.info("分库分表数据源事务内清理测试数据");
//
//        log.info("------------- 分库分表数据源事务内操作演示 ---------------");
//
//        return teacherDO;
//    }
//
//    public ShardingTeacherDO shardingTeacherTest() throws Exception {
//        org.springframework.util.Assert.notNull(shardingStudentMapper, "请确保 MybatisConfiguration 类中的 @Configuration 注释已打开");
//
//        log.info("------------- 分库分表数据源不开事务操作演示 ---------------");
//
//        Integer id = Math.abs(new Random().nextInt());
//
//        shardingTeacherDOExtendMapper.deleteByNumber(TEACHER_NUMBER);
//
//        ShardingTeacherDO teacherDO = new ShardingTeacherDO();
//        teacherDO.setId(id);
//        teacherDO.setNumber(TEACHER_NUMBER);
//        teacherDO.setName("Rose");
//        shardingTeacherDOExtendMapper.insert(teacherDO);
//
//        log.info("分库分表数据源插入数据:{}", teacherDO);
//
//        ShardingTeacherDO currentThreadResult = shardingTeacherDOExtendMapper.selectByNumber(TEACHER_NUMBER);
//
//        Assert.notNull(currentThreadResult, "当前线程查询结果为空");
//
//        log.info("分库分表数据源当前线程查询:{}", currentThreadResult);
//
//        AtomicReference<ShardingTeacherDO> otherThreadReference = new AtomicReference<>();
//
//        CountDownLatch latch = new CountDownLatch(1);
//
//        new Thread(() -> {
//            ShardingTeacherDO otherThreadResult = shardingTeacherDOExtendMapper.selectByNumber(TEACHER_NUMBER);
//            latch.countDown();
//            otherThreadReference.set(otherThreadResult);
//        }).start();
//
//        latch.await();
//
//        Assert.notNull(otherThreadReference.get(), "并发线程查询结果为空");
//
//        log.info("分库分表数据源并发线程查询:{}", currentThreadResult);
//
//        shardingTeacherDOExtendMapper.deleteByNumber(TEACHER_NUMBER);
//
//        log.info("分库分表数据源不开事务清理测试数据");
//
//        log.info("------------- 分库分表数据源不开事务操作演示 ---------------");
//
//        return teacherDO;
//    }
}
