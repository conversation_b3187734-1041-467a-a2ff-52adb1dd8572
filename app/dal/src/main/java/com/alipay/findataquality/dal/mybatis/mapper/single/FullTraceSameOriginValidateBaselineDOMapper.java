package com.alipay.findataquality.dal.mybatis.mapper.single;

import com.alipay.findataquality.dal.mybatis.model.single.FullTraceSameOriginValidateBaselineDO;
import java.util.List;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.type.JdbcType;

public interface FullTraceSameOriginValidateBaselineDOMapper  {
    @Delete({
        "delete from full_trace_same_origin_validate_baseline",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int deleteByPrimaryKey(Long id);

    @Insert({
        "insert into full_trace_same_origin_validate_baseline (id, gmt_create, ",
        "gmt_modified, data_source, ",
        "business_region, db_name, ",
        "table_name, business_code, ",
        "ignore_columns, operator, ",
        "operator_id, unique_md5, ",
        "standard_check_source)",
        "values (#{id,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, ",
        "#{gmtModified,jdbcType=TIMESTAMP}, #{dataSource,jdbcType=VARCHAR}, ",
        "#{businessRegion,jdbcType=VARCHAR}, #{dbName,jdbcType=VARCHAR}, ",
        "#{tableName,jdbcType=VARCHAR}, #{businessCode,jdbcType=VARCHAR}, ",
        "#{ignoreColumns,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, ",
        "#{operatorId,jdbcType=VARCHAR}, #{uniqueMd5,jdbcType=VARCHAR}, ",
        "#{standardCheckSource,jdbcType=LONGVARCHAR})"
    })
    int insert(FullTraceSameOriginValidateBaselineDO record);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, data_source, business_region, db_name, table_name, ",
        "business_code, ignore_columns, operator, operator_id, unique_md5, standard_check_source",
        "from full_trace_same_origin_validate_baseline",
        "where id = #{id,jdbcType=BIGINT}"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="data_source", property="dataSource", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_code", property="businessCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="ignore_columns", property="ignoreColumns", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="unique_md5", property="uniqueMd5", jdbcType=JdbcType.VARCHAR),
        @Result(column="standard_check_source", property="standardCheckSource", jdbcType=JdbcType.LONGVARCHAR)
    })
    FullTraceSameOriginValidateBaselineDO selectByPrimaryKey(Long id);

    @Select({
        "select",
        "id, gmt_create, gmt_modified, data_source, business_region, db_name, table_name, ",
        "business_code, ignore_columns, operator, operator_id, unique_md5, standard_check_source",
        "from full_trace_same_origin_validate_baseline"
    })
    @Results({
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="gmt_create", property="gmtCreate", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="gmt_modified", property="gmtModified", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="data_source", property="dataSource", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_region", property="businessRegion", jdbcType=JdbcType.VARCHAR),
        @Result(column="db_name", property="dbName", jdbcType=JdbcType.VARCHAR),
        @Result(column="table_name", property="tableName", jdbcType=JdbcType.VARCHAR),
        @Result(column="business_code", property="businessCode", jdbcType=JdbcType.VARCHAR),
        @Result(column="ignore_columns", property="ignoreColumns", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator", property="operator", jdbcType=JdbcType.VARCHAR),
        @Result(column="operator_id", property="operatorId", jdbcType=JdbcType.VARCHAR),
        @Result(column="unique_md5", property="uniqueMd5", jdbcType=JdbcType.VARCHAR),
        @Result(column="standard_check_source", property="standardCheckSource", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<FullTraceSameOriginValidateBaselineDO> selectAll();

    @Update({
        "update full_trace_same_origin_validate_baseline",
        "set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},",
          "gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},",
          "data_source = #{dataSource,jdbcType=VARCHAR},",
          "business_region = #{businessRegion,jdbcType=VARCHAR},",
          "db_name = #{dbName,jdbcType=VARCHAR},",
          "table_name = #{tableName,jdbcType=VARCHAR},",
          "business_code = #{businessCode,jdbcType=VARCHAR},",
          "ignore_columns = #{ignoreColumns,jdbcType=VARCHAR},",
          "operator = #{operator,jdbcType=VARCHAR},",
          "operator_id = #{operatorId,jdbcType=VARCHAR},",
          "unique_md5 = #{uniqueMd5,jdbcType=VARCHAR},",
          "standard_check_source = #{standardCheckSource,jdbcType=LONGVARCHAR}",
        "where id = #{id,jdbcType=BIGINT}"
    })
    int updateByPrimaryKey(FullTraceSameOriginValidateBaselineDO record);
}