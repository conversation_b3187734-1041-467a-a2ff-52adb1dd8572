# 使用说明

## 快速开始
应用的启动类`com.alipay.findataquality.FindataqualityApplication`位于`bootstrap`模块的`src/main/java`目录下，本地开发时直接运行该类的`main`方法即可启动应用

应用的集成测试基类`com.alipay.findataquality.AbstractTestBase`位于`bootstrap`或`test`模块的`src/test/java`目录下，继承该类的单元测试类可以集成SOFABoot应用启动

应用采用 [半关闭模块化](https://yuque.antfin-inc.com/middleware/sofaboot/modular#%E5%8D%8A%E5%85%B3%E9%97%AD%E6%A8%A1%E5%9D%97%E5%8C%96) 模式，应用内各模块之间没有上下文隔离，不需要手动添加 MANIFEST.MF 文件

## SOFABoot 用户文档
你可以通过下述文档了解到 SOFABoot 的使用细节
+ SOFABoot 应用开发: [主站版SOFABoot应用开发概述](https://yuque.antfin-inc.com/middleware/sofaboot/db7fgl)
+ SOFABoot 版本: [主站版SOFABoot发布报告](https://yuque.antfin-inc.com/middleware/sofaboot/releasenote)
+ SOFABoot 技术支持: [主站版SOFABoot常见问题](https://yuque.antfin-inc.com/middleware/sofaboot/faq)

## 组件使用示例
下面是各个组件的使用示例代码,您可以找到它们,使用这些示例代码前请阅读类上的注释说明
* SOFA RPC 示例代码:
    + `RpcSampleController`: SOFA RPC 服务调用演示接口
* DRM 示例代码:
    + `DrmSampleController`: DRM 配置推送演示接口
    + `DrmSampleConfig`: DRM 配置注册示例代码
* ZDAL 示例代码:
    + `ZdalSampleController`: Zdal + JDBC 使用演示接口
* MyBatis Framework 示例代码:
    + `MyBatisConfiguration`: Mybatis 生成Mapper配置示例代码
    + `MybatisSampleController`: Mybatis 使用演示接口

## 组件用户文档
下面是各个组件件的用户文档,您可以查看详细的使用说明、工作原理:

* [SOFA RPC 用户文档](https://yuque.antfin-inc.com/middleware/sofa-rpc)
* [DRM 用户文档](https://yuque.antfin-inc.com/middleware/drm)
* [ZDAL 用户文档](https://yuque.antfin-inc.com/middleware/zdal)
* [Tracer 用户文档](https://yuque.antfin-inc.com/middleware/tracer)
* [MyBatis Framework](https://mybatis.org/spring-boot-starter/mybatis-spring-boot-autoconfigure/)

## 组件门户网站
下面是各个组件的门户网站,您可以进行问题咨询/意见反馈:

* [SOFA RPC 服务治理平台](https://sofa.alipay.com/app/GLOBAL_APP/serviceGovernance/overview?tenant=MAIN_SITE)
* [DRM 控制台](https://sofa.alipay.com/app/GLOBAL_APP/drm/overview?tenant=MAIN_SITE)
* [ZDAL 控制台](https://sofa.alipay.com/app/GLOBAL_APP/dds/overview?tenant=MAIN_SITE&appName=GLOBAL_APP)
* [MyBatis Quick Start](https://github.com/mybatis/spring-boot-starter/wiki/Quick-Start)

